import json

# Sample data for 26 users
users_data = [
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {"name": "<PERSON>", "email": "<EMAIL>", "password": "Password123!"},
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {"name": "<PERSON>", "email": "<EMAIL>", "password": "Password123!"},
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Kevin King",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Laura <PERSON>",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Mark Martin",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Nina Nelson",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Owen Olson",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Paige Parker",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Quentin Quinn",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Rachel Roberts",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {"name": "Sam Smith", "email": "<EMAIL>", "password": "Password123!"},
    {
        "name": "Tina Turner",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Ulysses Underwood",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Vivian Vance",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Walter White",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Xander Xavier",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Yolanda Young",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
    {
        "name": "Zachary Zane",
        "email": "<EMAIL>",
        "password": "Password123!",
    },
]

# Save the data to a JSON file
file_path = "/tmp/data/users_data.json"
with open(file_path, "w") as json_file:
    json.dump(users_data, json_file, indent=4)

file_path
