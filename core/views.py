import base64
import re
from io import BytesIO

from django.shortcuts import render

from core.models import TestCamera


# Create your views here.


def homepage(request):
    context = {}
    return render(request, "pages/home.html", context)


def about(request):
    return render(request, "pages/about.html")


def products(request):
    # context = {}
    return render(request, "pages/products.html")


def recommendation(request):
    # context = {}
    return render(request, "users/recommendation.html")


def camera(request):
    if request.POST:
        if request.POST.get("captured_image"):
            captured_image_url = request.POST.get("captured_image")
            captured_image_url = re.sub(
                "^data:image/.+;base64,", "", captured_image_url
            )

            byte_data = base64.b64decode(captured_image_url)
            image_data = BytesIO(byte_data)

            new_image = TestCamera()
            new_image.image.save("image.jpeg", image_data)
    return render(request, "pages/camera.html")


def get_ip(request):
    from django.http import HttpResponse

    return HttpResponse(request.META["REMOTE_ADDR"])
