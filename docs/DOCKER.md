# Using Docker

## Installing

Installing is unique to each OS, https://www.docker.com/products/docker-desktop/ is the best place to start.


## Logging into container registry

We don't want people just being able to run a copy of our site with their own details, so our repository and container
registry are marked as private and require authentication. We use GitHub for both of these facilities. To login to GitHub via Docker.

You will need to create a Personal Access Token here: https://github.com/settings/tokens
Generate a new Classic token. It will need to have the permission "read:packages".

Go to the CLI and type in `docker login ghcr.io`, the username will be your github login and the password will be whatever GitHub gives you
back... It usually starts with `ghp_*`

## Build or Pull?

If you have made changes locally that have not been pushed into the main branch? If yes you need to build, you can achieve this by two ways:

```bash
docker compose up -d --build
```

or just running

```bash
docker compose build
```


Otherwise, you can just pull the latest image by running:

```bash
docker compose pull && \
docker compose up -d
```


## Logs

You can get to the logs by running:

```bash
docker compose logs -f
```

remove the `-f` if you dont want to be caught up to live view once its vomited all its logs

## Running Migrations

```bash
docker exec -it cosm_web python manage.py migrate
```

## Populating default questions

```bash
docker exec -it cosm_web python manage.py populate_default_questions
```

## Accessing the DB directly

```bash
docker inspect -f '{{range.NetworkSettings.Networks}}{{.IPAddress}}{{end}}' cosm_db
```

DB_HOST=that.ip.address


## Alright I just want to start fresh

```bash
docker compose down
docker volume rm software-development_data_volume
```

## DEV MODE

```bash
docker compose watch
```
