from ninja import Router
from typing import List
from django.shortcuts import get_object_or_404

from .models import Questionaire, Question
from .schemas import (
    AnswerSchema,
    ReplySchema,
    QuestionaireSchema,
    QuestionSchema,
    NotFoundSchema,
    QuestionCategorySchema,
)

from .models import Answer, Reply

router = Router()


@router.get("/questionaires", response=List[QuestionaireSchema])
def list_questionaires(request):
    return Questionaire.objects.all()


@router.post("/questionaires", response=QuestionaireSchema)
def create_questionaire(request, payload: QuestionaireSchema):
    questionaire = Questionaire.objects.create(**payload.dict())
    return questionaire


@router.get("/questionaires/{questionaire_id}", response=QuestionaireSchema)
def get_questionaire(request, questionaire_id: int):
    questionaire = get_object_or_404(Questionaire, id=questionaire_id)
    return questionaire


@router.put("/questionaires/{questionaire_id}", response=QuestionaireSchema)
def update_questionaire(request, questionaire_id: int, payload: QuestionaireSchema):
    questionaire = get_object_or_404(Questionaire, id=questionaire_id)
    for attr, value in payload.dict().items():
        setattr(questionaire, attr, value)
    questionaire.save()
    return questionaire


@router.delete("/questionaires/{questionaire_id}", response={204: None})
def delete_questionaire(request, questionaire_id: int):
    questionaire = get_object_or_404(Questionaire, id=questionaire_id)
    questionaire.delete()
    return 204


@router.get("/questions", response=List[QuestionSchema])
def list_questions(request):
    """List all questions sorted by their position.

    Args:
        request: The HTTP request object.

    Returns:
        List[QuestionSchema]: A list of question schemas.
    """
    return list(sorted(Question.objects.all(), key=lambda x: x.position))


@router.get(
    "/questions/{question_id}", response={200: QuestionSchema, 404: NotFoundSchema}
)
def get_question(request, question_id: int):
    """Retrieve a question by its position ID.

    Args:
        request: The HTTP request object.
        question_id (int): The position ID of the question.

    Returns:
        tuple: A tuple containing the status code and either the question schema or a not found message.
    """
    try:
        return 200, Question.objects.get(position=question_id)
    except Question.DoesNotExist:
        return 404, {"message": "That 'Question' does not exist."}


@router.get(
    "/questions/category/{question_id}",
    response={200: QuestionCategorySchema, 404: NotFoundSchema},
)
def get_category_by_question_id(request, question_id: int):
    """Retrieve the category of a question by its position ID.

    Args:
        request: The HTTP request object.
        question_id (int): The position ID of the question.

    Returns:
        tuple: A tuple containing the status code and either the question category schema or a not found message.
    """
    try:
        category = Question.objects.get(position=question_id)
        return 200, category
    except Question.DoesNotExist:
        return 404, {"message": "No such Question hence no Category."}


@router.get("/questions/category/", response=List[QuestionSchema])
def get_category_by_name(request, category_name: str):
    """Retrieve questions by category name.

    Args:
        request: The HTTP request object.
        category_name (str): The name of the category.

    Returns:
        tuple: A tuple containing the status code and either a list of question schemas or a not found message.
    """
    try:
        category_ques = Question.objects.filter(category=category_name.capitalize())
        return 200, category_ques
    except Question.DoesNotExist:
        return 404, {"message": "No such Question hence no Category."}


@router.get("/categories/", response=List[QuestionSchema])
def get_questions_by_category(request, category: str):
    """Retrieve questions by category.

    Args:
        request: The HTTP request object.
        category (str): The category of the questions.

    Returns:
        List[QuestionSchema]: A list of question schemas.
    """
    questions = Question.objects.filter(category=category)
    return questions


@router.get("/categories/all", response=List[QuestionCategorySchema])
def get_categories(request):
    """Retrieve all distinct categories.

    Args:
        request: The HTTP request object.

    Returns:
        List[QuestionCategorySchema]: A list of distinct question categories.
    """
    categories = Question.objects.values_list("category", flat=True).distinct()
    return [{"category": category} for category in categories]


@router.post("/questions", response=QuestionSchema)
def create_question(request, payload: QuestionSchema):
    """Create a new question.

    Args:
        request: The HTTP request object.
        payload (QuestionSchema): The schema containing the question data.

    Returns:
        tuple: A tuple containing the status code and either the created question schema or an error message.
    """
    try:
        position = payload.position
        if Question.objects.get(position=position):
            return 200, payload

        question = Question.objects.create(**payload.dict())
        return 201, question
    except Exception as e:
        return 200, {"message": str(e)}


@router.put("/questions/{question_id}", response=QuestionSchema)
def update_question(request, question_id: int, payload: QuestionSchema):
    """Update an existing question.

    Args:
        request: The HTTP request object.
        question_id (int): The position ID of the question to be updated.
        payload (QuestionSchema): The schema containing the updated question data.

    Returns:
        QuestionSchema: The updated question schema.
    """
    question = get_object_or_404(Question, position=question_id)
    for attr, value in payload.dict().items():
        setattr(question, attr, value)
    question.save()
    return question


@router.delete("/questions/{question_id}", response=dict)
def delete_question(request, question_id: int):
    """Delete a question by its position ID.

    Args:
        request: The HTTP request object.
        question_id (int): The position ID of the question to be deleted.

    Returns:
        dict: A dictionary indicating the success of the operation.
    """
    question = get_object_or_404(Question, position=question_id)
    question.delete()
    return {"success": True}


@router.get("/answers", response=List[AnswerSchema])
def list_answers(request):
    return Answer.objects.all()


@router.post("/answers", response=AnswerSchema)
def create_answer(request, payload: AnswerSchema):
    answer = Answer.objects.create(**payload.dict())
    return answer


@router.get("/answers/{answer_id}", response=AnswerSchema)
def get_answer(request, answer_id: int):
    answer = get_object_or_404(Answer, id=answer_id)
    return answer


@router.get("/answers/questions/{question_id}", response=List[AnswerSchema])
def get_answers_for_question(request, question_id: int):
    try:
        answers = list(Answer.objects.filter(question=question_id))

        return answers
    except Answer.DoesNotExist:
        return 404, {"message": f"That 'Answers' not found for {question_id}."}


@router.put("/answers/{answer_id}", response=AnswerSchema)
def update_answer(request, answer_id: int, payload: AnswerSchema):
    answer = get_object_or_404(Answer, id=answer_id)
    for attr, value in payload.dict().items():
        setattr(answer, attr, value)
    answer.save()
    return answer


@router.delete("/answers/{answer_id}", response={204: None})
def delete_answer(request, answer_id: int):
    answer = get_object_or_404(Answer, id=answer_id)
    answer.delete()
    return 204


@router.get("/replies", response=List[ReplySchema])
def list_replies(request):
    return Reply.objects.all()


@router.get("/replies/{int:user_id}", response=List[ReplySchema])
def list_replies_by_user_id(request, user_id: int):
    replies = Reply.objects.filter(user_id=user_id) or []
    return replies


@router.get("/replies/{int:user_id}/{int:question_id}", response=ReplySchema)
def list_replies_by_user_id_for_question(request, user_id: int, question_id: int):
    replies = Reply.objects.filter(user_id=user_id, question=question_id) or None
    return replies[0]


@router.post("/replies", response=ReplySchema)
def create_reply(request, payload: ReplySchema):
    reply = Reply.objects.create(**payload.dict())
    return reply


@router.get("/replies/{reply_id}", response=ReplySchema)
def get_reply(request, reply_id: int):
    reply = get_object_or_404(Reply, id=reply_id)
    return reply


@router.put("/replies/{reply_id}", response=ReplySchema)
def update_reply(request, reply_id: int, payload: ReplySchema):
    reply = get_object_or_404(Reply, id=reply_id)
    for attr, value in payload.dict().items():
        setattr(reply, attr, value)
    reply.save()
    return reply


@router.delete("/replies/{reply_id}", response={204: None})
def delete_reply(request, reply_id: int):
    reply = get_object_or_404(Reply, id=reply_id)
    reply.delete()
    return 204
