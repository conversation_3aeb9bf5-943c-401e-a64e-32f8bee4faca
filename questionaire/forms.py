from django import forms
from django.core.exceptions import ValidationError

from .models import Answer


class SingleChoiceForm(forms.Form):
    def __init__(self, *args, **kwargs):
        question = kwargs.pop("question", None)
        answers = Answer.objects.filter(question=question)
        choices = answers.values_list("title", "title")
        self.advice = question.advice
        self.category = question.category
        super(SingleChoiceForm, self).__init__(*args, **kwargs)
        self.fields["reply"] = forms.ChoiceField(
            label=question.title,
            choices=choices,
            required=True,
            widget=forms.RadioSelect,
        )
        self.fields["reply"].widget.attrs.update({"class": "question_input"})


class CustomReply(forms.Form):
    def __init__(self, *args, **kwargs):
        question = kwargs.pop("question", None)
        super(CustomReply, self).__init__(*args, **kwargs)
        self.advice = question.advice
        self.category = question.category
        self.fields["reply"] = forms.CharField(
            label=question.title, required=False, initial="Hello"
        )
        self.fields["reply"].widget.attrs.update({"class": "input_field"})


class MultipleChoiceForm(forms.Form):
    def __init__(self, *args, **kwargs):
        question = kwargs.pop("question", None)
        answers = Answer.objects.filter(question=question)
        choices = answers.values_list("title", "title")
        self.advice = question.advice
        self.category = question.category
        super(MultipleChoiceForm, self).__init__(*args, **kwargs)

        self.fields["reply"] = forms.MultipleChoiceField(
            required=True,
            label=question.title,
            widget=forms.CheckboxSelectMultiple,
            choices=choices,
        )

    def clean(self):
        cleaned_data = super().clean()
        reply = cleaned_data.get("reply")
        if not reply:
            raise ValidationError("Please select at least one option.")

        return cleaned_data


class ImageUploadForm(forms.Form):
    def __init__(self, *args, **kwargs):
        question = kwargs.pop("question", None)
        self.advice = question.advice
        self.category = question.category
        self.label = question.title
