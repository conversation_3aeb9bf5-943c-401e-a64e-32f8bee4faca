# schemas.py
from typing import List
from ninja import Schema
from ninja import ModelSchema
from .models import Questionaire, Question, Answer, Reply


# HOW the objects are returned in RESPONSE
# class QuestionaireSchema(Schema):
#     id: int
#     title: str
#     description: str
#
#
# class QuestionTypeSchema(Schema):
#     code: str
#     label: str
#
#
# class QuestionSchema(Schema):
#     id: int
#     type: str
#     category: Optional[str]
#     advice: Optional[str]
#     title: str
#     scorable: bool
#     max_score: int
#     position: int
#     image: Optional[str]
#     questionaire_id: int
#
#
# class QuestionCategorySchema(Schema):
#     category: str
#
#
# class QuestionsListSchema(Schema):
#     questions: List[QuestionSchema]
#
#
# class AnswerSchema(Schema):
#     id: int
#     title: str
#     score: int
#     value: Optional[str]
#     question_id: int
#
#
# class ReplySchema(Schema):
#     id: int
#     text: Optional[str]
#     question_id: int
#     answer_id: Optional[int]
#     user_id: int


class QuestionaireSchema(ModelSchema):
    class Config:
        model = Questionaire
        model_fields = "__all__"


class QuestionSchema(ModelSchema):
    class Config:
        model = Question
        model_fields = "__all__"


class QuestionCategorySchema(Schema):
    category: str


class QuestionsListSchema(Schema):
    questions: List[QuestionSchema]


class AnswerSchema(ModelSchema):
    class Config:
        model = Answer
        model_fields = "__all__"


class ReplySchema(ModelSchema):
    class Config:
        model = Reply
        model_fields = "__all__"


class NotFoundSchema(Schema):
    message: str
