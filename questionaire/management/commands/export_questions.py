import json
import os
from pathlib import Path

from django.core.management.base import BaseCommand
from django.core.serializers import serialize

# from questions.models import Question
from questionaire.models import Question


class Command(BaseCommand):
    help = "Export all questions to a JSON file"

    def handle(self, *args, **kwargs):
        # Retrieve all questions
        questions = Question.objects.all()

        # Serialize the questions to JSON
        questions_json = serialize("json", questions)

        # Define the path where you want to save the JSON file
        BASE_DIR = Path(__file__).resolve().parent.parent.parent.parent
        file_path = os.path.join(BASE_DIR / "questionaire/data/questions.json")

        # Save the JSON data to a file
        try:
            with open(file_path, "w") as json_file:
                json.dump(json.loads(questions_json), json_file, indent=4)

            # Print success message to the console
            self.stdout.write(
                self.style.SUCCESS(f"Successfully exported questions to {file_path}")
            )
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Error exporting questions: {e}"))
