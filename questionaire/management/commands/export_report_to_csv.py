# your_app/management/commands/export_to_csv.py
import csv
from django.core.management.base import BaseCommand

from questionaire.models import Reply


class Command(BaseCommand):
    help = "Export MyModel data to CSV"

    def handle(self, *args, **kwargs):
        # Query data
        data = Reply.objects.all()

        # Specify the file path
        file_path = "user_responses.csv"

        # Write to CSV
        with open(file_path, mode="w", newline="") as file:
            writer = csv.writer(file)
            # Write the header
            writer.writerow(["Field1", "Field2", "Field3"])
            # Write data rows
            for item in data:
                writer.writerow([item.field1, item.field2, item.field3])

        self.stdout.write(
            self.style.SUCCESS(f"Data exported successfully to {file_path}")
        )
