from django.core.management.base import BaseCommand

from questionaire.models import Answer, Question, Questionaire, QuestionType, Reply


class Command(BaseCommand):
    """
    populate default question from tilda
    """

    def handle(self, *args, **options):
        Reply.objects.all().delete()
        Answer.objects.all().delete()
        Question.objects.all().delete()
        Questionaire.objects.all().delete()
        questionare = Questionaire.objects.create(
            title="Diagnostic Questionnaire",
            description="The following questions will help "
            "us to get a picture of you!",
        )

        for question in get_questions():
            if not Question.objects.filter(
                position=question.get("position"),
                questionaire=questionare,
            ).exists():
                created_question = Question.objects.create(
                    title=question.get("title"),
                    position=question.get("position"),
                    questionaire=questionare,
                    type=question.get("type"),
                    category=question.get("category"),
                    advice=question.get("advice"),
                )
                if created_question.type in [
                    QuestionType.MULTI_CHOICE,
                    QuestionType.SINGLE_CHOICE,
                ]:
                    # populate answer
                    question_answers = get_question_answers(created_question.position)
                    for tup in question_answers:
                        Answer.objects.create(
                            title=tup[0],
                            question=created_question,
                            score=tup[1],
                            value=tup[2],
                        )

        self.stdout.write("Questionare created !")


def get_questions():
    return [
        {
            "title": "What your main hair goal?",
            "position": 1,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "About You",
            "advice": None,
        },
        {
            "title": "Do you need help with any of the following issues?",
            "position": 2,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "About You",
            "advice": None,
        },
        {
            "title": "How would you describe your hair overall?",
            "position": 3,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "About You",
            "advice": None,
        },
        {
            "title": "How long is your hair today?",
            "position": 4,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "About You",
            "advice": None,
        },
        {
            "title": "Does your hair take a long time to get dry naturally Over 4 hours?",
            "position": 5,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "About You",
            "advice": None,
        },
        {
            "title": "What's your natural hair type when you air-dry?",
            "position": 6,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "About You",
            "advice": None,
            "image": ["static/images/question5hairtypedry.png"],
        },
        {
            "title": "Do you currently have split ends?",
            "position": 7,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "About You",
            "advice": None,
        },
        {
            "title": "To help us understand your hair Density - Please part your hair, How much scalp can you see",
            "position": 8,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "About You",
            "advice": None,
            "image": ["static/images/question7hairdensity.png"],
        },
        {
            "title": "Hair Texture - How thick is an individual strand of your hair? (closest to scalp)",
            "position": 9,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "About You",
            "advice": None,
            "image": ["static/images/question8hairtexturethickness.png"],
        },
        {
            "title": "Please share any relevant information about your hair you think may be useful",
            "position": 10,
            "type": QuestionType.CUSTOM_REPLY,
            "category": "About You",
            "advice": "None",
        },
        {
            "title": "Upload Your Back Hair Image Here!",
            "position": 11,
            "type": QuestionType.IMAGE_UPLOAD,
            "category": "Hair Cam",
            "advice": "Your back hair image should be clear & high quality - See how it works page for more info",
        },
        {
            "title": "Upload Your Front Hair Image Here!",
            "position": 12,
            "type": QuestionType.IMAGE_UPLOAD,
            "category": "Hair Cam",
            "advice": "Your front hair image should be clear & high quality - See how it works page for more info",
        },
        {
            "title": "Upload Your Side Hair Image Here!",
            "position": 13,
            "type": QuestionType.IMAGE_UPLOAD,
            "category": "Hair Cam",
            "advice": "Your side hair image should be clear & high quality - See how it works page for more info",
        },
        {
            "title": "Do you currently have any of the following health conditions?",
            "position": 14,
            "type": QuestionType.MULTI_CHOICE,
            "category": "Medical Review",
            "advice": None,
        },
        {
            "title": "Have you had a raised temperature fever due to illness in the last 3 months?",
            "position": 15,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Medical Review",
            "advice": None,
        },
        {
            "title": "Do you have an allergy to the following?",
            "position": 16,
            "type": QuestionType.MULTI_CHOICE,
            "category": "Medical Review",
            "advice": None,
        },
        {
            "title": "Have you stopped or started any prescription medications in the last 3 months?",
            "position": 17,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Medical Review",
            "advice": None,
        },
        {
            "title": "Do you have an iron deficiency or any other form of dietary deficiency?",
            "position": 18,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Diet & Nutrition",
            "advice": None,
        },
        {
            "title": "If YES please share which dietary deficiency you have?",
            "position": 19,
            "type": QuestionType.CUSTOM_REPLY,
            "category": "Diet & Nutrition",
            "advice": None,
        },
        {
            "title": "Which of these describes your diet?",
            "position": 20,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Diet & Nutrition",
            "advice": None,
        },
        {
            "title": "Do you take any mineral or vitamin supplements within a normal month?",
            "position": 21,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Diet & Nutrition",
            "advice": None,
        },
        {
            "title": "How often do you skip meals?",
            "position": 22,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Diet & Nutrition",
            "advice": None,
        },
        {
            "title": "Do you usually go for more than 5 hours without eating during a normal day?",
            "position": 23,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Diet & Nutrition",
            "advice": None,
        },
        {
            "title": "How much water do you drink a day?",
            "position": 24,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Diet & Nutrition",
            "advice": None,
        },
        {
            "title": "What is your general health?",
            "position": 25,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Lifestyle",
            "advice": None,
        },
        {
            "title": "What is your current fitness level?",
            "position": 26,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Lifestyle",
            "advice": None,
        },
        {
            "title": "In a typical month how many times do you exercise work out?",
            "position": 27,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Lifestyle",
            "advice": None,
        },
        {
            "title": "In an average month where do you workout, select all that apply?",
            "position": 28,
            "type": QuestionType.MULTI_CHOICE,
            "category": "Lifestyle",
            "advice": None,
        },
        {
            "title": "How often do you experience stress on a weekly basis?",
            "position": 29,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Lifestyle",
            "advice": None,
        },
        {
            "title": "How does your hair feel this week?",
            "position": 30,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "How often do you wash your hair?",
            "position": 31,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "When you wash your hair have you noticed any of the following?",
            "position": 32,
            "type": QuestionType.MULTI_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "How often do you use hair care products Not including shampoo?",
            "position": 33,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "Have you received any of these treatments in the last 6 months or will you soon?",
            "position": 34,
            "type": QuestionType.MULTI_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "Have you received any hair enhancers within the last 9 months or will you soon?",
            "position": 35,
            "type": QuestionType.MULTI_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "Does your hair retain noticeable odors from food or smoke often?",
            "position": 36,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "Which option best describes how your hair behaves generally?",
            "position": 37,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "What do you use to style your hair select all that apply?",
            "position": 38,
            "type": QuestionType.MULTI_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "How often do you apply heat tools to your hair Blow dry, straighten, curl, flat, iron etc?",
            "position": 39,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "Which part of your hair routine would you like to improve most?",
            "position": 40,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "How does your scalp usually feel most often?",
            "position": 41,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "Do you have a flaky scalp?",
            "position": 42,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "After washing your scalp how long does it usually take to get oily again?",
            "position": 43,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "After washing your scalp how long does it usually take to get dry after washing?",
            "position": 44,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "Any specific ingredient preferences?",
            "position": 45,
            "type": QuestionType.SINGLE_CHOICE,
            "category": "Hair Care",
            "advice": None,
        },
        {
            "title": "Please share why you have chosen your ingredient preference if selected",
            "position": 46,
            "type": QuestionType.CUSTOM_REPLY,
            "category": "Hair Care",
            "advice": None,
        },
    ]


def get_question_answers(question_position):
    # questions 10,11,12,13, 19, 46 are images upload questions and custom_reply questions.
    answers = {
        1: [
            ("Growth", 0, None),
            ("Moisture retention", 0, None),
            ("Ultra clean hair & scalp", 0, None),
            ("Healthy Hair", 0, None),
        ],
        2: [
            ("Scalp dryness", 2, None),
            ("Split ends / hair breakage", 2, None),
            ("Thinning hair", 2, None),
            ("Not me", 0, None),
        ],
        3: [
            ("Generally Dry", 3, None),
            ("Oily roots + dry ends", 2, None),
            ("Flaky Scalp + Dry Ends", 3, None),
            ("Fairly balanced / No outstanding issues", 0, None),
        ],
        4: [
            ("Short - close crop , fade", 0, None),
            ("Chin length", 0, None),
            ("Shoulder length", 0, None),
            ("Past your shoulders", 0, None),
        ],
        5: [("Yes", 0, None), ("No", 0, None)],
        6: [
            ("Type 3 Curly", 0, "Type 3 Curly"),
            ("Type 4a", 0, "Type 4a"),
            ("Type 4b", 0, "Type 4b"),
            ("Type 4c", 0, "Type 4c"),
        ],
        7: [("Yes", 1, None), ("No", 0, None)],
        8: [
            ("Barely any scalp", 0, "Low"),
            ("A little bit of scalp", 0, "Medium"),
            ("Fair amount of scalp", 0, "High"),
        ],
        9: [
            ("Thin or fine", 0, "Thin or Fine"),
            ("Medium", 0, "Medium"),
            ("Thick", 0, "Thick"),
        ],
        14: [
            ("Anaemia", 0, None),
            ("Depression", 0, None),
            ("Diabetes", 0, None),
            ("Hormone Imbalance", 0, None),
            ("Liver Problems", 0, None),
            ("Polycistic Ovarian Syndrome", 0, None),
            ("Thyroid Problem", 0, None),
            ("No not me", 0, None),
        ],
        15: [("Yes", 0, None), ("No", 0, None)],
        16: [
            ("Lactose/dairy", 0, None),
            ("Caffeine", 0, None),
            ("Nuts", 0, None),
            ("No not me", 0, None),
        ],
        17: [("Yes", 0, None), ("No", 0, None)],
        18: [("Yes", 0, None), ("No", 0, None)],
        20: [
            ("No restrictions", 0, None),
            ("Balanced or health-conscious", 0, None),
            ("Vegetarian/vegan", 0, None),
        ],
        21: [("Yes", 0, None), ("No", 0, None)],
        22: [
            ("Every day", 0, None),
            ("Less than 3 times per week", 0, None),
            ("Currently intermittent fasting", 0, None),
            ("I do not skip meals", 0, None),
        ],
        23: [
            ("Yes", 0, None),
            ("No", 0, None),
            ("Currently intermittent fasting", 0, None),
        ],
        24: [
            ("Quarter Litre - 1 average size glass of water", 2, None),
            ("Half a Litre", 1, None),
            ("1 Litre or more", 0, None),
            ("I don't drink water daily", 3, None),
        ],
        25: [("Good", 0, None), ("Fair", 0, None), ("Poor", 0, None)],
        26: [("Good", 0, None), ("Fair", 0, None), ("Poor", 0, None)],
        27: [
            ("1 - 2", 0, None),
            ("3 - 5", 0, None),
            ("6+", 0, None),
            ("I dont regularly exercise", 0, None),
        ],
        28: [
            ("Indoors/gym", 0, None),
            ("Outdoors/Boot camps", 0, None),
            ("Chlorine swimming pool", 0, None),
            ("I dont exercise often", 0, None),
        ],
        29: [
            ("Every day", 0, None),
            ("Multiple times a week", 0, None),
            ("Rarely", 0, None),
        ],
        30: [
            ("Weighed down", 0, None),
            ("Coarse and dry", 0, None),
            ("Limp", 0, None),
            ("Not responding to anything", 0, None),
            ("None of these / Normal", 0, None),
        ],
        31: [
            ("More than once a week", 0, None),
            ("Once a week", 0, None),
            ("Twice a month", 0, None),
            ("About once a month", 0, None),
        ],
        32: [
            ("Your hair colour fades easily or dulls rapidly?", 0, None),
            ("Do you have problems with your shampoo lathering?", 0, None),
            (
                "Your hair feels really dry no matter how much you wash or deep condition it?",
                0,
                None,
            ),
            (
                "Do you have chronic hair breakage problems that are not fixed by previous treatment or care?",
                0,
                None,
            ),
            ("None of the above", 0, None),
        ],
        33: [
            ("Daily", 0, None),
            ("3- 4 times a week", 0, None),
            ("Once a week or less", 0, None),
            ("Never", 0, None),
        ],
        34: [
            ("Relaxers", 1, None),
            ("Perm", 1, None),
            ("Keratin", 1, None),
            ("Bleach", 1, None),
            ("Colour / Dyed", 1, None),
            ("No not me", 0, None),
        ],
        35: [
            ("Natural extensions", 1, None),
            ("Synthetic extensions", 1, None),
            ("Weave", 1, None),
            ("No not me", 0, None),
        ],
        36: [("Yes", 0, None), ("No", 0, None)],
        37: [
            (
                "A - Repels moisture when wet,hair is also prone to build up, oils don't penetrate quickly, often very shiny, resists penetration of chemicals.",
                0,
                "Low",
            ),
            (
                "B - Its bouncy and elastic, not prone to breakage or brittle, requires very little maintenance, easily accepts applied moisture, hold styles well, accepts color well and evenly.",
                1,
                "Medium",
            ),
            (
                "C - Easily absorbs water, requires more hair care products, loses moisture easily, prone to frizz and tangling, It dries quickly,takes on colour easily , Colour treatments fade quickly.",
                2,
                "High",
            ),
        ],
        38: [
            ("Blow dryer", 0, None),
            ("Straightening iron", 0, None),
            ("Curling iron/wand", 0, None),
            ("Gel/pomade/paste", 0, None),
            ("Hairspray", 0, None),
            ("Mousse/ cremes / butters", 0, None),
            ("Oil", 0, None),
            ("No stylers for me", 0, None),
        ],
        39: [
            ("Everyday or every other day", 3, None),
            ("Once or twice a week", 2, None),
            ("Once or twice a month", 1, None),
            ("Rarely", 0, None),
            ("I dont do this", 0, None),
        ],
        40: [
            ("Washing", 0, None),
            ("Drying", 0, None),
            ("Styling", 0, None),
            ("Product Application", 0, None),
            ("I dont have a routine", 0, None),
        ],
        41: [
            ("A little bit tight", 1, None),
            ("Tight & Itchy", 2, None),
            ("Itchy & painful", 2, None),
            ("Itchy", 2, None),
            ("Sensitive", 2, None),
            ("None of the above", 0, None),
        ],
        42: [
            ("Yes Rarely", 1, None),
            ("Yes Usually", 2, None),
            ("Yes Always", 3, None),
            ("No Not Me", 0, None),
        ],
        43: [
            ("1 - 2 Days", 3, None),
            ("3 - 4 Days", 2, None),
            ("5 + Days", 1, None),
            ("My scalp gets dry not oily", 0, None),
        ],
        44: [
            ("Within hours", 3, None),
            ("1 - 2 Days", 2, None),
            ("3 - 5 Days", 1, None),
            ("My scalp gets oily not dry", 0, None),
        ],
        45: [
            ("Vegan", 0, None),
            ("Protein free", 0, None),
            ("Silicone-free", 0, None),
            ("Nut free", 0, None),
            ("No Thanks", 0, None),
        ],
    }
    return answers[question_position]
