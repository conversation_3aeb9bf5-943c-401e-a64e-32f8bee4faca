import pytest
from ninja.testing import TestClient

from .api import router
from .models import Question

client = TestClient(router)


@pytest.fixture
def mock_question(mocker):
    return mocker.patch(
        "cosmetrics_ai.models.Question"
    )  # replace 'app_name' with your app's name


def test_list_questions(mock_question):
    mock_question.objects.all.return_value = [
        Question(id=1, title="Question 1", position=1),
        Question(id=2, title="Question 2", position=2),
    ]
    response = client.get("/question")
    assert response.status_code == 200
    assert response.json() == [
        {"id": 1, "title": "Question 1", "position": 1},
        {"id": 2, "title": "Question 2", "position": 2},
    ]


def test_get_question(mock_question):
    mock_question.objects.get.return_value = Question(
        id=1, title="Question 1", position=1
    )
    response = client.get("/question/1")
    assert response.status_code == 200
    assert response.json() == {"id": 1, "title": "Question 1", "position": 1}


def test_get_question_not_found(mock_question):
    mock_question.objects.get.side_effect = Question.DoesNotExist
    response = client.get("/question/99")
    assert response.status_code == 404
    assert response.json() == {"message": "That 'Question' does not exist."}


def test_get_category_by_question_id(mock_question):
    mock_question.objects.get.return_value = Question(
        id=1, category="General", position=1
    )
    response = client.get("/question/category/1")
    assert response.status_code == 200
    assert response.json() == {"id": 1, "category": "General", "position": 1}


def test_get_category_by_question_id_not_found(mock_question):
    mock_question.objects.get.side_effect = Question.DoesNotExist
    response = client.get("/question/category/99")
    assert response.status_code == 404
    assert response.json() == {"message": "No such Question hence no Category."}


def test_get_category_by_name(mock_question):
    mock_question.objects.filter.return_value = [
        Question(id=1, category="General", title="Question 1", position=1),
    ]
    response = client.get("/question/category/", {"category_name": "General"})
    assert response.status_code == 200
    assert response.json() == [
        {"id": 1, "category": "General", "title": "Question 1", "position": 1},
    ]


def test_get_questions_by_category(mock_question):
    mock_question.objects.filter.return_value = [
        Question(id=1, category="General", title="Question 1", position=1),
    ]
    response = client.get("/categories/", {"category": "General"})
    assert response.status_code == 200
    assert response.json() == [
        {"id": 1, "category": "General", "title": "Question 1", "position": 1},
    ]


def test_get_categories(mock_question):
    mock_question.objects.values_list.return_value.distinct.return_value = [
        "General",
        "Specific",
    ]
    response = client.get("/categories/all")
    assert response.status_code == 200
    assert response.json() == [
        {"category": "General"},
        {"category": "Specific"},
    ]


@pytest.mark.skip(reason="more dev required")
def test_create_question(mock_question):
    mock_question.objects.create.return_value = Question(
        id=1, title="Question 1", position=1
    )
    response = client.post("/question", json={"title": "Question 1", "position": 1})
    assert response.status_code == 201
    assert response.json() == {"id": 1, "title": "Question 1", "position": 1}


@pytest.mark.skip(reason="more dev required")
def test_update_question(mock_question):
    mock_question.objects.get.return_value = Question(
        id=1, title="Question 1", position=1
    )
    response = client.put(
        "/question/1", json={"title": "Updated Question", "position": 1}
    )
    assert response.status_code == 200
    assert response.json() == {"id": 1, "title": "Updated Question", "position": 1}


@pytest.mark.skip(reason="more dev required")
def test_delete_question(mock_question):
    mock_question.objects.get.return_value = Question(
        id=1, title="Question 1", position=1
    )
    response = client.delete("/question/1")
    assert response.status_code == 200
    assert response.json() == {"success": True}
