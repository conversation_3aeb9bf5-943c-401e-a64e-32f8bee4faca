# Generated by Django 5.0.7 on 2024-09-01 23:33

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("questionaire", "0005_question_image"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="questionaire",
            options={
                "ordering": ["-created_at"],
                "verbose_name": "Questionaire",
                "verbose_name_plural": "Questionaires",
            },
        ),
        migrations.AddField(
            model_name="questionaire",
            name="created_at",
            field=models.DateTimeField(
                auto_now_add=True, default=django.utils.timezone.now
            ),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name="questionaire",
            name="updated_at",
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name="answer",
            name="question",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="answers",
                to="questionaire.question",
            ),
        ),
        migrations.AlterField(
            model_name="answer",
            name="score",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="answer",
            name="title",
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name="answer",
            name="value",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="question",
            name="advice",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="question",
            name="category",
            field=models.CharField(blank=True, max_length=120, null=True),
        ),
        migrations.AlterField(
            model_name="question",
            name="id",
            field=models.AutoField(primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name="question",
            name="max_score",
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AlterField(
            model_name="question",
            name="position",
            field=models.PositiveIntegerField(),
        ),
        migrations.AlterField(
            model_name="question",
            name="questionaire",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="questions",
                to="questionaire.questionaire",
            ),
        ),
        migrations.AlterField(
            model_name="question",
            name="title",
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name="questionaire",
            name="description",
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name="questionaire",
            name="title",
            field=models.CharField(max_length=255),
        ),
        migrations.AlterField(
            model_name="reply",
            name="answer",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="replies",
                to="questionaire.answer",
            ),
        ),
        migrations.AlterField(
            model_name="reply",
            name="question",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="replies",
                to="questionaire.question",
            ),
        ),
        migrations.AlterField(
            model_name="reply",
            name="text",
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name="reply",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="replies",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddIndex(
            model_name="question",
            index=models.Index(
                fields=["questionaire", "position"],
                name="questionair_questio_5a0a76_idx",
            ),
        ),
    ]
