from django.db import models
from django.utils.translation import gettext_lazy as _
from users.models import User


class Questionaire(models.Model):
    title = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Questionaire"
        verbose_name_plural = "Questionaires"
        ordering = ["-created_at"]

    def __str__(self):
        return self.title


class QuestionType(models.TextChoices):
    CUSTOM_REPLY = "CR", _("Custom Reply")
    SINGLE_CHOICE = "SC", _("Single Choice")
    MULTI_CHOICE = "MC", _("Multi Choice")
    IMAGE_UPLOAD = "IU", _("Image Upload")


class Question(models.Model):
    id = models.AutoField(primary_key=True)
    type = models.CharField(
        max_length=2,
        choices=QuestionType.choices,
        default=QuestionType.CUSTOM_REPLY,
    )
    category = models.Char<PERSON>ield(max_length=120, null=True, blank=True)
    advice = models.CharField(max_length=255, null=True, blank=True)
    title = models.CharField(max_length=255)
    scorable = models.BooleanField(default=False)
    max_score = models.PositiveIntegerField(default=0)
    position = models.PositiveIntegerField()
    image = models.ImageField(upload_to="question_images/", blank=True, null=True)
    questionaire = models.ForeignKey(
        Questionaire, on_delete=models.CASCADE, related_name="questions"
    )

    class Meta:
        unique_together = ("questionaire", "position")
        indexes = [
            models.Index(fields=["questionaire", "position"]),
        ]

    def __str__(self):
        return f"{self.title} - {self.position}"


# class Question(models.Model):
#     type = models.CharField(
#         max_length=2,
#         choices=QuestionType.choices,
#         default=QuestionType.CUSTOM_REPLY,
#     )
#     category = models.CharField(max_length=120, null=True, blank=True)  # Added blank=True for consistency
#     advice = models.CharField(max_length=120, null=True, blank=True)  # Added blank=True for consistency
#     title = models.CharField(max_length=120)
#     scorable = models.BooleanField(default=False)
#     max_score = models.PositiveIntegerField(default=0)  # Changed to PositiveIntegerField for better constraint
#     position = models.PositiveIntegerField()  # Changed to PositiveIntegerField for better constraint
#     image = models.ImageField(upload_to="question_images/", blank=True, null=True)
#     questionaire = models.ForeignKey(Questionaire, on_delete=models.CASCADE, related_name='questions')
#
#     class Meta:
#         unique_together = ("questionaire", "position")
#         indexes = [
#             models.Index(fields=['questionaire', 'position']),
#         ]
#
#     def __str__(self):
#         return f"{self.title} - {self.position}"


class Answer(models.Model):
    title = models.CharField(
        max_length=255
    )  # Changed to CharField for better performance
    score = models.PositiveIntegerField(
        default=0
    )  # Changed to PositiveIntegerField for better constraint
    value = models.CharField(
        max_length=255, null=True, blank=True
    )  # Added blank=True for consistency
    question = models.ForeignKey(
        Question, on_delete=models.CASCADE, related_name="answers"
    )

    def __str__(self):
        return self.title


class Reply(models.Model):
    text = models.CharField(
        max_length=255, null=True, blank=True
    )  # Changed to CharField and added blank=True
    question = models.ForeignKey(
        Question, on_delete=models.CASCADE, related_name="replies"
    )
    answer = models.ForeignKey(
        Answer, on_delete=models.CASCADE, null=True, blank=True, related_name="replies"
    )
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="replies")

    def __str__(self):
        return self.text or "No text provided"
