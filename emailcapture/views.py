from django.contrib.auth.decorators import login_required
from django.shortcuts import render

# Create your views here.
from django.http import HttpResponse
from django.core.mail import send_mail
from django.conf import settings
from .models import Email


def capture_email(request):
    if request.method == "POST":
        email_address = request.POST.get("email")
        email, created = Email.objects.get_or_create(email=email_address)
        # if new(not in database), send a email to user
        if created:
            send_mail(
                "Confirmation",
                "Welcome! Cosmetrics Ai!",
                settings.DEFAULT_FROM_EMAIL,
                [email_address],
                fail_silently=False,
            )
        return HttpResponse("Thank you for subscribing!")
    return render(request, "emailcapture/capture.html")


@login_required
def list_emails(request):
    emails = Email.objects.all()
    return render(request, "emailcapture/list_emails.html", {"emails": emails})
