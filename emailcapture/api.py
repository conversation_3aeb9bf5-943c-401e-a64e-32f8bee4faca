from django.shortcuts import render
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth.decorators import login_required
from ninja import Router, Schema
from .models import Email

# Initialize NinjaAPI
router = Router()


# Create a Schema for Email
class EmailIn(Schema):
    email: str


# Endpoint to capture email
@router.post("/capture-email")
def capture_email(request, payload: EmailIn):
    email_address = payload.email
    email, created = Email.objects.get_or_create(email=email_address)
    # If new (not in database), send an email to the user
    if created:
        send_mail(
            "Confirmation",
            "Welcome! Cosmetrics Ai!",
            settings.DEFAULT_FROM_EMAIL,
            [email_address],
            fail_silently=False,
        )
        return {"message": "Thank you for subscribing!"}
    return {"message": "This email is already subscribed!"}


# Endpoint to list emails (requires authentication)
@router.get("/emails", auth=lambda request: request.user.is_authenticated)
def list_emails(request):
    emails = Email.objects.all()
    return [{"email": email.email, "created_at": email.created_at} for email in emails]


# Regular Django views
@login_required
def list_emails_view(request):
    emails = Email.objects.all()
    return render(request, "emailcapture/list_emails.html", {"emails": emails})


# In your urls.py, you will need to include the API router
