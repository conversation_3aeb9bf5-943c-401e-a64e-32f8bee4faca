import csv
from product_page import ProductPage


def main():
    """
    Main function to extract product details from Amazon product pages and write them to a CSV file.

    This function initializes a list of product URLs, extracts details such as title, price, image URL, and description
    for each product, and writes the details to a CSV file.

    Raises:
        Exception: If any error occurs during the extraction or writing process.
    """
    # Example URLs of Amazon product pages
    urls = [
        "https://www.amazon.co.uk/dp/B08N5WRWNW",  # Add more URLs as needed
    ]

    # Define the CSV file path
    csv_file_path = "product_details.csv"

    # Open the CSV file for writing
    with open(csv_file_path, mode="w", newline="", encoding="utf-8") as csv_file:
        fieldnames = ["Title", "Price", "Image URL", "Description"]
        writer = csv.DictWriter(csv_file, fieldnames=fieldnames)

        # Write the header
        writer.writeheader()

        for url in urls:
            # Initialize the ProductPage with the URL
            product_page = ProductPage(url)

            # Extract the product details
            title = product_page.get_product_title()
            price = product_page.get_product_price()
            image_url = product_page.get_product_image_url()
            description = product_page.get_product_description()

            # Write the product details to the CSV file
            writer.writerow(
                {
                    "Title": title,
                    "Price": price,
                    "Image URL": image_url,
                    "Description": description,
                }
            )

    print(f"Product details have been written to {csv_file_path}")


if __name__ == "__main__":
    main()
