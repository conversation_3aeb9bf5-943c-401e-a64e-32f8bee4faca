import requests
from bs4 import BeautifulSoup


class BasePage:
    """
    Base class for web pages, providing basic functionalities to load and parse web content.

    Attributes:
        url (str): The URL of the web page to be loaded.
        page_content (bytes): The raw content of the loaded web page.
        soup (BeautifulSoup): The BeautifulSoup object for parsing HTML content.
    """

    def __init__(self, url):
        """
        Initializes the BasePage with a URL.

        Args:
            url (str): The URL of the web page to be loaded.
        """
        self.url = url
        self.page_content = None
        self.soup = None

    def load_page(self):
        """
        Loads the web page content from the specified URL.

        Raises:
            Exception: If the page fails to load.
        """
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(self.url, headers=headers, timeout=10)
        if response.status_code == 200:
            self.page_content = response.content
            self.soup = BeautifulSoup(self.page_content, "html.parser")
        else:
            raise Exception(f"Failed to load page: {self.url}")

    def get_soup(self):
        """
        Returns the BeautifulSoup object for the loaded web page content.

        Returns:
            BeautifulSoup: The BeautifulSoup object for parsing HTML content.

        Raises:
            Exception: If the page content has not been loaded.
        """
        if self.soup is None:
            self.load_page()
        return self.soup
