from pprint import pprint

from faker import Faker
from loguru import logger


def get_fake_profile(count=10):
    logger.info("Generating test user profiles...")
    fake = Faker()
    user_data = []
    for _ in range(count):
        profile = fake.profile()
        data = {
            "username": profile.get("username"),
            "email": profile.get("mail"),
            "is_active": True,
        }

        if "name" in profile:
            fname, lname = profile.get("name").split(" ")[:2]
            data["first_name"] = fname
            data["last_name"] = lname

        user_data.append(data)
    # logger.info(user_data)
    return user_data


if __name__ == "__main__":
    pprint(get_fake_profile(count=13))
