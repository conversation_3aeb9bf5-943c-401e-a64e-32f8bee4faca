from datetime import datetime as dt
from datetime import timezone as tz
from time import sleep

from loguru import logger
from selenium import webdriver

from ..pages.about import AboutPage


def test_about_page():
    now = dt.now(tz=tz.utc)
    logger.info(f"Testing User Registration now: {now}")
    # domain_url = "https://cosmetricsai.com/" #PROD
    domain_url = "http://0.0.0.0/"
    driver = webdriver.Chrome()
    driver.maximize_window()
    driver.get(domain_url)

    # homepage = Homepage(driver)
    about_page = AboutPage(driver)

    driver.get(domain_url + "about")
    about_page.future_where_text()
    logger.info(f"CURRENT_URL: {driver.current_url}")

    sleep(5)
    driver.quit()
