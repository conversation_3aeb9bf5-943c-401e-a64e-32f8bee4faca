from datetime import datetime as dt
from datetime import timezone as tz
from time import sleep

from loguru import logger
from selenium import webdriver

# from src.pages.homepage import Homepage
from ..pages.login import LoginPage
from ..pages.register import RegisterUser


def test_register():
    now = dt.now(tz=tz.utc)
    logger.info(f"Testing User Registration now: {now}")
    # domain_url = "https://cosmetricsai.com/"
    domain_url = "http://0.0.0.0/"
    driver = webdriver.Chrome()
    driver.maximize_window()
    driver.get(domain_url + "user/register")

    # homepage = Homepage(driver)
    register_page = RegisterUser(driver)
    login_page = LoginPage(driver)

    register_page.enter_username()
    register_page.enter_user_email()
    register_page.enter_password()
    register_page.enter_password_confirm()
    register_page.submit_registration()

    driver.get(domain_url + "user/login")
    login_page.enter_username()
    login_page.enter_password()
    sleep(3)
    login_page.click_login_button()
    logger.info("\n\tEND OF TEST!!!!!!!!!!!!!!!!")

    sleep(5)
    driver.quit()
