from datetime import datetime as dt
from datetime import timezone as tz
from time import sleep

from loguru import logger
from selenium import webdriver

from ..pages.homepage import HomePage


def test_homepage_links():
    now = dt.now(tz=tz.utc)
    logger.info(f"Testing User Registration now: {now}")
    # domain_url = "https://cosmetricsai.com/"
    domain_url = "http://0.0.0.0/"
    driver = webdriver.Chrome()
    driver.maximize_window()
    driver.get(domain_url)

    home_page = HomePage(driver)

    home_page.take_quiz_hero()
    logger.info("Now on questionnaire page via take quiz link")
    # assert page url is root_url + "questionaire"
    driver.back()

    home_page.how_it_works_quiz()
    driver.back()

    home_page.learn_more_start_quiz()
    driver.back()

    home_page.learn_more_analyze_quiz()
    driver.back()

    home_page.take_quiz_discover()

    driver.back()

    driver.get(domain_url + "user/register")
    # assert
    driver.back()
    sleep(3)

    logger.info("Homepage Tests done!")

    sleep(5)
    driver.quit()
