from seleniumpagefactory.Pagefactory import PageFactory


class QuestionnairePage(PageFactory):
    def __init__(self, driver):
        self.driver = driver

    locators = {
        "sign_in_survey": ("ID", ""),
        "questionnaire_learn_more": ("ID", ""),
        "pictures_learn_more": ("ID", ""),
        "hair_analysis_report": ("ID", ""),
        "review_refine_leaving_a_review": ("ID", ""),
    }

    def sign_in_to_survey(self):
        self.sign_in_to_survey.click()

    def questionnaire_learn_more(self):
        self.questionnaire_learn_more.click()

    def pictures_learn_more(self):
        self.pictures_learn_more.click()

    def hair_analysis_report(self):
        self.hair_analysis_report.click()

    def review_refine_leaving_a_review(self):
        self.review_refine_leaving_a_review.click()
