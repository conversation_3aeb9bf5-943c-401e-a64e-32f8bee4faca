from seleniumpagefactory.Pagefactory import PageFactory


class LoginPage(PageFactory):
    def __init__(self, driver):
        self.driver = driver

    locators = {
        "username": ("ID", "id_username"),
        "user_password": ("ID", "id_password"),
        "submit_button": ("CLASS_NAME", "custom-btn"),
    }

    def enter_username(self):
        self.username.set_text("testusername")

    def enter_password(self):
        self.user_password.set_text("password1234?")

    def click_login_button(self):
        self.submit_button.click()
