from seleniumpagefactory.Pagefactory import PageFactory


class UserDashboard(PageFactory):
    def __init__(self, driver):
        self.driver = driver

    # profile recommendations report 'hair condition' 'hair id' questionnaire
    locators = {
        "user_profile": ("ID", "user_profile"),
        "user_recommendations": ("ID", "user_recommendations"),
        "user_report": ("ID", "user_report"),
        "user_condition_score": ("ID", "user_condition_score"),
        "user_hair_id": ("ID", "user_hair_id"),
        "user_questionnaire": ("ID", "user_questionnaire"),
    }

    def get_user_profile(self):
        self.get_user_profile.click()

    def get_user_recommendations(self):
        self.get_user_recommendations.click()

    def get_user_report(self):
        self.get_user_report()

    def get_user_condition_score(self):
        self.get_user_condition_score()

    def get_user_hair_id(self):
        self.get_user_hair_id()

    def get_user_quesitonnaire(self):
        self.get_user_quesitonnaire()
