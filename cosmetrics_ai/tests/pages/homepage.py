from loguru import logger
from seleniumpagefactory.Pagefactory import PageFactory


class HomePage(PageFactory):
    def __init__(self, driver):
        self.driver = driver

    locators = {
        "take_quiz_hero": ("ID", "welcome_quiz"),
        "take_quiz_discover": ("ID", "discover_quiz"),
        "learn_more_start": ("ID", "start_quiz"),
        "how_it_works": ("ID", "how_it_works_quiz"),
        "learn_more_analyze": ("ID", "analyze_take_quiz"),
    }

    def take_quiz_hero(self):
        logger.info("Take the quiz now!")
        self.take_quiz_hero.click()

    def take_quiz_discover(self):
        self.take_quiz_discover.click()

    def learn_more_start_quiz(self):
        self.learn_more_start.click()

    def how_it_works_quiz(self):
        self.how_it_works_quiz

    def learn_more_analyze_quiz(self):
        self.learn_more_start_quiz.click()
