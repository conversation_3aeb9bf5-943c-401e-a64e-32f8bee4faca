from seleniumpagefactory.Pagefactory import PageFactory


class RegisterUser(PageFactory):
    def __init__(self, driver):
        self.driver = driver

    locators = {
        "user_email": ("ID", "id_email"),
        "username": ("ID", "id_username"),
        "user_password": ("ID", "id_password"),
        "user_password_confirmation": ("ID", "id_password_confirmation"),
        "submit_button": ("ID", "submit_registration"),
    }

    def enter_user_email(self):
        self.user_email.set_text("<EMAIL>")

    def enter_username(self, username: str = None):
        if username is None:
            username = "testusername"
        self.username.set_text(username)

    def enter_password(self):
        self.user_password.set_text("password1234?")

    def enter_password_confirm(self):
        self.user_password_confirmation.set_text("password1234?")

    def submit_registration(self):
        self.submit_button.click()
