import debug_toolbar
from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path

import core
from .api import api


urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/", api.urls),
    path("", include("core.urls")),
    path("questionaires/", include("questionaire.urls")),
    path("users/", include("users.urls")),
    path("products/", include("products.urls")),
    path("ip/", core.views.get_ip),
]

# + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# Serve media files during development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += [
        path("__debug__/", include(debug_toolbar.urls)),
    ]
