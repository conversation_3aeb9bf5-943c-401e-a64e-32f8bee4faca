"""
Django settings for cosmetrics_ai project.

Generated by 'django-admin startproject' using Django 4.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/4.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.1/ref/settings/
"""

import os
from pathlib import Path

# Load env from decouple
from datetime import timedelta


if os.path.exists("env.py"):
    pass

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-l%4oi3+4+j_@#z7qjnuncgmzgqec6n_o+ptm4-26gf36qqemaj"

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = "DEVELOPMENT" in os.environ

ALLOWED_HOSTS = [
    "************",
    "localhost",
    "0.0.0.0",
    "127.0.0.1",
    "https://api.cosmetricsai.com",
    "https://www.cosmetricsai.com",
    "https://cosmetricsai.com",
    "http://cosmetricsai.com:8000/api",
    "cosmetricsai.com",
    "www.cosmetricsai.com",
    "http://ec2-15-188-66-74.eu-west-3.compute.amazonaws.com",
]

CSRF_TRUSTED_ORIGINS = [
    "https://************",
    "https://api.cosmetricsai.com",
    "https://cosmetricsai.com",
    "http://cosmetricsai.com:8000/api",
    "http://localhost:3000",
    "http://0.0.0.0:3000",
    "http://127.0.0.1:3000",
]

if not DEBUG:  # Assuming DEBUG is False in production
    CSRF_COOKIE_SECURE = True
    SESSION_COOKIE_SECURE = True
else:
    CSRF_COOKIE_SECURE = False
    SESSION_COOKIE_SECURE = False

if not DEBUG:
    CSRF_COOKIE_SAMESITE = "None"
    SESSION_COOKIE_SAMESITE = "None"
else:
    CSRF_COOKIE_SAMESITE = "Lax"  # or "Strict"
    SESSION_COOKIE_SAMESITE = "Lax"  # or "Strict"


# Application definition
INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    # 'django_gulp',
    "django.contrib.staticfiles",
    "django.contrib.sites",
    "whitenoise.runserver_nostatic",
    "corsheaders",
    "debug_toolbar",
    "ninja",
    "ninja_extra",
    "ninja_jwt",
    # INTERNAL APPS
    "allauth",
    "allauth.account",
    "allauth.socialaccount",
    "core",
    "questionaire",
    "users",
    "products",
    "emailcapture",
]

MIDDLEWARE = [
    "corsheaders.middleware.CorsMiddleware",
    "debug_toolbar.middleware.DebugToolbarMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "whitenoise.middleware.WhiteNoiseMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "allauth.account.middleware.AccountMiddleware",
]

# AUTH_USER_MODEL = 'users.User'

CORS_ALLOWED_ORIGINS = [
    "https://api.cosmetricsai.com",
    "https://www.cosmetricsai.com",
    "http://localhost:80",
    "http://localhost:3000",
    "http://127.0.0.1:3000",
    "http://0.0.0.0:3000",
]

CORS_ALLOW_CREDENTIALS = True

CORS_ORIGIN_ALLOW_ALL = True
CORS_ALLOW_HEADERS = [
    "accept",
    "accept-encoding",
    "authorization",
    "content-type",
    "dnt",
    "origin",
    "user-agent",
    "x-csrftoken",
    "x-requested-with",
    "hx-request",
    "hx-current-url",
    "hx-target",
]

CORS_URLS_REGEX = r"^/api/.*$"

ROOT_URLCONF = "cosmetrics_ai.urls"

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": ["templates", os.path.join(BASE_DIR / "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]

MESSAGE_STORAGE = "django.contrib.messages.storage.session.SessionStorage"

AUTHENTICATION_BACKENDS = [
    # Needed to login by username in Django admin, regardless of `allauth`
    "django.contrib.auth.backends.ModelBackend",
    # `allauth` specific authentication methods, such as login by e-mail
    "allauth.account.auth_backends.AuthenticationBackend",
]

SITE_ID = 1

EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

# ACCOUNT_AUTHENTICATION_METHOD = "username_email"
ACCOUNT_EMAIL_REQUIRED = True
ACCOUNT_EMAIL_VERIFICATION = "mandatory"
ACCOUNT_SIGNUP_EMAIL_ENTER_TWICE = True
ACCOUNT_USERNAME_MIN_LENGTH = 4
LOGIN_URL = "/users/login/"
# LOGIN_REDIRECT_URL = "homepage"
# LOGOUT_REDIRECT_URL = "/"
# LOGIN_REDIRECT_URL = '/users/dashboard'


WSGI_APPLICATION = "cosmetrics_ai.wsgi.application"

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases
#
"""
db_name = "cosmplatform"
name = "janitor"
password = "cT8_bNXA1DqJon-v-EGJ"

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql_psycopg2",
        "NAME": config("DB_NAME", default=f"{db_name}"),
        "USER": config("DB_USER", default="janitor"),
        "PASSWORD": config("DB_PASSWORD", default=f"{password}"),
        "HOST": config(
            "DB_HOST",
            default=f"postgres://{name}:{password}*************/{db_name}",
        ),
        "PORT": "5432",
    }
}
"""

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.sqlite3",
        "NAME": os.path.join(BASE_DIR, "db.sqlite3"),
    }
}

# DATABASES = {
#     "default": {
#         "ENGINE": os.getenv("SQL_ENGINE", "django.db.backends.sqlite3"),
#         "NAME": os.getenv("DB_DATABASE", os.path.join(BASE_DIR, "db.sqlite3")),
#         "USER": os.getenv("DB_USER", "user"),
#         "PASSWORD": os.getenv("DB_PASSWORD", "password"),
#         "HOST": os.getenv("DBL_HOST", "localhost"),
#         "PORT": os.getenv("DB_PORT", "5432"),
#     }
# }

"""
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.example.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
DEFAULT_FROM_EMAIL = '<EMAIL>'


"""

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = "en-us"

TIME_ZONE = "UTC"

USE_I18N = True

USE_TZ = True

# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/
STATIC_URL = "static/"
STATICFILES_DIRS = [
    BASE_DIR / "static",
]
STATIC_ROOT = os.path.join(
    BASE_DIR, "staticfiles"
)  # all images are collected into this dir

MEDIA_URL = "/media/"
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

"""

# Using AWS S3 for file storage in Django
AWS_ACCESS_KEY_ID = config("AWS_ACCESS_KEY_ID")
AWS_SECRET_ACCESS_KEY = config("AWS_SECRET_ACCESS_KEY")
AWS_STORAGE_BUCKET_NAME = "cosmplatform-storage"
AWS_S3_SIGNATURE_NAME = "s3v4"
AWS_S3_SIGNATURE_KEY = config("AWS_S3_SIGNATURE_KEY")
AWS_S3_REGION_NAME = config("AWS_S3_REGION_NAME")
AWS_S3_FILE_OVERWRITE = False
AWS_DEFAULT_ACL = None  # "public-read"
AWS_S3_VERIFY = True
DEFAULT_FILE_STORAGE = "storages.backends.s3boto3.S3Boto3Storage"


modules to install - for S3 integration:
boto3
django-storages


"""

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "handlers": {
        "console": {
            "class": "logging.StreamHandler",
        },
    },
    "loggers": {
        "django.db.backends": {
            "level": "DEBUG",
            "handlers": ["console"],
        },
    },
}

NINJA_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=60),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=7),
}
