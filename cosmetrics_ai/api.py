from ninja.security import <PERSON><PERSON><PERSON><PERSON><PERSON>c<PERSON><PERSON>
from ninja_extra import NinjaExtraAPI
from ninja_jwt.controller import NinjaJWTDefaultController

from emailcapture.api import router as emailcapture_router
from products.api import router as products_router
from questionaire.api import router as questionnaire_router
from users.api import router as users_router

api = NinjaExtraAPI(
    csrf=True, title="Cosmetrics API", urls_namespace="preprod", version="1.3.1"
)

api.register_controllers(NinjaJWTDefaultController)

# api.add_router("/auth/", auth_router)
# api.add_router("/auth/jwt/", jwt_controller)
api.add_router("/emails/", emailcapture_router)
api.add_router("/users/", users_router)
api.add_router("/products/", products_router)
api.add_router("/quiz/", questionnaire_router)


class BasicAuth(HttpBasicAuth):
    def authenticate(self, request, username, password):
        if username == "batmen" and password == "password1000":
            return username


@api.get("/basic", auth=BasicAuth())
def basic(request):
    # BasicAuth()
    return {"httpuser": request.auth}
