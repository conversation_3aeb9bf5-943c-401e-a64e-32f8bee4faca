apiVersion: apps/v1
kind: Deployment
metadata:
  name: cosm-deployment
spec:
  replicas: 3
  selector:
    matchLabels:
      app: django
  template:
    metadata:
      labels:
        app: django
    spec:
      containers:
      - name: django
        image: 471112819534.dkr.ecr.eu-west-3.amazonaws.com/django-ec2:latest
        ports:
        - containerPort: 8000
        env:
        - name: DJANGO_SETTINGS_MODULE
          value: cosmetricsai.settings.production
        envFrom:
        - configMapRef:
            name: django-config
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
