import os
from loguru import logger


def format_filename(filename: str) -> str:
    """Format the filename by replacing spaces with underscores,
    removing commas, and ensuring consistency."""
    formatted = (
        filename.lower()
        .replace(" ", "_")
        .replace("-", "_")
        .replace(",", "")
        .replace("___", "_")
        .replace("__", "_")
    )
    logger.info(f"Formatted filename from '{filename}' to '{formatted}'")
    return formatted


def rename_file(original: str, new_name: str) -> None:
    """Renames a file from original name to the new formatted name."""
    try:
        os.rename(original, new_name)
        logger.info(f"Renamed '{original}' to '{new_name}'")
    except Exception as e:
        logger.error(f"Failed to rename '{original}' to '{new_name}': {e}")


def collect_assets(asset_path: str) -> list[str]:
    """Collect all filenames from the given asset path."""
    try:
        files_list = os.listdir(asset_path)
        logger.info(f"Collected assets from '{asset_path}': {files_list}")
        return files_list
    except Exception as e:
        logger.error(f"Failed to collect assets from '{asset_path}': {e}")
        return []


def process_assets(asset_path: str) -> None:
    """Process and rename files in the given asset directory."""
    files = collect_assets(asset_path)
    os.chdir(asset_path)
    for file in files:
        formatted_name = format_filename(file)
        rename_file(file, formatted_name)


if __name__ == "__main__":
    asset_paths = ["staticfiles/media", "conditioners"]

    for path in asset_paths:
        logger.info(f"Processing assets in '{path}'")
        process_assets(path)
        logger.info("=" * 120)

    logger.info("Asset renaming done!")
