from django.contrib.auth.models import User
from django.db import models
from django.db.models.signals import post_save
from django.dispatch import receiver
from django_countries.fields import CountryField

from products.models import Product

"""
##########
# TODO: BIG REFACTOR  - User model
##########
# TODO: use once login is OK via API - BIG REFACTOR!!!!!!!!!!!!!!!

class User(AbstractUser):
    # TODO:  add AUTH_USER_MODEL = users.User to settings
    name = models.Char<PERSON>ield(max_length=255)
    email = models.EmailField(max_length=255, unique=True)
    password = models.CharField(max_length=255)
    username = None
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

"""


class UserProfile(models.Model):
    """
    A user profile model for maintaining default
    delivery information and order history
    """

    user = models.OneToOneField(User, on_delete=models.CASCADE)
    first_name = models.Char<PERSON>ield(max_length=255, default="")
    last_name = models.CharField(max_length=255, default="")
    completed_questions = models.IntegerField(default=0)
    default_phone_number = models.CharField(max_length=20, null=True, blank=True)
    default_street_address1 = models.CharField(max_length=80, null=True, blank=True)
    default_street_address2 = models.CharField(max_length=80, null=True, blank=True)
    default_town_or_city = models.CharField(max_length=40, null=True, blank=True)
    default_county = models.CharField(max_length=80, null=True, blank=True)
    default_postcode = models.CharField(max_length=20, null=True, blank=True)
    default_country = CountryField(blank_label="Country", null=True, blank=True)
    hair_image = models.FileField(null=True, blank=True, upload_to="images/")
    hair_type = models.CharField(max_length=2, null=True, blank=True)
    questionaire_completed = models.BooleanField(default=False)
    recommendations = models.ManyToManyField(Product, related_name="recommended_users")

    def __str__(self):
        # return str(self.user.id)
        return self.user.username


class UserImage(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    image = models.ImageField(null=True, blank=True)


@receiver(post_save, sender=User)
def create_or_update_user_profile(sender, instance, created, **kwargs):
    """
    Create or update the user profile
    """
    if created:
        UserProfile.objects.create(user=instance)
    else:
        instance.userprofile.save()
