# Generated by Django 5.0.7 on 2024-09-01 23:33

from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("products", "0005_product_link"),
        ("users", "0005_alter_userimage_user"),
    ]

    operations = [
        migrations.Add<PERSON>ield(
            model_name="userprofile",
            name="first_name",
            field=models.CharField(default="", max_length=255),
        ),
        migrations.AddField(
            model_name="userprofile",
            name="last_name",
            field=models.CharField(default="", max_length=255),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="hair_type",
            field=models.CharField(blank=True, max_length=2, null=True),
        ),
        migrations.AlterField(
            model_name="userprofile",
            name="recommendations",
            field=models.ManyToManyField(
                related_name="recommended_users", to="products.product"
            ),
        ),
    ]
