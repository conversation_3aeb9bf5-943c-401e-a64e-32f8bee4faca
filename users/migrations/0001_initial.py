# Generated by Django 4.1.2 on 2022-11-30 11:55

import django.db.models.deletion
import django_countries.fields
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("completed_questions", models.IntegerField(default=0)),
                ("questionaire_completed", models.BooleanField(default=False)),
                (
                    "default_phone_number",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "default_street_address1",
                    models.CharField(blank=True, max_length=80, null=True),
                ),
                (
                    "default_street_address2",
                    models.Char<PERSON>ield(blank=True, max_length=80, null=True),
                ),
                (
                    "default_town_or_city",
                    models.Char<PERSON>ield(blank=True, max_length=40, null=True),
                ),
                (
                    "default_county",
                    models.CharField(blank=True, max_length=80, null=True),
                ),
                (
                    "default_postcode",
                    models.CharField(blank=True, max_length=20, null=True),
                ),
                (
                    "default_country",
                    django_countries.fields.CountryField(
                        blank=True, max_length=2, null=True
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="UserImage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("image", models.ImageField(blank=True, null=True, upload_to="")),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.DO_NOTHING,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
