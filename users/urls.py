from django.contrib.auth import views as auth_views
from django.urls import path

from . import views


urlpatterns = [
    # path("", api.urls),
    path("profile/", views.profile, name="profile"),
    path("login/", views.sign_in, name="login"),
    path("logout/", views.sign_out, name="logout"),
    path("register/", views.register, name="register"),
    path("report/", views.report, name="report"),
    path("condition_score/", views.condition_score, name="condition_score"),
    path("hair_id/", views.hair_id, name="hair_id"),
    path("recommendation/", views.recommendation, name="recommendation"),
    path("dashboard/", views.dashboard, name="dashboard"),
    # path(
    #     "password_reset/", auth_views.PasswordResetView.as_view(), name="password_reset"
    # ),
    path(
        "password_reset/",
        views.CustomPasswordResetView.as_view(),
        name="password_reset",
    ),
    path(
        "password_reset/done/",
        auth_views.PasswordResetDoneView.as_view(),
        name="password_reset_done",
    ),
    path(
        "reset/<uidb64>/<token>/",
        auth_views.PasswordResetConfirmView.as_view(),
        name="password_reset_confirm",
    ),
    path(
        "reset/done/",
        auth_views.PasswordResetCompleteView.as_view(),
        name="password_reset_complete",
    ),
]
