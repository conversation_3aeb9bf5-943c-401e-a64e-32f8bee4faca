from typing import Dict
from typing import List

from django.contrib.auth import authenticate, logout
from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.shortcuts import get_object_or_404
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from ninja import Router
from ninja.security import django_auth

# from ninja_jwt.authentication import AuthBearer
from ninja_jwt.controller import NinjaJWTDefaultController
from ninja_jwt.tokens import RefreshToken
from loguru import logger

from products.models import Product
from questionaire.models import Question
from src.report.reporter import Reporter
from .models import UserProfile
from .schemas import (
    LoginSchema,
    UserProfileSchema,
    UserProfileCreateSchema,
    UserSchema,
    UserOutSchema,
    UserOut,
    UserIn,
    UserProfileOutSchema,
    LoginResponseSchema,
    RegisterResponseSchema,
    RegisterSchema,
    UserProfileStatusOutSchema,
    ReportSchema,
    CurrentUserSchema,
)

router = Router()

jwt_controller = NinjaJWTDefaultController()

HAIR_IDS = [6, 7, 8, 9, 37, 42, 42]


@router.get("/me", response=CurrentUserSchema, auth=django_auth)
def me(request):
    logger.info(request.user)
    return request.user


@router.post("/profiles", response=UserProfileCreateSchema)
def create_user_profile(request, payload: UserProfileCreateSchema):
    """Create a new user profile.

    Args:
        request: The HTTP request object.
        payload (UserProfileCreateSchema): The schema containing the data for the new user profile.

    Returns:
        UserProfileSchema: The created user profile schema.
    """
    user = User.objects.create(username=f"user_{payload.user_id}")
    data = payload.to_django_model()
    logger.info(f" \n\tPAYLOAD: {data}\n")
    user_profile = UserProfile.objects.create(user=user, **data)
    for product_id in payload.recommendations:
        product = Product.objects.get(id=product_id)
        user_profile.recommendations.add(product)
    user_profile.save()
    return UserProfileSchema.from_django(user_profile)


@router.get("/profiles/{int:user_id}", response=UserProfileOutSchema)
def get_user_profile(request, user_id: int):
    user_profile = get_object_or_404(UserProfile, user_id=user_id)
    data = UserProfileOutSchema.from_django(user_profile).dict()
    data["default_country"] = (
        user_profile.default_country.code if user_profile.default_country else None
    )
    return data


@router.get("/profiles/recommendations/{int:user_id}", response=List[int])
def get_user_recommendations_only(request, user_id: int):
    user_profile = get_object_or_404(UserProfile, user_id=user_id)
    data = UserProfileOutSchema.from_django(user_profile).dict()
    return data["recommendations"]


@router.get(
    "/profiles/status/{str:complete}", response=List[UserProfileStatusOutSchema]
)
def get_user_profile_status(request, complete: bool):
    profiles = UserProfile.objects.filter(questionaire_completed=complete)
    return profiles


@router.get(
    "/profiles/status/{str:complete}/{int:user_id}", response=UserProfileStatusOutSchema
)
def get_profiles_status(request, complete: bool, user_id: int):
    profile_results = UserProfile.objects.filter(
        questionaire_completed=complete
    ).filter(user_id=user_id)

    return profile_results


@router.get("/", response=List[UserSchema])
def get_all_users(request):
    try:
        users = User.objects.all()
        return users
    except User.DoesNotExist as e:
        logger.debug(f"Cannot retrieve users: {e}\n")


@router.get("/{int:user_id}", response=UserOutSchema)
def get_user_by_id(request, user_id: int):
    try:
        user = get_object_or_404(User, pk=user_id)
        return user
    except User.DoesNotExist as e:
        logger.debug(f"Cannot retrieve users: {e}")


@router.post("/login")
def login(request, data: LoginSchema):
    user = authenticate(username=data.username, password=data.password)
    if not user:
        return {"success": False, "message": "Invalid credentials"}

    # Generate tokens using NinjaJWTDefaultController
    # tokens = jwt_controller.obtain_token(user_token=user.username)
    # tokens = jwt_controller.obtain_pair_for_user(user)

    refresh = RefreshToken.for_user(user)
    access_token = str(refresh.access_token)

    return {
        "success": True,
        "access": access_token,
        "refresh": str(refresh),
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
        },
    }

    # return {
    #     "success": True,
    #     "access": tokens["access"],
    #     "refresh": tokens["refresh"],
    #     "user": {
    #         "id": user.id,
    #         "username": user.username,
    #         "email": user.email,
    #     },
    # }


@router.post("/login-fix")
def login_not(request, data: LoginSchema):
    user = authenticate(username=data.username, password=data.password)
    if not user:
        return {"success": False, "message": "Invalid credentials"}

    refresh = RefreshToken.for_user(user)
    return {
        "success": True,
        "access": str(refresh.access_token),
        "refresh": str(refresh),
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
        },
    }


@method_decorator(csrf_exempt, name="dispatch")
@router.post("/login-old")
@csrf_exempt
def user_login(request, payload: LoginSchema) -> Dict:
    user = authenticate(username=payload.username, password=payload.password)
    if user is not None:
        # Simulate token generation for user
        # In practice, use a real token system such as JWT or OAuth
        return {"success": True, "message": "Login successful", "user_id": user.id}
    else:
        return {"success": False, "message": "Invalid credentials"}


@router.get("/logout", response=LoginResponseSchema)
def user_logout(request) -> Dict:
    # In Basic Auth, logout is managed client-side by removing credentials
    logout(request)
    return {"success": True, "message": "Logout successful"}


#
# @router.put("/{user_id}/profile", response=UserProfileSchema)
# def update_user_profile(request, user_id: int, payload: UserProfileCreateSchema):
#     """Update an existing user profile by user ID.
#
#     Args:
#         request: The HTTP request object.
#         user_id (int): The ID of the user whose profile is to be updated.
#         payload (UserProfileCreateSchema): The schema containing the updated data for the user profile.
#
#     Returns:
#         UserProfileSchema: The updated user profile schema.
#     """
#     user_profile = get_object_or_404(UserProfile, user_id=user_id)
#     data = payload.to_django_model()
#     for key, value in data.items():
#         setattr(user_profile, key, value)
#     user_profile.recommendations.clear()
#     for product_id in payload.recommendations:
#         product = Product.objects.get(id=product_id)
#         user_profile.recommendations.add(product)
#     user_profile.save()
#     return UserProfileSchema.from_django(user_profile)


@router.put("/signup", response=UserOut)
def signup(request, data: UserIn):
    user = User.objects.create_user(
        username=data.username,
        password1=data.password1,
        password2=data.password2,
        email=data.email,
    )
    return user


@router.post("/register", response=RegisterResponseSchema)
@csrf_exempt
def register_user(request, payload: RegisterSchema) -> Dict:
    # Check if the username already exists
    if User.objects.filter(username=payload.username).exists():
        return {"success": False, "message": "Username already exists"}

    # Check if the email already exists
    if User.objects.filter(email=payload.email).exists():
        return {"success": False, "message": "Email already exists"}

    # Validate the password
    try:
        validate_password(payload.password)
    except ValidationError as e:
        return {"success": False, "message": " ".join(e.messages)}

    # Create a new user
    # user = User.objects.create_user(
    #     username=payload.username, email=payload.email, password=payload.password
    # )

    # Respond with success
    return {"success": True, "message": "User registered successfully"}


@router.put("/{int:user_id}/profile", response=UserProfileOutSchema)
def update_user_profile(request, user_id: int, payload: UserProfileSchema):
    """Update an existing user profile by user ID.

    Args:
        request: The HTTP request object.
        user_id (int): The ID of the user whose profile is to be updated.
        payload (UserProfileCreateSchema): The schema containing the updated data for the user profile.

    Returns:
        UserProfileSchema: The updated user profile schema.
    """
    user_profile = get_object_or_404(UserProfile, user_id=user_id)
    data = payload.to_django_model()

    logger.debug(f"\n\tDATA TYPE: {type(data)}\n")
    logger.debug(f"\n\tDATA CONTENT: {data}\n")
    logger.debug(f"\n\tPROFILE CONTENT: {user_profile}\n")

    # Update user profile fields
    for key, value in data.items():
        setattr(user_profile, key, value)

    # Use set() to update recommendations
    recommended_products = Product.objects.filter(id__in=payload.recommendations)
    user_profile.recommendations.set(recommended_products)

    # Save the user profile
    user_profile.save()

    # Return the updated user profile schema
    return UserProfileSchema.from_django(user_profile)


@router.delete("/{user_id}", response={204: None})
def delete_user_profile(request, user_id: int):
    """Delete a user profile by user ID.

    Args:
        request: The HTTP request object.
        user_id (int): The ID of the user whose profile is to be deleted.

    Returns:
        int: HTTP 204 No Content status code.
    """
    user_profile = get_object_or_404(UserProfile, user_id=user_id)
    user_profile.delete()
    return 204


# Create an instance of the Reporter class
reporter = Reporter()


@router.get(
    "/scores/{int:user_id}", response=ReportSchema, summary="Get Scores for a User"
)
def get_score_by_user_id(request, user_id: int):
    report = reporter.create_report(user_id)
    return report


# Optionally, you can also create an endpoint for getting answer scores directly
@router.get("/scores/{user_id}/{question_id}", summary="Get Answer Score")
def get_user_score_for_question(request, user_id: int, question_id: int):
    user = get_object_or_404(User, id=user_id)
    question = get_object_or_404(Question, id=question_id)
    score = reporter.get_answer_score(user, question)
    return {"score": score}
