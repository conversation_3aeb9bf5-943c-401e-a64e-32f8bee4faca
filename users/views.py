import pandas as pd
from django.contrib import messages
from django.contrib.auth import authenticate, login
from django.contrib.auth import logout
from django.contrib.auth import views as auth_views
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.shortcuts import render, redirect
from django.urls import reverse_lazy
from loguru import logger

from questionaire.models import Question, Reply
from src.report.reporter import Reporter
from .forms import PasswordResetForm, UserProfileForm, UserRegisterForm
from .forms import UserLoginForm
from .models import UserProfile


@login_required
def profile(request):
    """Display the user's profile."""
    user_profile = UserProfile.objects.get(user=request.user)

    if request.method == "POST":
        form = UserProfileForm(request.POST, instance=user_profile)
        if form.is_valid():
            form.save()
            messages.success(request, "Profile updated successfully.")
        else:
            messages.error(request, "Update failed. Please ensure the form is valid.")
    else:
        form = UserProfileForm(instance=user_profile)

    return render(request, "users/profile.html", {"form": form})


@login_required
def report(request):
    context = {"replies": Reply.objects.filter(user=request.user).order_by("id")}
    return render(request, "users/report.html", context)


@login_required
def hair_id(request):
    user_profile = UserProfile.objects.get(user=request.user)
    context = {"error": "Questionnaire not completed yet"}

    if user_profile.completed_questions >= Question.objects.count():
        context = {
            "hair_type": Reply.objects.get(
                question__position=6, user=request.user
            ).answer.value,
            "hair_texture": Reply.objects.get(
                question__position=9, user=request.user
            ).answer.value,
            "porosity": Reply.objects.get(
                question__position=37, user=request.user
            ).answer.value,
            "hair_density": Reply.objects.get(
                question__position=8, user=request.user
            ).answer.value,
        }
    return render(request, "users/hair_id.html", context)


@login_required
def recommendation(request):
    user_profile = UserProfile.objects.get(user=request.user)
    context = {}

    if user_profile.completed_questions < Question.objects.count():
        context["error"] = (
            "Can't get recommendations if questionnaire is not completed."
        )
    elif user_profile.recommendations.exists():
        context["recommendations"] = user_profile.recommendations.all()
    else:
        context["error"] = (
            "Recommendations are being generated, please refresh the page in a couple of seconds."
        )

    return render(request, "users/recommendation.html", context)


# def sign_in(request):
#     if request.user.is_authenticated:
#         request.GET.get("next", "/users/dashboard")
#         # return redirect("profile")  # Redirect to profile page if already logged in
#
#     if request.method == "GET":
#         form = UserLoginForm()
#
#         return render(request, "users/login.html", {"form": form})
#
#     elif request.method == "POST":
#         form = UserLoginForm(data=request.POST)
#         if form.is_valid():
#             username = form.cleaned_data["username"]
#             password = form.cleaned_data["password"]
#
#             user = authenticate(request, username=username, password=password)
#             if user is not None:
#                 login(request, user)
#                 redirect_to = request.GET.get("next", "/users/dashboard")
#                 return redirect(redirect_to)
#
#         messages.error(request, "Invalid username or password.")
#         return render(request, "users/login.html", {"form": form})


def sign_in_old(request):
    if request.user.is_authenticated:
        return redirect(request.GET.get("next", "/users/dashboard"))

    if request.method == "GET":
        form = UserLoginForm()
        return render(request, "users/login.html", {"form": form})

    elif request.method == "POST":
        form = UserLoginForm(data=request.POST)
        if form.is_valid():
            username = form.cleaned_data["username"]
            password = form.cleaned_data["password"]

            user = authenticate(request, username=username, password=password)
            if user is not None:
                login(request, user)
                redirect_to = request.GET.get("next", "/users/dashboard")
                return redirect(redirect_to)
        messages.error(request, "Invalid username or password.")
        return render(request, "users/login.html", {"form": form})


def sign_in_try(request):
    if request.method == "POST":
        username = request.POST["username"]
        password = request.POST["password"]

        user = authenticate(username=username, password=password)

        if user is not None:
            login(request, user)
            fname = user.first_name
            return render(request, "index.html", {"fname": fname})

        else:
            messages.error(request, "Bad credentials")
            return redirect("home")

    return render(request, "users/login.html")


def sign_in(request):
    # if request.user.is_authenticated:
    #     # Redirect to the dashboard or next page if already logged in
    #     return redirect(request.GET.get("next", "/users/dashboard"))

    if request.method == "POST":
        form = UserLoginForm(data=request.POST)
        if form.is_valid():
            username = form.cleaned_data["username"]
            password = form.cleaned_data["password"]

            # Authenticate user
            user = authenticate(request, username=username, password=password)
            if user is not None:
                # Log the user in
                login(request, user)
                logger.info("\n\tLogin attempted!\n")
                # Redirect to 'next' or default to '/users/dashboard'
                return render(request, "users/dashboard.html")
                # redirect_to = request.GET.get("next", "/users/dashboard")
                # return redirect(redirect_to)
            else:
                # Invalid credentials
                messages.error(request, "Invalid username or password.")
        else:
            # Invalid form data
            messages.error(request, "Invalid form submission.")

    else:
        form = UserLoginForm()
        # Render login page with form (GET request or invalid POST)
        return render(request, "users/login.html", {"form": form})


@login_required
def dashboard(request):
    return render(request, "users/dashboard.html")


# @login_required
# def sign_out(request):
#     logger.debug(f"\n\n\tREQ_METHOD: {request.method}\n")
#     if request.method == 'GET':
#         logout(request)
#         logger.debug("User now logged out.")
#         return redirect('login')
#     return redirect("/")


@login_required
def sign_out(request):
    logout(request)
    logger.debug("User now logged out.")
    return redirect("homepage")


def register(request):
    if request.method == "GET":
        form = UserRegisterForm()
        return render(request, "users/register.html", {"form": form})

    elif request.method == "POST":
        form = UserRegisterForm(data=request.POST)
        logger.info(f"\n\tREGISTER FORM: {form}")
        if form.is_valid():
            username = form.cleaned_data["username"]
            password = form.cleaned_data["password1"]
            email = form.cleaned_data["email"]

            if not User.objects.filter(username=username).exists():
                User.objects.create_user(
                    username=username, email=email, password=password
                )
                messages.success(request, "Account created successfully.")
                return redirect("/")
            else:
                messages.error(request, "Username already exists.")
        return render(request, "users/register.html", {"form": form})


@login_required
def condition_score(request):
    user_profile = UserProfile.objects.get(user=request.user)
    context = {"error": "Questionnaire has not been completed yet."}

    if user_profile.completed_questions >= Question.objects.count():
        reporter = Reporter()
        context = {"report": reporter.create_report(request.user.id)}

    return render(request, "users/condition_score.html", context)


def load_contextual_report(request):
    raw_df = (
        pd.read_csv()
    )  # Add the file path or necessary parameters for reading the CSV
    raw_df.info()


class CustomPasswordResetView(auth_views.PasswordResetView):
    template_name = "registration/password_reset_form.html"
    email_template_name = "registration/password_reset_email.html"
    subject_template_name = "registration/password_reset_subject.txt"
    success_url = reverse_lazy("password_reset_done")
    form_class = PasswordResetForm
