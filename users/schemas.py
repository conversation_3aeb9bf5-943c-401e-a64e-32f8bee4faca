from ninja import Schema, ModelSchema
from typing import List, Optional
from django_countries.fields import Country
from django.contrib.auth.models import User

from products.schemas import ProductOutSchema
from .models import UserProfile


class CurrentUserSchema(Schema):
    username: str
    id: int


class UserSchema(ModelSchema):
    class Config:
        from_attribute = True
        model = User
        model_fields = "__all__"


class UserIn(Schema):
    username: str
    password1: str
    password2: str
    email: str


class RequestUserOutSchema(Schema):
    id: str
    # username: str
    # email: str


class LoginSchema(Schema):
    username: str
    password: str


class LoginResponseSchema(Schema):
    success: bool
    message: str


class RegisterSchema(Schema):
    username: str
    email: str
    password: str


# Define a schema for the registration response
class RegisterResponseSchema(Schema):
    success: bool
    message: str


class UserOut(Schema):
    id: int
    username: str
    email: str


class UserProfileSchema(Schema):
    class Config:
        from_attributes = True
        model = UserProfile
        model_fields = "__all__"


class UserProfileStatusOutSchema(Schema):
    user_id: int
    completed_questions: int
    hair_image: Optional[str] = None
    hair_type: Optional[str] = None
    questionaire_completed: bool
    recommendations: List[ProductOutSchema]


class UserProfileOutSchema(Schema):
    user_id: int
    completed_questions: int
    default_phone_number: Optional[str] = None
    default_street_address1: Optional[str] = None
    default_street_address2: Optional[str] = None
    default_town_or_city: Optional[str] = None
    default_county: Optional[str] = None
    default_postcode: Optional[str] = None
    default_country: Optional[str] = None
    hair_image: Optional[str] = None
    hair_type: Optional[str] = None
    questionaire_completed: bool
    recommendations: List[int]

    # @computed_field
    # @property
    # def get_recommendations(self) -> List[ProductOutSchema]:
    #     products = Product.objects.filter(pk__in=self.recommendations)
    #     return products

    @classmethod
    def from_django(cls, obj):
        """Maps obj to UserProfile model in Django."""
        return cls(
            user_id=obj.user.id,
            completed_questions=obj.completed_questions,
            default_phone_number=obj.default_phone_number,
            default_street_address1=obj.default_street_address1,
            default_street_address2=obj.default_street_address2,
            default_town_or_city=obj.default_town_or_city,
            default_county=obj.default_county,
            default_postcode=obj.default_postcode,
            default_country=obj.default_country.code if obj.default_country else None,
            hair_image=obj.hair_image.url if obj.hair_image else None,
            hair_type=obj.hair_type,
            questionaire_completed=obj.questionaire_completed,
            recommendations=[product.id for product in obj.recommendations.all()],
        )


class UserProfileCreateSchema(Schema):
    completed_questions: int
    default_phone_number: Optional[str] = None
    default_street_address1: Optional[str] = None
    default_street_address2: Optional[str] = None
    default_town_or_city: Optional[str] = None
    default_county: Optional[str] = None
    default_postcode: Optional[str] = None
    default_country: Optional[str] = None
    hair_image: Optional[str] = None
    hair_type: Optional[str] = None
    questionaire_completed: bool
    recommendations: List[int]

    def to_django_model(self):
        data = self.dict()
        if data["default_country"]:
            data["default_country"] = Country(data["default_country"])
        return data


class UserOutSchema(ModelSchema):
    class Config:
        from_attributes = True
        model = User
        model_fields = ["id", "username", "first_name", "last_login"]


"""


class UserProfileCreateSchema(Schema):
    completed_questions: Optional[int] = Field(0, description="Number of completed questions")
    default_phone_number: Optional[str] = Field(None, description="Default phone number")
    default_street_address1: Optional[str] = Field(None, description="Default street address line 1")
    default_street_address2: Optional[str] = Field(None, description="Default street address line 2")
    default_town_or_city: Optional[str] = Field(None, description="Default town or city")
    default_county: Optional[str] = Field(None, description="Default county")
    default_postcode: Optional[str] = Field(None, description="Default postcode")
    default_country: Optional[str] = Field(None, description="Default country")
    hair_image: Optional[str] = Field(None, description="Path to hair image")
    hair_type: Optional[str] = Field(None, description="Hair type")
    questionaire_completed: Optional[bool] = Field(False, description="Questionnaire completion status")
    recommendations: List[int] = Field([], description="List of recommended product IDs")


class UserProfileSchema(Schema):
    id: int
    user_id: int
    completed_questions: int
    default_phone_number: Optional[str]
    default_street_address1: Optional[str]
    default_street_address2: Optional[str]
    default_town_or_city: Optional[str]
    default_county: Optional[str]
    default_postcode: Optional[str]
    default_country: Optional[str]
    hair_image: Optional[str]
    hair_type: Optional[str]
    questionaire_completed: bool
    recommendations: List[int]
"""


# Define the schema for each percentage output
class PercentageSchema(Schema):
    value: float
    description: str


# Define the schema for the entire report
class ReportSchema(Schema):
    dry_score_percentage: PercentageSchema
    damage_score_percentage: PercentageSchema
    sensitivity_percentage: PercentageSchema
    sebum_oily_percentage: PercentageSchema
    sebum_dry_percentage: PercentageSchema
    dsc_percentage: PercentageSchema
    flake_score_percentage: PercentageSchema


class ScoreSchema(Schema):
    score: int


class ScoreValueSchema(Schema):
    scores: List[ScoreSchema]
