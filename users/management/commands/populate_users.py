import json
from django.core.management.base import BaseCommand
from users.models import User
from loguru import logger


class Command(BaseCommand):
    """
    Populate sample users from a JSON file.
    """

    def handle(self, *args, **options):
        # Delete all existing users to start fresh
        try:
            User.objects.all().delete()
            logger.info("Existing users deleted.")
        except Exception as e:
            logger.error(f"Error deleting users: {e}")
            return

        # Load users from JSON file
        try:
            with open("users/fixtures/users.json", "r") as f:
                users_data = json.load(f)
        except FileNotFoundError:
            logger.error("The file users/fixtures/users.json was not found.")
            return
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON: {e}")
            return
        except Exception as e:
            logger.error(f"Unexpected error reading JSON file: {e}")
            return

        # Create users from JSON data
        for user in users_data:
            name = user.get("name")
            email = user.get("email")
            password = user.get("password")

            if not all([name, email, password]):
                logger.warning(f"User data incomplete: {user}")
                continue

            username = name.replace(" ", "_")

            try:
                User.objects.create_user(
                    username=username, email=email, password=password
                )
                logger.info(f"User created: {username}")
            except Exception as e:
                logger.error(f"Could not create user {username}: {e}")

        logger.info("User creation process completed.")
