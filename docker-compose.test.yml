services:
  db:
    container_name: cosm_db
    restart: unless-stopped
    image: postgres:16.1
    volumes:
      - data_volume:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=cosmetrics
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=cosmetrics_pass
    networks:
      - default
  web:
    container_name: cosm_web
    restart: unless-stopped
    image: ghcr.io/cosm1/software-development:test
    ports:
      - 80:80
    environment:
      - DB_NAME=cosmetrics
      - DB_USER=postgres
      - DB_PASSWORD=cosmetrics_pass
      - DB_HOST=cosm_db
      - ALLOWED_HOSTS=0.0.0.0
    depends_on:
      - db
    networks:
      - default

volumes:
  data_volume:
networks:
  default:
    name: internal
    driver: bridge
