import styled from "styled-components";
import Column from "../../components/Column";
import FlexEnd from "../../components/FlexEnd";
import CosmetricsLogo from '../../assets/images/cosmetrics-logo-white.svg';
import { FastArrowLeft, HomeSimple, LogOut, QuoteMessage, Reports, Star, SubmitDocument, User } from 'iconoir-react';
import { useCallback, useMemo } from "react";
import useScale from "../../hooks/useScale";

const NavBarRoot = styled.div<{ $width?: string; }>`
    display: flex;
    flex-direction: column;
    position: relative;

    height: 100vh;
    width: ${props => props.$width};
    box-sizing: border-box;
    background-color: ${props => props.$width === '0' ? 'transparent' : '#303445'};
    padding: 1vw 0.5vw 1vw 0.5vw;
    color: #FFFFFF;
    border-radius: 0 0 24px 0;
    overflow: hidden;
    z-index: 10001;
    transition: 0.3s;

    img {
        align-self: center;
        margin: 0 0 4vw 0;
    }

    button {
        display: flex;
        flex-direction: row;
        height: 52px;
        align-items: center;
        width: 100%;
        outline: none;
        border: none;
        gap: 8px;
        background-color: transparent;
        color: #FFFFFF;
        font-size: 0.875em;
        letter-spacing: 0.5px;
        cursor: pointer;
    }

    @media only screen and (max-width: 768px) {
        position: fixed;

        button {
            font-size: 0.675em;
        }

        img {
            margin: 4vw 0 4vw 0;
            width: 80%;
        }
    }
`

const Header = styled.div`
    display: flex;
    justify-content: flex-end;

    button {
        height: fit-content;
        width: fit-content;
        font-size: 1em;
    }
`

interface Props {
    visible?: boolean;
    toggleVisible?: (() => void);
}

const NavBar: React.FC<Props> = ({
    visible,
    toggleVisible,
}) => {

    const { isMobile } = useScale();

    const navBarWidth = useMemo(() => {
        if (!isMobile) {
            return '16vw';
        } else {
            return visible ? '54vw' : '0';
        }
    }, [isMobile, visible])

    const handleNavigate = useCallback((path: string) => window.location.href = path, []);

    return (
        <NavBarRoot $width={navBarWidth}>
            {
                visible &&
                <>
                    {
                        isMobile &&
                        <Header>
                            <button onClick={toggleVisible}><FastArrowLeft /></button>
                        </Header>
                    }
                    <img src={CosmetricsLogo} alt='' />

                    <Column>
                        <button onClick={() => handleNavigate('/dashboard/')}><Reports /> Hair Analysis</button>
                        <button onClick={() => handleNavigate('/dashboard/recommendations')}><Star /> Recommendations</button>
                        <button onClick={() => handleNavigate('/dashboard/reporthistory')}><SubmitDocument /> Report History</button>
                        <button onClick={() => handleNavigate('/dashboard/account')}><User /> Account</button>
                    </Column>

                    <FlexEnd>
                        <Column style={{ borderTop: '1.5px solid #FFFFFF' }}>
                            <button onClick={() => handleNavigate('/')}><HomeSimple /> Cosmetrics Home</button>
                            <button onClick={() => handleNavigate('/feedback')}><QuoteMessage /> Give Feedback</button>
                            <button><LogOut /> Logout</button>
                        </Column>
                    </FlexEnd>
                </>
            }
        </NavBarRoot>
    )
};

export default NavBar;