import styled from 'styled-components';
import NavBar from './NavBar';
import useScale from '../../hooks/useScale';
import { useEffect, useState } from 'react';
import { Menu } from 'iconoir-react';

const Root = styled.div`
    display: flex;
    flex-direction: row;
    position: relative;
    min-height: 100vh;
    width: 100vw;
    box-sizing: border-box;
    background-color: #FCFCFC;
`

const Column = styled.div`
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100vw;

    box-sizing: border-box;
`

const Header = styled.div`
    display: flex;
    box-sizing: border-box;
    padding: 3vw 5vw 0 5vw;

    button {
        background-color: transparent;
        border: none;
        outline: none;
        padding: 0;
        margin: 0;
        font-size: 1em;
    }
`

const Dashboard = ({ children }: any) => {

    const { isMobile } = useScale();

    const [isNavVisible, setIsNavVisible] = useState<boolean>(!isMobile);

    // useEffect(() => {
    //     setIsNavVisible(true);
    // }, [])

    return (
        <Root>
            {/* {!isMobile &&
                <NavBar />
            } */}
            <NavBar visible={isNavVisible} toggleVisible={() => { setIsNavVisible(prev => !prev); }} />
            <Column>
                {isMobile &&
                    <Header>
                        <button onClick={() => { setIsNavVisible(true); }}><Menu /></button>
                    </Header>
                }
                {children}
            </Column>
        </Root>
    );
}

export default Dashboard;
