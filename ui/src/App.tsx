import { BrowserRouter as Router, Route, Routes } from 'react-router-dom';
import Home from './screens/Home';
import DashboardHome from './screens/Dashboard/Home';
import './App.css';
import Quiz from './screens/Quiz';
import Signup from './screens/Signup';
import Login from "./screens/Login";
import NotFound from "./screens/NotFound";
import Recommendations from './screens/Dashboard/Recommendations';
import ReportHistory from './screens/Dashboard/ReportHistory';
import Account from './screens/Dashboard/Account';
import Register from "./screens/Register";
import Header from './components/Header';



function App() {
  return (

      <Router>
        <Header />
        <Routes>
          <Route path='' element={<Home />} />
          <Route path='/quiz' element={<Quiz />} />
          <Route path='/login' element={<Login />} />
          <Route path='/signup' element={<Signup />} />
          <Route path='/register' element={<Register />} />
          <Route path='/dashboard' Component={DashboardHome} />
          <Route path='/dashboard/recommendations' Component={Recommendations} />
          <Route path='/dashboard/reporthistory' Component={ReportHistory} />
          <Route path='/dashboard/account' Component={Account} />

          <Route path="*" element={<NotFound />} />
        </Routes>
      </Router>
  );
}

export default App;
