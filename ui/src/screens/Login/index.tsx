import styled from "styled-components";
import { useCallback, useMemo, useState } from "react";
import Button from "../../components/Button";
import Row from "../../components/Row";
import Column from "../../components/Column";
import axios from "axios";
import useSignIn from 'react-auth-kit/hooks/useSignIn';


const Root = styled.div`
    display: flex;
    position: relative;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    height: 100vh;
    width: 100vw;
    color: #303445;
    padding: 0 10% 0 10%;

    h1 {
        margin: 0 0 50px 0;
        padding: 0;
        text-align: center;
    }

    span {
        font-size: 0.95em;
    }

    button {
        width: 150px;
        background-color: #78D3F7;
        align-self: center;
        margin: 50px 0 20px 0;
    }

    a {
        text-decoration: none;
        font-weight: 500;
        color: #303445;
        font-size: 0.875em;
    }

    @media only screen and (max-width: 768px) {

    }
`;

const TextBox = styled.input`
    display: flex;
    flex-direction: row;
    outline: 1.3px solid #303445;
    border: none;
    border-radius: 8px;
    height: 48px;
    width: 24vw;
    padding: 0 12px 0 12px;
    margin: 10px 0 30px 0;

    @media only screen and (max-width: 768px) {
        width: 100%;
    }
`;
// const COSM_API_LOGIN = 'http://localhost/api/users/login'
const baseURL = process.env.BACKEND_API;
const Login = () => {

    const type = useMemo(() => {
        if (window.location.href.includes('/login')) {
            return 'login';
        } else {
            return 'signup';
        }
    }, []);

    const [userDetails, setUserDetails] = useState({
        name: '',
        email: '',
        password: '',
        confirmPassword: '',
    });

    const handleSignUp = useCallback(() => {
        return userDetails;
    }, [userDetails]);

    const handleLogin = useCallback(() => {
        // e.preventDefault();
        // axios.post('http://localhost/api/me')
        return userDetails;
    }, [userDetails]);

    const handleAuthAction = useCallback(() => {
        if (type === 'login') {
            handleLogin();
        } else if (type === 'signup') {
            handleSignUp();
        }
    }, [handleLogin, handleSignUp, type]);

    return (
        <Root>
            <Column>
                <h1>{type === 'login' ? 'Log In' : 'Sign Up'}</h1>
                {
                    type === 'signup' &&
                    <>
                        <span>Name:</span>
                        <TextBox value={userDetails.name} onChange={(event) => {
                            setUserDetails(prev => ({ ...prev, name: event.target.value }));
                        }} placeholder="my name" />
                    </>
                }
                <span>Email:</span>
                <TextBox type="email" value={userDetails.email} onChange={(event) => {
                    setUserDetails(prev => ({ ...prev, email: event.target.value }));
                }} placeholder="<EMAIL>" />

                <span>Password</span>
                {type === 'signup' &&
                    <span style={{ fontSize: '0.8em', color: '#9D9EA7', marginTop: 4 }}>Must contain at least 1 capital letter and 1 number</span>}
                <TextBox type="password" value={userDetails.password} onChange={(event) => {
                    setUserDetails(prev => ({ ...prev, password: event.target.value }));
                }} placeholder="password" />
                {
                    type === 'signup' &&
                    <>
                        <span>Confirm Password</span>
                        <TextBox type="password" value={userDetails.confirmPassword} onChange={(event) => {
                            setUserDetails(prev => ({ ...prev, confirmPassword: event.target.value }));
                        }} placeholder="confirm password" />
                    </>
                }
                <Button onClick={handleAuthAction}>{type === 'login' ? 'Login' : 'Sign Up'}</Button>

                {
                    type === 'signup'
                        ?
                        <span style={{ fontSize: '0.6em', textAlign: 'center' }}>By signing up you agree to the terms and conditions of Cosmetrics</span>
                        :
                        <Row style={{ justifyContent: 'center', gap: 4, marginTop: 12 }}>
                            <span style={{ fontSize: '0.875em', color: '#9D9EA7' }}>Don't have an account?</span>
                            <a href="/signup">Sign Up</a>
                        </Row>
                }
            </Column>
        </Root>
    );
};

export default Login;
