import { QuizPageRoot, TextBox } from "../../style"

const ExtraInformation: React.FC<QuizPageProps> = ({
    answers,
    setAnswer,
    onAction,
}) => {

    return (
        <QuizPageRoot>

            <TextBox placeholder="Write here" value={answers['extra_information']} onChange={(text) => { text.target.value.length <= 255 && setAnswer('extra_information', text.target.value) }} />
        </QuizPageRoot>
    )
};

export default ExtraInformation;