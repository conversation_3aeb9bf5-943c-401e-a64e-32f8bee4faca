import React from 'react';
import styled from 'styled-components';
import Question from './Question';
import ImageQuestion from './ImageQuestion';

const Root = styled.div`
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: 100%;
    width: 100vw;
    padding: 5vw;

    h1 {
        font-size: 2em;
        font-weight: 500;
    }
`;

const General: React.FC<QuizPageProps> = ({ questions, answers, setAnswer, onAction }) => {
    return (
        <Root>
            {questions && questions.map((question, index) => {
                if (question.type === 'question') {
                    return (
                        <Question
                            {...question}
                            key={question.id}
                            value={answers[question.id]}
                            setAnswer={setAnswer}
                            index={index}
                        />
                    )
                } else {
                    return (
                        <ImageQuestion
                            {...question}
                            key={question.id}
                            value={answers[question.id]}
                            setAnswer={setAnswer}
                            index={index}
                        />
                    )
                }
            })}
        </Root>
    );
};

export default General;
