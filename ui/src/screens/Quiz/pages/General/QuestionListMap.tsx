import ImageQuestion from "./ImageQuestion";
import Question from "./Question";
import TextQuestion from "./TextQuestion";

interface Props extends QuizPageProps { }

const QuestionListMap: React.FC<Props> = ({
    questions,
    answers,
    setAnswer,
}) => {
    return (
        <>
            {questions && questions.map((question, index) => {
                if (question.type === 'question') {
                    return (
                        <Question
                            {...question}
                            key={question.id}
                            value={answers[question.id]}
                            setAnswer={setAnswer}
                            index={index}
                        />
                    )
                } else if (question.type === 'image_question') {
                    return (
                        <ImageQuestion
                            {...question}
                            key={question.id}
                            value={answers[question.id]}
                            setAnswer={setAnswer}
                            index={index}
                        />
                    )
                } else if (question.type === 'text_question') {
                    return (
                        <TextQuestion
                            {...question}
                            key={question.id}
                            value={answers[question.id]}
                            setAnswer={setAnswer}
                            index={index}
                        />
                    )
                } else {
                    return null;
                }
            })}
        </>
    )
}

export default QuestionListMap;
