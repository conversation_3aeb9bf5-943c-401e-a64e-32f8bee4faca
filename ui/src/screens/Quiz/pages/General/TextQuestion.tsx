import React, { useMemo } from 'react';
import styled from 'styled-components';
import Column from '../../../../components/Column';

export const QuestionDiv = styled.div`
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 80px 0 40px 12px;

    h3 {
        margin: 0;
        padding: 0;
        font-weight: 600;
    }

    h4 {
        margin: 0;
        padding: 0;
        font-size: 0.9em;
        font-weight: 300;
    }

    h5 {
        margin: 6px 0 0 0;
        padding: 0;
        font-size: 0.875em;
        font-weight: 300;
    }
`;

const TextArea = styled.textarea`
    display: flex;
    position: relative;
    box-sizing: border-box;
    height: 9vw;
    width: 95%;
    border: 1.3px solid #303445;
    outline: none;
    background-color: transparent;
    resize: none;
    border-radius: 8px;
    padding: 1.8vw;
    font-size: 0.95em;

    @media only screen and (max-width: 768px) {
        height: 24vw;
        font-size: 0.875em;
        padding: 4vw;
    }
`

const TextQuestion: React.FC<QuestionProps & { index: number }> = ({
    id,
    title,
    value,
    setAnswer,
    subheader,
    index,
}) => {

    const backgroundColor = useMemo(() => {
        const val = index;

        if (val % 2 === 0 || isNaN(val)) {
            return '#FFFFFF';
        } else {
            return '#FCFCFC';
        }
    }, [index]);

    return (
        <QuestionDiv style={{ backgroundColor }}>
            <h3>{title}</h3>
            {subheader && <h5>{subheader}</h5>}
            <Column style={{ marginTop: 12 }}>
                <TextArea placeholder='Write here' value={value} onChange={(event) => { setAnswer(id, event.target.value) }} />
            </Column>
        </QuestionDiv>
    );
};

export default React.memo(TextQuestion);
