interface Question {
    id: string;
    title: string;
    subheader?: string;
    options: { label: string; image?: string }[];
    type: 'question' | 'image_question' | 'text_question';
}

type SetAnswer = (id: string, value: string) => void;

interface QuestionProps extends Question {
    value: string;
    setAnswer: SetAnswer;
    // setAnswer: (id: string, value: string | boolean) => void;
}

interface QuizPageProps {
    questions?: Question[];
    answers: { [key: string]: string };
    // setAnswer: (questionId: string, answer: string | boolean) => void;
    setAnswer: SetAnswer;
    onAction?: ((action: string) => void);
}