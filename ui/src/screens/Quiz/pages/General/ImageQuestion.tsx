import styled from "styled-components";
import { QuestionDiv } from "./Question";
import Column from "../../../../components/Column";
import { useMemo } from "react";

const ImageQuestionDiv = styled(QuestionDiv)`
    img {
        height: 180px;
        width: 180px;
    }

    span {
        margin: 12px 0 0 0;
    }
`;

const ImageRow = styled.div`
    display: flex;
    flex-direction: row;
    align-items: center;
    margin: 30px 0 0 0;
    gap: 30px;

    @media only screen and (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
    }
`

const ImageQuestion: React.FC<QuestionProps & { index: number }> = ({
    title,
    subheader,
    options,
    index,
    setAnswer,
    value,
    id
}) => {

    const backgroundColor = useMemo(() => {
        const val = index;

        if (val % 2 === 0 || isNaN(val)) {
            return '#FFFFFF';
        } else {
            return '#FCFCFC';
        }
    }, [index]);

    return (
        <ImageQuestionDiv style={{ backgroundColor }}>
            <h3>{title}</h3>
            {subheader && <h5>{subheader}</h5>}

            <ImageRow>
                {
                    options.map((val, key) => {
                        const selected = val.label === value;
                        return (
                            <Column style={{ alignItems: 'center' }} onClick={() => setAnswer(id, val.label)}>
                                {val.image && <img src={val.image} alt="" style={{ outline: `${selected ? 2.5 : 0}px solid #303445` }} />}
                                <span>{val.label}</span>
                            </Column>
                        )
                    })
                }
            </ImageRow>
        </ImageQuestionDiv>
    )
}

export default ImageQuestion;