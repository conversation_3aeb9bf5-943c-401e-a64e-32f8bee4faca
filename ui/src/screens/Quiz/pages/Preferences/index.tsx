import React from "react";
import { QuizPageRoot } from "../../style";
import QuestionListMap from "../General/QuestionListMap";

const Preferences: React.FC<QuizPageProps> = ({
    onAction,
    ...props
}) => {
    return (
        <QuizPageRoot>
            <QuestionListMap {...props} />
            {/* {questions && questions.map((question, index) => {
                return (
                    <Question
                        {...question}
                        key={question.id}
                        value={answers[question.id]}
                        setAnswer={setAnswer}
                        index={index}
                    />
                )
            })} */}
        </QuizPageRoot>
    );
};

export default Preferences;