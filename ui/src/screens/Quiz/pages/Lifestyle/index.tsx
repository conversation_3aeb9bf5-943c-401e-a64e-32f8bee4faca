import React from "react";
import { QuizPageRoot } from "../../style";
import Question from "../General/Question";

const Lifestyle: React.FC<QuizPageProps> = ({
    answers,
    setAnswer,
    questions,
    onAction,
}) => {
    return (
        <QuizPageRoot>

            {questions && questions.map((question, index) => {
                return (
                    <Question
                        {...question}
                        key={question.id}
                        value={answers[question.id]}
                        setAnswer={setAnswer}
                        index={index}
                    />
                )
            })}
        </QuizPageRoot>
    );
};

export default Lifestyle;