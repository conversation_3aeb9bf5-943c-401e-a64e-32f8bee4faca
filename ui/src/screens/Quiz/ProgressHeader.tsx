import React, { useMemo } from 'react'
import styled from 'styled-components'
import Column from '../../components/Column'
import colors from '../../style/colors'
import Row from '../../components/Row'

const Root = styled.div`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    position: relative;
    padding: 3vw 5vw 0 5vw;

    h1 {
        font-size: 2.5em;
        font-weight: 500;
        color: #C5538F;
        margin: 0;
        padding: 0;
    }

    h5 {
        font-size: 0.9em;
        font-weight: 300;
        margin: 8px 0 0 0;
        padding: 0;
    }

    @media only screen and (max-width: 768px) {
        flex-direction: column;
        gap: 3vw;

        h1 {
            font-size: 1.8em;
        }

        h5 {
            font-size: 0.875em;
        }
    }
`

const ProgressHeaderIndex = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: #FCFCFC;
    padding: 16px 40px 16px 40px;
    gap: 12px;
    border-radius: 16px;
    box-sizing: border-box;

    @media only screen and (max-width: 768px) {
        width: 100%;
        padding: 3vw;
    }
`

const ProgressHeaderTab = styled.div`
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 0 0 12px;
    height: 34px;
    width: 140px;

    span {
        font-size: 0.875em;
    }

    @media only screen and (max-width: 768px) {
        span {
            font-size: 0.675em;
        }
    }
`

const ProgressBar = styled.div`
    display: flex;
    height: 30px;
    width: 100%;
    background-color: #303445;
    overflow: hidden;

    div {
        height: 100%;
        background-color: #78D3F7;
        flex-direction: column;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        box-sizing: border-box;
        padding: 0 2px 0 0;
    }

    span {
        font-size: 0.875em;
        font-weight: 500;
        color: #303445;
        align-self: flex-end;
    }

    @media only screen and (max-width: 768px) {
        span {
            font-size: 0.675em;
        }
    }
`

const titleMap: { [key: number]: string } = {
    0: 'Your Hair, Your Goals',
    1: 'Your Hair, Your Goals',
    2: 'Upload Pictures',
    3: 'Extra Information',
    4: 'Medical Conditions',
    5: 'Lifestyle',
    6: 'Hair Care',
    7: 'A little about you',
};

const subTitleMap: { [key: number]: string } = {
    0: 'The following questions will help us to get a picture of you!',
    1: 'The following questions will help us to get a picture of you!',
    2: 'Your x3 images should be clear & high quality !',
    3: 'Please share any relevant information and observations about your hair E.g. Multiple hair types, Problems,  Reactions, Behaviour, treatment history, etc',
    4: 'The following questions will help us to get a picture of you!',
    5: 'The following questions will help us to get a picture of you!',
    6: 'The following questions will help us to get a picture of you!',
    7: 'The following questions will help us to get a picture of you!',
}

const ProgressHeader = ({
    index = 0,
    answers = {},
    totalQuestions = 0,
}) => {

    const title = useMemo(() => titleMap[index], [index]);
    const subTitle = useMemo(() => subTitleMap[index], [index]);

    const progress = useMemo(() => {
        const answerEntries = Object.values(answers).length;

        const percentage = (answerEntries / totalQuestions) * 100;
        return percentage;
    }, [answers, totalQuestions]);

    return (
        <Root>
            <Column>
                <h1>{title}</h1>
                <h5>{subTitle}</h5>
            </Column>

            <ProgressHeaderIndex>
                <Row>
                    <ProgressHeaderTab style={{ backgroundColor: index <= 3 ? colors.BLUE.primary : undefined }}>
                        <span>Hair Details</span>
                    </ProgressHeaderTab>
                    <ProgressHeaderTab style={{ backgroundColor: index === 4 ? colors.BLUE.primary : undefined }}>
                        <span>Medical</span>
                    </ProgressHeaderTab>
                    <ProgressHeaderTab style={{ backgroundColor: index === 5 ? colors.BLUE.primary : undefined }}>
                        <span>Lifestyle</span>
                    </ProgressHeaderTab>
                    <ProgressHeaderTab style={{ backgroundColor: index >= 6 ? colors.BLUE.primary : undefined }}>
                        <span>Hair Care</span>
                    </ProgressHeaderTab>
                </Row>
                <ProgressBar>
                    <div style={{ width: `${progress}%` }}>
                        <span>{Math.round(progress)}%</span>
                    </div>
                </ProgressBar>
            </ProgressHeaderIndex>

        </Root>
    )
}

export default ProgressHeader;
