import styled from "styled-components";
import { HomeSection } from "./style";
import BackgroundGradient from "../../components/BackgroundGradient";
import Row from "../../components/Row";
import Button from "../../components/Button";

const Root = styled(HomeSection)`
    justify-content: center;

    button {
        width: 200px;
        background-color: #FE91EA;
        align-self: center;
        margin-top: 40px;
    }
`

const FlexBox = styled.div`
    display: flex;
    height: 100%;
    justify-content: center;
    flex: 1;
    flex-direction: column;
    color: #FFFFFF;

    h1 {
        font-size: 2.5em;
        font-weight: 500;
        margin: 0 0 20px 0;
    }

    p {

    }

    @media only screen and (max-width: 768px) {
        h1 {
            text-align: center;
            font-size: 2em;
        }

        p {
            text-align: center;
            font-size: 0.875em;
        }
    }
`

const Image = styled.img`
    aspect-ratio: 1.7;
    width: 40vw;
    border-radius: 24px;
    z-index: 1;

    @media only screen and (max-width: 768px) {
        width: 100%;
    }
`

const Discover = () => {
    const isMobile = window.innerWidth < 768;
    return (
        <Root>
            <BackgroundGradient />
            <Row style={{ gap: '7vw', flexDirection: isMobile ? 'column' : 'row' }}>
                <Image src={require('../../assets/images/discover-your-hair.png')} alt="" />
                <FlexBox>
                    <h1>Discover your hair</h1>
                    <p>By using a quite a few questions, we evaluate a range of factors around your hair, health and lifestyle to discover exactly what your hair needs.</p>
                    <p>Over 100+ factors are analysed to create your ideal product recommendation.</p>
                    <p>We believe developing game changing cosmetic technology will unlock the secrets to happy hair days for the underrepresented and empower communities globally.</p>
                </FlexBox>
            </Row>
            <Button>How it Works</Button>
        </Root>
    )
}

export default Discover;
