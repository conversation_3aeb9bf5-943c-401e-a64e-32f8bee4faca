import styled from "styled-components";
import { HomeSection } from "./style";
import ImageWithFade from "../../components/ImageWithFade";
import Button from "../../components/Button";

const Root = styled(HomeSection)`
    justify-content: center;
    align-items: center;
`

const TransparentCard = styled.div`
    display: flex;
    flex-direction: column;
    width: 50%;
    padding: 20px 40px 20px 40px;
    box-sizing: border-box;
    align-items: center;
    background: rgba(252, 252, 252, 0.1);
    border-radius: 15px;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: #FFFFFF;
    text-align: center;

    h1 {
        font-size: 2.5em;
        font-weight: 500;
        margin: 0 0 10px 0;
    }

    p {
        letter-spacing: 0.25px;
    }

    button {
        margin-top: 20px;
        background-color: #FE91EA;
    }

    @media only screen and (max-width: 768px) {
        width: 100%;
        padding: 5vw;

        h1 {
            font-size: 2em;
        }

        p {
            font-size: 0.875em;
        }
    }
`;

const Landing = () => {
    return (
        <Root>
            <ImageWithFade src={require('../../assets/images/cosmetrics-landing.png')} alt='' />

            <TransparentCard>
                <h1>Learn to love your hair!</h1>
                <p>Our technology and expert knowledge matches you to products that actually work with your hair, not against it.</p>
                <p>Learn more about your own hair in 20 min and empower your hair care journey!</p>

                <Button onClick={() => { window.location.href = '/quiz' }}>Start Quiz!</Button>
            </TransparentCard>
        </Root>
    )
}

export default Landing;
