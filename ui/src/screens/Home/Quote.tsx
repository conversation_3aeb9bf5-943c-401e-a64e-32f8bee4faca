import styled from "styled-components";
import BackgroundGradient from "../../components/BackgroundGradient";
import { HomeSection } from "./style";

const Root = styled(HomeSection)`
    padding: 7% 10% 7% 10%;
    justify-content: center;
    color: #FFFFFF;

    p {
        font-size: 2.5em;
        font-weight: 300;
        z-index: 1;
    }

    span {
        z-index: 1;
    }

    @media only screen and (max-width: 768px) {
        p {
            font-size: 1.8em;
        }
    }
`

const BoldText = styled.span`
    font-weight: 600;
`

const Quote = () => {
    return (
        <Root>
            <BackgroundGradient />
            <p>
                ‘ Unfortunately, <BoldText>everyone’s skin is different</BoldText>, and while one person might be sensitive to an ingredient, the next person may be fine with it. So, it’s a case of <BoldText>trial and error to rule out the products your scalp doesn’t like!</BoldText> ’
            </p>
            <BoldText>P&G group - Head & Shoulders, Pantene , Aussie Hair, Herbal Essences 2021</BoldText>
        </Root>
    )
}

export default Quote;
