import styled from "styled-components";
import Footer from "../../components/Footer";
import Header from "../../components/Header";
import Landing from "./Landing";
import Recommendations from "./Recommendations";
import Discover from "./Discover";
import Analyse from "./Analyse";
import Quote from "./Quote";
import Final from "./Final";

const Root = styled.div`
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background: linear-gradient(to bottom, #A9AFEA 0%, #DE90C4 100%);
`

const Home = () => {
    return (
        <Root>
            <Landing />
            <Recommendations />
            <Discover />
            <Analyse />
            <Quote />
            <Final />

            <Footer />

            <Header />
        </Root>
    )
}

export default Home;