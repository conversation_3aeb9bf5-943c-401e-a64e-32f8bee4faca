import styled from "styled-components";
import { HomeSection } from "./style";
import Button from "../../components/Button";
import PinkBubble from '../../assets/images/pink-bubble.svg';
import Quiz from '../../assets/images/take-quiz.svg';
import UploadPhotos from '../../assets/images/upload-photos.svg';
import HairSuggestions from '../../assets/images/hair-suggestions.svg';
import Column from "../../components/Column";

const Root = styled(HomeSection)`
    display: flex;
    justify-content: center;
    flex-direction: column;
    color: #FFFFFF;

    div {
        display: flex;
    }

    button {
        align-self: center;
        background-color: #78D3F7;
    }

    h1 {
        font-size: 2.5em;
        font-weight: 500;
    }

    img {
        height: 120px;
        width: 120px;
        border-radius: 200px;
        align-self: center;
    }

    span {
        font-weight: 600;
        color: #303445;
        margin: 20px 0 0 0;
        text-align: center;
    }

    @media only screen and (max-width: 768px) {
        justify-content: space-between;

        h1 {
            font-size: 2em;
            text-align: center;
        }

        P {
            text-align: center;
            font-size: 0.875em;
        }
    }
`

const FlexRow = styled.div`
    flex-direction: row;
    flex: 1;
`

const FlexBox = styled.div`
    flex-direction: column;
    flex: 1;
`

const PinkBubbleImg = styled.img`
    position: absolute;
    height: 421px !important;
    width: 550px !important;
    border-radius: 0 !important;
    resize: both;
`

const Recommendations = () => {
    return (
        <Root>
            <FlexRow style={{ gap: 40 }}>
                <FlexBox>
                    <h1>Get Recommendations</h1>
                    <p>Your curly or textured hair is one of a kind and requires an individual approach for daily maintenance.</p>
                    <p>Our process uses artificial Intelligence technology to provide you with advanced hair, health and lifestyle insights.</p>
                    <p> You will learn about your hair and its distinct traits and receive instant product recommendations based on your unique hair type.</p>
                    <p>Uncover great products that work for your hair now!</p>
                </FlexBox>
                {window.innerWidth > 768 &&
                    <FlexBox>
                        <PinkBubbleImg src={PinkBubble} alt="" />
                        <FlexRow style={{ zIndex: 1, alignItems: 'center' }}>
                            <Column style={{ flex: 1 }}>
                                <img src={Quiz} alt="" />
                                <span>Take the Quiz</span>
                            </Column>
                            <Column style={{ flex: 1 }}>
                                <img src={UploadPhotos} alt="" />
                                <span>Upload Pictures</span>
                            </Column>
                            <Column style={{ flex: 1 }}>
                                <img src={HairSuggestions} alt="" />
                                <span>Get Hair Suggestions</span>
                            </Column>
                        </FlexRow>
                    </FlexBox>
                }
            </FlexRow>
            <Button>Unlock Insights</Button>
        </Root>
    )
}

export default Recommendations;
