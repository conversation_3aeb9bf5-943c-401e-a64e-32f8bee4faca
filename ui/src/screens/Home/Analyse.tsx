import styled from "styled-components";
import { HomeSection } from "./style";
import Button from "../../components/Button";

const Root = styled(HomeSection)`
    justify-content: center;
    align-items: center;

    div {
        display: flex;
        flex-direction: row;
        flex: 1;

        div {
            display: flex;
            height: 100%;
            justify-content: center;
            flex: 1;
            flex-direction: column;
            color: #FFFFFF;
        }
    }

    h1 {
        font-size: 2.5em;
        font-weight: 500;
        margin: 0 0 20px 0;
    }

    p {
        margin: 30px 0 0 0;
    }

    ul {
        margin: 0;
    }

    button {
        background-color: #78D3F7;
    }

    img {
        width: 700px;
    }

    @media only screen and (max-width: 768px) {

        h1 {
            text-align: center;
            font-size: 2em;
            color: #303445;
        }

        img {
            width: 95%;
            left: 2.5%;
            right: 2.5%;
            position: absolute;
            top: 5%;
        }

        p {
            font-size: 0.875em;
        }

        li {
            font-size: 0.875em;
        }
    }
`

const Analyse = () => {
    return (
        <Root>
            <div style={{ gap: 80 }}>
                <div>
                    <h1>Analyse & Recommend</h1>
                    <p>Advanced match making - Our technology will match you with top quality hair products based on your individual hair type, hair goals, and lifestyle information.</p>
                    <p>Product recommendations currently include:</p>
                    <ul>
                        <li>Shampoo</li>
                        <li>Conditioner</li>
                        <li>Suggested add ons - (e.g) oils, sealants etc</li>
                    </ul>
                </div>
                <img src={require('../../assets/images/analyse-and-recommend.png')} alt="" />
            </div>
            <Button>How it Works</Button>
        </Root>
    )
}

export default Analyse;
