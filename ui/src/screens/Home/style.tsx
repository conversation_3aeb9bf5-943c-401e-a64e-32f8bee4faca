import styled from "styled-components";

export const HomeSection = styled.div`
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    position: relative;
    height: calc(100vh - 80px);
    padding: 7% 5% 7% 5%;
    overflow: hidden;

    h1 {
        z-index: 1;
    }

    p {
        z-index: 1;
    }

    button {
        width: 200px;
        z-index: 1;
    }

    @media only screen and (max-width: 768px) {
        padding: 5.5% 5.5% 10% 5.5%;
    }
`