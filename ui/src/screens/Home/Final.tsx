import styled from "styled-components";
import { HomeSection } from "./style";
import Button from "../../components/Button";

const Root = styled(HomeSection)`
    justify-content: center;
    align-items: center;
    color: #FFFFFF;

    h1 {
        font-size: 3em;
        font-weight: 500;
    }

    div {
        display: flex;
        flex-direction: row;
        width: 50%;

        div {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
    }

    img {
        height: 140px;
        aspect-ratio: 1;
        border-radius: 200px;
    }

    span {
        font-weight: 600;
        margin: 30px 0 0 0;
    }

    p {
        margin: 50px 0 40px 0;
        font-size: 1.25em;
    }

    button {
        background-color: #78D3F7;
    }

    @media only screen and (max-width: 768px) {
        h1 {
            text-align: center;
            font-size: 2em;
            margin: 0 0 20vw 0;
        }

        span {
            font-size: 0.875em;
            text-align: center;
        }

        p {
            font-size: 0.875em;
            text-align: center;
        }

        img {
            height: 90px;
        }

        div {
            width: 100%;

            div {
                flex: 1;
            }
        }
    }
`

const Final = () => {
    return (
        <Root>
            <h1>Improving Hair Care Forever!</h1>

            <div style={{ }}>
                <div>
                    <img src={require('../../assets/images/set-goals.png')} alt="" />
                    <span>Set Goals</span>
                </div>
                <div>
                    <img src={require('../../assets/images/untangle-issues.png')} alt="" />
                    <span>Untangle Issues</span>
                </div>
                <div>
                    <img src={require('../../assets/images/advice-and-insights.png')} alt="" />
                    <span>Advice & Insights</span>
                </div>
            </div>

            <p>
                Our technology works with you to continually improve your hair and your products.
            </p>

            <Button>Unlock Insights!</Button>
        </Root>
    )
}

export default Final;
