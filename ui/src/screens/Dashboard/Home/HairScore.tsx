import { RowSpaced } from '../../../components/Row';
import ProgressBar from '../../../components/ProgressBar';
import styled from 'styled-components';

const Text = styled.span`
    font-size: 1em;
    font-weight: 400;
    width: 100px;

    @media only screen and (max-width: 768px) {
        font-size: 0.875em;
    }
`;

interface Props {
    label: string;
    value: number;
}

const HairScore: React.FC<Props> = ({
    label,
    value,
}) => {
    return (
        <RowSpaced>
            <Text>{label}</Text>
            <ProgressBar value={value / 10} />
            <Text style={{ textAlign: 'right' }}>{value}/100</Text>
        </RowSpaced>
    );
};

export default HairScore;
