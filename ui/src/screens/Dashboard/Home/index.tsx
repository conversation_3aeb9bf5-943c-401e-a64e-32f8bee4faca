import styled from "styled-components";
import Row from "../../../components/Row";
import colors from "../../../style/colors";
import Dashboard from "../../../routes/Dashboard";
import Column from "../../../components/Column";
import HairScore from "./HairScore";
import { NavArrowDown } from "iconoir-react";
import Button from "../../../components/Button";
import useScale from "../../../hooks/useScale";

const Root = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
    box-sizing: border-box;
    padding: 3vw 5vw 3vw 5vw;
    color: #303445;
    height: 100%;
    width: 100%;
    gap: 2vw;

    h1 {
        font-size: 2em;
        font-weight: 500;
        margin: 0;
        padding: 0;
    }

    p {
        font-size: 0.875em;
        margin: 0;
        padding: 0;
    }

    button {
        width: fit-content;
        background-color: transparent;
    }

    @media only screen and (max-width: 768px) {
        gap: 4vw;

        h1 {
            font-size: 1.8em;
        }

        p {
            font-size: 0.775em;
        }
    }
`

const Card = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 20px 24px 20px 24px;
    border-radius: 12px;
    min-height: 21vw;
    width: 100%;
    box-sizing: border-box;

    h1 {
        font-size: 1.05em;
        font-weight: 500;
    }

    h2 {
        font-size: 0.95em;
        font-weight: 600;
        margin: 0;
        padding: 0;
    }

    p {
        margin: 12px 0 0 0;
    }

    @media only screen and (max-width: 768px) {
        h2 {
            font-size: 0.875em;
        }

        p {
            font-size: 0.675em;
        }
    }
`

const CircleImage = styled.img`
    display: flex;
    height: 60px;
    aspect-ratio: 1;
    border-radius: 100px;
`

const RectangleImage = styled.img`
    display: flex;
    width: 170px;
    aspect-ratio: 1.8;
    border-radius: 8px;

    @media only screen and (max-width: 768px) {
        height: 19.125vw;
        width: 34vw;
    }
`

const WhiteBox = styled.div`
    display: flex;
    flex-direction: row;
    padding: 20px;
    gap: 24px;
    border-radius: 8px;
    flex: 1;
    width: 100%;
    background-color: #FFFFFF;
    box-sizing: border-box;

    button {
        background-color: transparent;
        margin: 8px 0 0 0;
        padding: 0;
        border: none;
        outline: none;
        text-decoration: underline;
        align-self: flex-start;
        cursor: pointer;
    }
`

const GridRow = styled.div`
    display: flex;
    flex-direction: row;
    flex: 1;
    position: relative;
    height: 100%;
    margin: 0;
    padding: 0;
    box-sizing: border-box;

    gap: 4vw;

    @media only screen and (max-width: 768px) {
        flex-direction: column;
    }
`

const TipBar = styled.div`
    display: flex;
    flex-direction: row;
    position: relative;
    height: 68px;
    width: 100%;
    box-sizing: border-box;
    background-color: #303445;
    border-radius: 12px;
    padding: 0 20px 0 20px;
    justify-content: space-between;
    align-items: center;

    h1 {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 0.875em;
        font-weight: 600;
        color: #FFFFFF;
        margin: 0;
        padding: 0;
    }

    div {
        height: 100%;
        div {
            display: flex;
            height: 58%;
            padding: 0 12px 0 12px;
            margin: 0;
            background-color: #E7F7FB;
            border-radius: 8px;
            justify-content: center;
            align-items: center;

            span {
                color: #303445;
                font-weight: 400;
                font-size: 0.875em;
            }
        }
    }
`

const RowSpaced = styled(Row)`
    justify-content: space-between;

    @media only screen and (max-width: 768px) {
        gap: 5vw;
        flex-direction: column;
    }
`

const Home = () => {

    const { isMobile } = useScale();

    return (
        <Dashboard>
            <Root>
                <Column>
                    <h1>Welcome Name!</h1>
                    <p>Find out insights about how to care for your hair below. These are based on the diagnostic test taken on 14/03/2024</p>
                </Column>

                <GridRow>
                    <Card style={{ backgroundColor: colors.BLUE.pastel }}>
                        <h1>Hair Score</h1>

                        <Column style={{ marginTop: 24, gap: 12 }}>
                            <HairScore label="Dryness" value={97} />
                            <HairScore label="Damage" value={13} />
                            <HairScore label="Sensitivity" value={51} />
                            <HairScore label="Sebum Oil" value={27} />
                            <HairScore label="Dry Scalp" value={82} />
                            <HairScore label="Flakes" value={17} />
                        </Column>
                    </Card>
                    <Card style={{ backgroundColor: colors.PINK.pastel }}>
                        <h1>Routines</h1>

                        <Row style={{ gap: 24, alignItems: 'unset', marginTop: 16 }}>
                            <CircleImage src={require('../../../assets/images/washing.png')} alt="" />
                            <Column>
                                <h2>Washing</h2>
                                <p>
                                    Dial down the temperature of your shower to lukewarm water when you apply shampoo and to cool water when you apply conditioner this can significantly reduce breakage and split ends.
                                </p>
                                <p>
                                    Tip:
                                    Weekly cleaning and conditioning is a critical step in healthy hair maintenance.
                                </p>
                            </Column>
                        </Row>
                    </Card>
                </GridRow>

                <Card style={{ backgroundColor: colors.ORANGE.pastel, gap: 24 }}>
                    <h1>Hair ID</h1>

                    <RowSpaced>
                        <Row style={{ alignItems: 'unset', gap: 16, flex: 1 }}>
                            <RectangleImage src={require('../../../assets/images/type-4c-diagram.png')} alt="" />
                            <Column>
                                <h2>Type: {'4C'}</h2>
                                <p>
                                    Tight, springy 'Z' shaped ringlets, prone to shrinkage, can shrink up to 75% of their length.
                                </p>
                            </Column>
                        </Row>
                        <Row style={{ alignItems: 'unset', gap: 16, flex: 1 }}>
                            <RectangleImage src={require('../../../assets/images/density-low.png')} alt="" />
                            <Column>
                                <h2>Density: {'Low'}</h2>
                                <p>
                                    Fewer strands per inch, may seem thin, sparse, lack volume, scalp more visible.
                                </p>
                            </Column>
                        </Row>
                    </RowSpaced>
                    <RowSpaced>
                        <Row style={{ alignItems: 'unset', gap: 16, flex: 1 }}>
                            <RectangleImage src={require('../../../assets/images/texture-thin.png')} alt="" />
                            <Column>
                                <h2>Texture: {'Thin/Fine'}</h2>
                                <p>
                                    Lightweight strands, may lack volume, prone to breakage, needs special care.
                                </p>
                            </Column>
                        </Row>
                        <Row style={{ alignItems: 'unset', gap: 16, flex: 1 }}>
                            <RectangleImage src={require('../../../assets/images/porosity-high.png')} alt="" />
                            <Column>
                                <h2>Porosity: {'High'}</h2>
                                <p>
                                    Cuticles raised, absorbs moisture quickly, prone to dryness, frizz, breakage.
                                </p>
                            </Column>
                        </Row>
                    </RowSpaced>
                </Card>

                <Card style={{ backgroundColor: colors.PURPLE.pastel, gap: 24 }}>
                    <h1>Hair Objectives</h1>

                    <GridRow style={{ gap: 24 }}>
                        <WhiteBox>
                            <CircleImage src={require('../../../assets/images/split-ends.png')} alt="" />
                            <Column>
                                <h2>Issues</h2>
                                <button>Split ends / hair breakage</button>
                                <p>
                                    Split ends are caused by a number of reasons including overuse of heat, excessive styling, chemical treatments, lack of sealing those ends and not trimming when it's time.
                                </p>
                                <p>
                                    They cause our hair to look frizzy and damaged.
                                </p>
                            </Column>
                        </WhiteBox>
                        <WhiteBox>
                            <CircleImage src={require('../../../assets/images/goals.png')} alt="" />
                            <Column>
                                <h2>Goals</h2>
                                <button>Ultra clean hair & scalp</button>
                                <p>
                                    Curls & coils are often stuck under layers of dirt, oil, product and mineral build up, rather than lacking protein or moisture they maybe in need of a deep clean that allows for fresh start.
                                </p>
                                <p>
                                    Signs that it's time for a clean slate include your hair looking less curly, less shiny, less bouncy. Your Holy Grail products stop working or even dandruff-like particles start to appear on your hair and scalp.
                                </p>
                            </Column>
                        </WhiteBox>
                        <WhiteBox>
                            <CircleImage src={require('../../../assets/images/split-ends.png')} alt="" />
                            <Column>
                                <h2>Issues</h2>
                                <p>
                                    Avoid products with high resin contents, heavy oils and non-water soluble silicones, opting instead for water soluble ingredients, proteins, amino acids, essential oils and moisturisers.
                                </p>
                                <p>
                                    To make your hair stronger you should use products with better / compatible ingredients.
                                </p>
                            </Column>
                        </WhiteBox>
                    </GridRow>
                </Card>

                <Card style={{ backgroundColor: colors.BLUE.pastel, gap: 24 }}>
                    <h1>Scalp</h1>

                    <GridRow style={{ gap: 24 }}>
                        <WhiteBox>
                            <CircleImage src={require('../../../assets/images/sebum-production.png')} alt="" />
                            <Column>
                                <h2>Sebum Production</h2>
                                <button>Your Rating: {'Oily'}</button>
                                <p>
                                    Your scalp forms small amounts of sebum (scalp oil) to envelop the hair and protect the scalp from external influences.
                                </p>
                            </Column>
                        </WhiteBox>
                        <WhiteBox>
                            <CircleImage src={require('../../../assets/images/scalp-dryness.png')} alt="" />
                            <Column>
                                <h2>Scalp Dryness</h2>
                                <button>Your Rating: {'Very Dry'}</button>
                                <p>
                                    A dry scalp can be caused by eczema, dry weather, water, temperature, and using products containing alcohol. A dry scalp means you aren’t producing enough sebum to keep your scalp moisturised.
                                </p>
                            </Column>
                        </WhiteBox>
                    </GridRow>
                </Card>

                {
                    !isMobile &&
                    <TipBar>
                        <h1>Cosmetric Care - Improve your hair score with these tips! <NavArrowDown /></h1>

                        <Row style={{ gap: 14 }}>
                            <div>
                                <span>Environmental</span>
                            </div>
                            <div>
                                <span>Diet & Nutrition</span>
                            </div>
                            <div>
                                <span>Health & Fitness</span>
                            </div>
                            <div>
                                <span>Hair Condition</span>
                            </div>
                        </Row>
                    </TipBar>
                }

                <Button>Add your hair care products</Button>

            </Root>
        </Dashboard>
    )
}

export default Home;