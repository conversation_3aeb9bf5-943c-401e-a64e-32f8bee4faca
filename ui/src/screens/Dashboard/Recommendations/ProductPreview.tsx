import React from "react";
import styled from "styled-components";
import { RowSpaced } from "../../../components/Row";
import Button from "../../../components/Button";

const Root = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
    width: 26vw;
    box-sizing: border-box;
    color: #303445;

    span {
        font-size: 0.875em;
        font-weight: 400;
        margin: 16px 0 0 0;
        padding: 0;
    }

    button {
        background-color: transparent;
        margin: 18px 0 0 0;
        width: auto !important;
    }

    @media only screen and (max-width: 768px) {
        width: 100%;
    }
`;

const WhiteBox = styled.div`
    display: flex;
    position: relative;
    justify-content: center;
    align-items: center;
    width: 100%;
    aspect-ratio: 0.8;
    background-color: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);

    img {
        height: 85%;
        max-width: 75%;
    }
`

const BoldText = styled.span`
    width: 40% !important;
    font-weight: 600 !important;
    text-align: right;
`;

const FrequencyBadge = styled.div`
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #E7F7FB;
    border-radius: 8px;
    height: 38px;
    padding: 0 12px 0 12px;
    position: absolute;
    top: 16px;
    right: 16px;

    span {
        margin: 0;
        padding: 0;
    }
`

interface Props extends Product { }

const ProductPreview: React.FC<Props> = ({
    name,
    description,
    price,
    image,
    frequency,
}) => {
    return (
        <Root>
            <WhiteBox>
                <FrequencyBadge>
                    <span>{frequency} Use</span>
                </FrequencyBadge>
                <img src={image} alt="" />
            </WhiteBox>
            <RowSpaced style={{ alignItems: 'start' }}>
                <span>{name}</span>
                <BoldText>{price}</BoldText>
            </RowSpaced>
            <span>{description}</span>

            <Button>Purchase from retailer</Button>
        </Root>
    )
};

export default ProductPreview;