import styled from "styled-components";
import Dashboard from "../../../routes/Dashboard";
import Column from "../../../components/Column";
import Button from "../../../components/Button";
import { ShoppingBag, Star } from "iconoir-react";
import colors from "../../../style/colors";
import { useMemo, useState } from "react";
import ProductPreview from "./ProductPreview";

const Root = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
    box-sizing: border-box;
    padding: 3vw 5vw 3vw 5vw;
    color: #303445;
    min-height: 100%;
    width: 100%;
    background-color: #FCFCFC;
    gap: 3vw;

    h1 {
        font-size: 2em;
        font-weight: 500;
        margin: 0;
        padding: 0;
    }

    p {
        font-size: 0.875em;
        margin: 0;
        padding: 0;
    }

    button {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        position: relative;
        gap: 8px;
        width: 200px;
    }

    header {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    @media only screen and (max-width: 768px) {
        h1 {
            font-size: 1.8em;
        }

        p {
            font-size: 0.775em;
        }

        button {
            width: 100%;
        }

        header {
            flex-direction: column;
            gap: 3vw;
        }
    }
`

const ListContainer = styled.div`
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 0 10vw 0 0;
    gap: 5vw;
    box-sizing: border-box;

    @media only screen and (max-width: 768px) {
        padding: 0;
    }
`

const ListRow = styled.div`
    display: flex;
    flex-direction: row;
    position: relative;
    width: 100%;
    gap: 5vw;
    box-sizing: border-box;

    @media only screen and (max-width: 768px) {
        flex-direction: column;
        align-items: center;
    }
`

const Card = styled.div`
    display: flex;
    flex-direction: column;
    padding: 20px 24px 20px 24px;
    border-radius: 12px;
    width: 100%;
    box-sizing: border-box;

    h1 {
        font-size: 0.95em;
        font-weight: 600;
    }

    p {
        font-size: 0.875em;
        margin: 4px 0 0 0;
        padding: 0;
    }

    span {
        font-size: 0.75em;
        font-weight: 300;
        margin: 12px 0 0 0;
    }
`

const Recommendations = () => {

    const [products] = useState<Product[]>([
        {
            image: require('../../../assets/images/product-1.png'),
            name: "Tgin Moisture Rich Sulfate Free Shampoo For Natural Hair",
            description: "Enriched with coconut and amla oils. Gently cleanses, reduces breakage, boosts moisture for soft, smooth, healthy hair. No harsh detergents or harmful ingredients.",
            price: "£11-£15",
            frequency: "Weekly"
        },
        {
            image: require('../../../assets/images/product-2.png'),
            name: "Tgin Moisture Rich Sulfate Free Shampoo For Natural Hair",
            description: "Enriched with coconut and amla oils. Gently cleanses, reduces breakage, boosts moisture for soft, smooth, healthy hair. No harsh detergents or harmful ingredients.",
            price: "£11-£15",
            frequency: "Weekly"
        },
        {
            image: require('../../../assets/images/product-3.png'),
            name: "Tgin Moisture Rich Sulfate Free Shampoo For Natural Hair",
            description: "Enriched with coconut and amla oils. Gently cleanses, reduces breakage, boosts moisture for soft, smooth, healthy hair. No harsh detergents or harmful ingredients.",
            price: "£11-£15",
            frequency: "Weekly"
        },
        {
            image: require('../../../assets/images/product-4.png'),
            name: "Tgin Moisture Rich Sulfate Free Shampoo For Natural Hair",
            description: "Enriched with coconut and amla oils. Gently cleanses, reduces breakage, boosts moisture for soft, smooth, healthy hair. No harsh detergents or harmful ingredients.",
            price: "£11-£15",
            frequency: "Weekly"
        },
    ]);

    const combineArrayIntoGroups = <T,>(array: T[], numGroup: number) => {
        const pairs = [];
        const groupLength = numGroup || 2;
        for (let i = 0; i < array.length; i += groupLength) {
            pairs.push(array.slice(i, i + groupLength));
        }
        return pairs;
    };

    const productList = useMemo(() => combineArrayIntoGroups(products, 2), [products])

    return (
        <Dashboard>
            <Root>
                <header>
                    <Column>
                        <h1>Your recommended products</h1>
                        <p>These products have been recommended based on your hair diagnostic, learn more.</p>
                    </Column>
                    <Button style={{ backgroundColor: colors.BLUE.primary, }}><ShoppingBag /> Purchase all</Button>
                </header>
                {/* <RowSpaced style={{ flexDirection: isMobile ? 'column' : 'row' }}>
                    <Column>
                        <h1>Your recommended products</h1>
                        <p>These products have been recommended based on your hair diagnostic, learn more.</p>
                    </Column>
                    <Button style={{ backgroundColor: colors.BLUE.primary, }}><ShoppingBag /> Purchase all</Button>
                </RowSpaced> */}

                <ListContainer>
                    {productList.map((item, index) => {
                        return (
                            <ListRow key={index}>
                                {item.map((rowItem, index) => {
                                    return (
                                        <ProductPreview {...rowItem} />
                                    )
                                })}
                            </ListRow>
                        )
                    })}
                </ListContainer>

                <Card style={{ backgroundColor: colors.PURPLE.pastel, gap: 24 }}>
                    <Column>
                        <h1>Why these products?</h1>
                        <p>The products chosen have been analysed and found to contain the best formulations to meet your stated hair goal and or address any issues you/we have identified.</p>
                    </Column>
                    <Column>
                        <h1>Expertise & Science</h1>
                        <p>Our expert led diagnostic evaluation process reveals your recommended products have been assessed to complement your Hair ID profile.</p>
                    </Column>
                    <Column>
                        <h1>Checking for changes</h1>
                        <p>Change of season, living in a new location, hormonal changes, stress, diet etc affect your hair! We recommend scheduling 'Change check-in' updates every 2-3 months as new changes may warrant updated recommendations.</p>
                    </Column>

                    <span>Disclaimer: Cosmetrics Ai is not affiliated to any cosmetics brand or company and does not receive any form of endorsement to recommend hair care products.We are proudly Independent & Impartial forever!</span>
                </Card>

                <Button style={{ backgroundColor: colors.BLUE.primary, width: 250 }}><Star /> Rate Recommendation</Button>

            </Root>
        </Dashboard>
    )
}

export default Recommendations;