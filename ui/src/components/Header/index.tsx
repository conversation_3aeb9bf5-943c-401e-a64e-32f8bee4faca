import styled from "styled-components";
import CosmetricsLogo from '../../assets/images/cosmetrics-logo.svg';
import Button from "../Button";
import colors from "../../style/colors";
import { useMemo } from "react";

const Root = styled.header`
    position: fixed;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    height: 80px;
    width: 100vw;
    padding: 0 5% 0 5%;
    background-color: #FCFCFC;
    z-index: 10001;

    div {
        display: flex;
        position: relative;
        flex: 1;
        flex-direction: row;
        align-items: center;
    }

    a {
        text-decoration: none;
        underline: none;
        font-weight: 300;
        color: #303445;
    }

    @media only screen and (max-width: 768px) {
        padding: 0 3% 0 3%;

        a {
            font-size: 0.8em;
        }

        button {
            font-size: 0.75em;
        }
    }
`

const Header = () => {

    const isMobile = useMemo(() => {
        return window.innerWidth < 768;
    }, [])

    return (
        <Root>

            <div>
                <img src={CosmetricsLogo} alt="" />
            </div>

            {
                !isMobile &&
                <div style={{ justifyContent: isMobile ? 'flex-start' : 'center', gap: '2.25vw' }}>
                    <a href="/">Home</a>
                    <a href="/">How it works</a>
                    <a href="/">Hair ID</a>
                    <a href="/">About us</a>
                </div>
            }
            <div style={{ justifyContent: 'flex-end', gap: '1.5vw', flex: isMobile ? 0 : 1 }}>
                {
                    window.innerWidth > 768 &&
                    <Button style={{ width: 200, backgroundColor: colors.PINK.primary }} onClick={() => { window.location.href = '/quiz' }}>Start Quiz</Button>
                }
                <Button onClick={() => { window.location.href = '/login' }}>Login</Button>
            </div>
        </Root>
    )
}

export default Header;
