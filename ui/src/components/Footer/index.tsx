import styled from "styled-components";

const Root = styled.div`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 80px;
    width: 100vw;
    box-sizing: border-box;
    background-color: #e7f7fb;
    padding: 0 2.5% 0 2.5%;
    color: #303445;

    a {
        color: #303445;
        text-decoration-line: none;
    }

    div {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 20px;
    }

    button {
        display: flex;
        flex-direction: row;
        background-color: transparent;
        border: none;
        outline: none;
        cursor: pointer;
    }

    span {
        font-size: 1em;
    }

    @media only screen and (max-width: 768px) {
        span {
            font-size: 0.875em;
        }
    }
`

const Footer = () => {
    const currentYear = new Date().getFullYear();
    return (
        <Root>
            <span>Cosmetrics AI 2021 - {currentYear}</span>

            <div>
                <button onClick={() => {}}>Twitter</button>
                <button onClick={() => {}}>Facebook</button>
            </div>
        </Root>
    )
};

export default Footer;