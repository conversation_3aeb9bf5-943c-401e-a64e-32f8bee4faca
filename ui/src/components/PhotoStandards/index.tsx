import styled from "styled-components";
import { RowSpaced } from "../Row";
import { Check, Xmark } from "iconoir-react";
import { useMemo, useState } from "react";
import { combineArrayIntoGroups } from "../../util/functions";
import Column from "../Column";

const GridBox = styled.div`
    display: flex;
    flex-direction: column;
    flex: 1;

    button {
        width: 200px;
        align-self: flex-end;
        vertical-align: bottom;
        background-color: #78D3F7;
    }
`

const ImageBox = styled(GridBox)`
    background-color: #F7F7FB;
    border-radius: 24px;
    padding: 3vw 1.8vw 3vw 1.8vw;
    justify-content: space-between;
    gap: 3vw;

    h3 {
        margin: 0;
        font-weight: 400;
        font-size: 1.125em;
    }

    @media only screen and (max-width: 768px) {
        padding: 5vw;

        h3 {
            font-size: 0.875em;
        }
    }
`

const ImageStatus = styled.div`
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    height: 32px;
    width: 60%;
    background-color: #FCFCFC;
    padding: 0 4px 0 8px;
    z-index: 1;

    span {
        font-size: 0.75em;
    }
`

const ImageContainer = styled.div`
    display: flex;
    position: relative;
    justify-content: flex-end;
    align-items: flex-end;
    background-color: salmon;
    width: 162px;
    aspect-ratio: 1;
    border-radius: 4px;
    overflow: hidden;

    img {
        position: absolute;
        height: 100%;
        width: 100%;
        margin: 0;
    }

    @media only screen and (max-width: 768px) {

    }
`

const Image = ({ image, status }: { image?: string; status?: string }) => {
    return (
        <ImageContainer>
            <img src={image} alt="" />

            <ImageStatus>
                <span>{status}</span>
                {
                    status === 'Approved'
                        ? <Check color="#303445" height={18} width={18} />
                        : <Xmark color="#303445" height={18} width={18} />
                }
            </ImageStatus>
        </ImageContainer>
    )
}

const PhotoStandards = () => {

    const [images, setImages] = useState([
        {
            status: 'Approved',
            image: require('../../assets/images/hair-1.png'),
        },
        {
            status: 'Approved',
            image: require('../../assets/images/hair-2.png'),
        },
        {
            status: 'Approved',
            image: require('../../assets/images/hair-3.png'),
        },
        {
            status: 'Too Close',
            image: require('../../assets/images/hair-4.png'),
        },
        {
            status: 'Too Dark',
            image: require('../../assets/images/hair-5.png'),
        },
        {
            status: 'Background',
            image: require('../../assets/images/hair-6.png'),
        },
    ]);

    const imageList = useMemo(() => combineArrayIntoGroups(images, window.innerWidth <= 768 ? 2 : 3), [images]);

    return (
        <ImageBox>
            <h3>Clear images are needed to conduct a detailed hair analysis and assess its condition.</h3>

            <Column style={{gap: '3vw'}}>
                {
                    imageList.map((arr, index) => {
                        return (
                            <RowSpaced style={{ gap: '3vw' }} key={index}>
                                {arr.map((item, index) => {
                                    return (
                                        <Image key={index} {...item} />
                                    )
                                })}
                            </RowSpaced>
                        )
                    })
                }
            </Column>
        </ImageBox>
    )
};

export default PhotoStandards;