{"name": "cosmetrics", "version": "0.1.0", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.101", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/styled-components": "^5.1.34", "axios": "^1.7.2", "crypto-js": "^4.2.0", "iconoir-react": "^7.7.0", "moment": "^2.30.1", "react": "^18.3.1", "react-auth-kit": "^3.1.3", "react-dom": "^18.3.1", "react-router-dom": "^6.23.1", "react-scripts": "5.0.1", "styled-components": "^6.1.11", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/crypto-js": "^4.2.2"}}