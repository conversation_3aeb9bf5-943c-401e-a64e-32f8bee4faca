{% extends 'base.html' %} {% load static %} {% block title %}Dashboard -
Cosmetrics AI{% endblock %} {% block content %}

<section class="condition-section container-fluid">
    <div class="condition-container vh-100">
        <div class="row">
            <div class="col-md-8 offset-md-2 modal-body">
                <h3 class="error-message">{% if error %}{{ error }}{% endif %}</h3>
            </div>
        </div>
        {% for key, value in report.items %} {% if report.error %}
        <h3 style="color: #673a90" class="item-heading">{{value}}</h3>
        {% else %}
        <div class="hexagon col-12">
            <div class="shape">
                <div class="content">
                    <div class="">
                        <h3>{{value.1}}</h3>
                        <hr/>
                        <h3>{{value.0}}</h3>
                    </div>
                </div>
            </div>
        </div>
        {% endif %} {% endfor %}
    </div>
</section>
{% endblock %}
