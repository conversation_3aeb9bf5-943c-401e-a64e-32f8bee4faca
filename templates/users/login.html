{% extends 'base.html' %}
{% load static %}

{% block title %}Login - Cosmetrics AI{% endblock %}

{% block content %}
<div class="container-fluid container-background d-flex justify-content-center align-items-center vh-100">
  <div class="form-container">
    <h1 class="text-center mb-4">Login</h1>
    <form method="POST" novalidate>
      {% csrf_token %}

      {% for field in form %}
        <div class="mb-3">
          <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
          {{ field }}
          {% if field.errors %}
            <div class="invalid-feedback d-block">
              {% for error in field.errors %}
                {{ error }}
              {% endfor %}
            </div>
          {% endif %}
        </div>
      {% endfor %}

      {% if form.non_field_errors %}
        <div class="alert alert-danger" role="alert">
          {% for error in form.non_field_errors %}
            {{ error }}
          {% endfor %}
        </div>
      {% endif %}

      <div class="d-grid gap-2">
        <button type="submit" class="btn custom-btn rounded-5">Login</button>
      </div>
    </form>
    <div class="mt-3 text-center">
      <a href="{% url 'password_reset' %}">Forgot password?</a>
    </div>
    <div class="mt-2 text-center">
      Don't have an account? <a href="{% url 'register' %}">Sign up</a>
    </div>
  </div>
</div>
{% endblock %}