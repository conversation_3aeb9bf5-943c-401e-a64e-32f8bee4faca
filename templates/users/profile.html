{% extends "base.html" %}
{% load static %}

{% block title %}Profile - Cosmetrics AI{% endblock %}

{% block content %}
<div class="container d-flex justify-content-center align-items-center my-5 bg-light">
    <div class="form-container w-100 p-4 bg-white rounded shadow-sm">
        <h4 class="profile-heading text-primary mb-4">Delivery Information</h4>
        <form action="{% url 'profile' %}" method="POST" class="row g-3">
            {% csrf_token %}

            <div class="col-md-6">
                <label for="{{ form.first_name.id_for_label }}" class="form-label fw-medium text-primary">First Name</label>
                <div class="form-control p-2">{{ form.first_name }}</div>
            </div>

            <div class="col-md-6">
                <label for="{{ form.last_name.id_for_label }}" class="form-label fw-medium text-primary">Last Name</label>
                <div class="form-control p-2">{{ form.last_name }}</div>
            </div>

            <div class="col-md-6">
                <label for="{{ form.default_phone_number.id_for_label }}" class="form-label fw-medium text-primary">Phone Number</label>
                <div class="form-control p-2">{{ form.default_phone_number }}</div>
            </div>

            <div class="col-md-12">
                <label for="{{ form.default_street_address1.id_for_label }}" class="form-label fw-medium text-primary">Street Address 1</label>
                <div class="form-control p-2">{{ form.default_street_address1 }}</div>
            </div>

            <div class="col-md-12">
                <label for="{{ form.default_street_address2.id_for_label }}" class="form-label fw-medium text-primary">Street Address 2</label>
                <div class="form-control p-2">{{ form.default_street_address2 }}</div>
            </div>

            <div class="col-md-6">
                <label for="{{ form.default_town_or_city.id_for_label }}" class="form-label fw-medium text-primary">Town or City</label>
                <div class="form-control p-2">{{ form.default_town_or_city }}</div>
            </div>

            <div class="col-md-6">
                <label for="{{ form.default_county.id_for_label }}" class="form-label fw-medium text-primary">County</label>
                <div class="form-control p-2">{{ form.default_county }}</div>
            </div>

            <div class="col-md-6">
                <label for="{{ form.default_postcode.id_for_label }}" class="form-label fw-medium text-primary">Post Code</label>
                <div class="form-control p-2">{{ form.default_postcode }}</div>
            </div>

            <div class="col-md-6">
                <label for="{{ form.default_country.id_for_label }}" class="form-label fw-medium text-primary">Country</label>
                <div class="form-control p-2">{{ form.default_country }}</div>
            </div>

            <div class="col-12 text-center mt-3">
                <button type="submit" class="btn btn-lg btn-primary rounded">Update</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
