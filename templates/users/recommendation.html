{% extends "base.html" %} {% load static %} {% block title %} Dashboard -
Cosmetrics AI {% endblock title %} {% block content %}
<div class="recommendations-container">
  <header class="rec-header"></header>

  <section class="rec-heading bg-primary">
    <div class="">
      <h1>Thank you for using Cosmetrics AI</h1>
      <p class="rec-desc">
        We have completed our analysis of your hair and lifestyle and these
        products are recommended just for you!
      </p>
      <p class="error-message">{% if error %}{{ error }}{% endif %}</p>
    </div>
  </section>
  <section class="rec-section">
    <div class="products-container">
      {% for product in recommendations %}

      <div class="product-item">
        <div class="image-container">
          {% if product.image %}
          <a href="{% url 'product_detail' product.id %}">
            <img
              class="card-img-top img-fluid image-resize"
              src="{% static product.image_url %}"
              alt="{{ product.name }}"
            />
          </a>
          {% else %}
          <a href="{% url 'product_detail' product.id %}">
            <img
              class="card-img-top img-fluid"
              src="{{ MEDIA_URL }}noimage.png"
              alt="{{ product.name }}"
            />
          </a>
          {% endif %}
        </div>
        <div class="text-container">
          <p class="mb-0">{{ product.name }}</p>

          <div class="icon-container">
            <p>Buy Now</p>
            <i class="fa-solid fa-cart-shopping shop"></i>
          </div>
          <p class="">{{ recommendation }}</p>
        </div>
      </div>

      {% endfor %}
    </div>

    <!-- <div class="rec-products col-12">
        <div class="row row-cols-3 m-3">
          {% for product in recommendations %}
          <div class="product-item col">
            <div class="text-container card-body pb-0">
              <div class="image-container">
                {% if product.image %}
                <a href="{% url 'product_detail' product.id %}">
                  <img
                    class="card-img-top img-fluid image-resize"
                    src="{{ product.image.url }}"
                    alt="{{ product.name }}"
                  />
                </a>
                {% else %}
                <a href="{% url 'product_detail' product.id %}">
                  <img
                    class="card-img-top img-fluid"
                    src="{{ MEDIA_URL }}noimage.png"
                    alt="{{ product.name }}"
                  />
                </a>
                {% endif %}

                <div class="text-container">
                  <div class="center">
                    <p class="">{{ recommendation }}</p>
                    <div class="icon-container">
                      <p>Buy Now</p>
                      <i class="fa-solid fa-cart-shopping shop"></i>
                    </div>
                  </div>
                </div>
              </div>
              <p class="mb-0">{{ product.name }}</p>
            </div>

            {% endfor %}
          </div>
        </div>
      </div> -->
  </section>
</div>
{% endblock content %}
