{% extends 'base.html' %}
{% load static %}

{% block title %}Register - Cosmetrics AI{% endblock %}

{% block content %}
<div class="container-fluid bg-light-blue min-vh-100 d-flex align-items-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow-lg">
                    <div class="card-body p-5">
                        <h2 class="card-title text-primary text-center mb-4 fw-medium">Register</h2>
                        <form method="POST" novalidate>
                            {% csrf_token %}
                            {% if form.non_field_errors %}
                                <div class="alert alert-danger" role="alert">
                                    {{ form.non_field_errors }}
                                </div>
                            {% endif %}
                            {% for field in form %}
                                <div class="mb-3">
                                    <label for="{{ field.id_for_label }}" class="form-label text-primary">{{ field.label }}</label>
                                    {{ field }}
                                    {% if field.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ field.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                    {% if field.help_text %}
                                        <small class="form-text fs-6 text-muted">
                                            {{ field.help_text|safe }}</small>
                                    {% endif %}
                                </div>
                            {% endfor %}
                            <div class="d-grid gap-2 mt-4">
                                <button type="submit" class="btn btn-primary btn-lg">Register</button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="text-center mt-3 text-secondary">
                    Already have an account? <a href="{% url 'login' %}" class="text-primary">Log in</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}