{% extends "account/base.html" %}

{% load i18n %}
{% block head_title %}{% trans "Change Password" %}{% endblock %}

{% block inner_content %}
    <hr>
    <h2 class="logo-font mb-4">{% if token_fail %}{% trans "Bad Token" %}{% else %}{% trans "Change Password" %}{% endif %}</h2>
    <hr>

    {% if token_fail %}
        {% url 'account_reset_password' as passwd_reset_url %}
        <p>{% blocktrans %}The password reset link was invalid, possibly because it has already been used.  Please request a <a href="{{ passwd_reset_url }}">new password reset</a>.{% endblocktrans %}</p>
    {% else %}
        {% if form %}
            <form method="POST" action="{{ action_url }}">
                {% csrf_token %}
                {{ form|crispy }}
                <a class="btn btn-outline-black rounded-0" href="{% url 'account_login' %}">Back to Login</a>
                <input type="submit" name="action" value="{% trans 'change password' %}"/>
            </form>
        {% else %}
            <p>{% trans 'Your password is now changed.' %}</p>
        {% endif %}
    {% endif %}
{% endblock %}