{% extends "account/base.html" %}

{% load i18n %}

{% block head_title %}{% trans "Signup" %}{% endblock %}

{% block inner_content %}
<br><br><br><br>
<hr>
<h2 class="logo-font mb-4">{% trans "Sign Up" %}</h2>
<hr>

<p>{% blocktrans %}Already have an account? Then please <a href="{{ login_url }}">sign in</a>.{% endblocktrans %}</p>

<form class="signup" id="signup_form" method="post" action="{% url 'account_signup' %}">
  {% csrf_token %}
  {{ form|crispy }}
  {% if redirect_field_value %}
  <input type="hidden" name="{{ redirect_field_name }}" value="{{ redirect_field_value }}" />
  {% endif %}
  <a class="btn btn-outline-black rounded-0" href="{% url 'account_login' %}">Back to Login</a>
  <button type="submit">{% trans "Sign Up" %}</button>
</form>

{% endblock %}