<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta content="Display Webcam Stream" name="title">
<title>Display Webcam Stream</title>
<style>
    body {
      margin: 30px;
    }

    h1 {
      font-family: sans-serif;
      color: #666;
    }

    #container {
      width: 500px;
      height: 375px;
      border: 10px #333 solid;
    }

    #videoElement {
      width: 500px;
      height: 375px;
      background-color: #666;
    }
    
    button {
      margin-top: 20px;
      font-size: 12px;
      font-weight: bold;
      padding: 5px;
      background-color: white;
      border: 5px solid black;
    }

    button:hover {
      background-color: yellow;
    }

    button:active {
      background-color: yellowgreen;
    }
  </style>
</head>
<body>
<h1>Stop Webcam Stream</h1>
<div id="container">
    <video autoplay="true" id="videoElement">
    </video>    
</div>
<div>
    <button id="stop">Stop Video</button>
    <button id="start">Start Video</button>
    <button id="snap">Snap Picture</button>
    <!-- in your HTML template -->
    <form method="post">
        {% csrf_token %}
        <canvas name="image" id="canvas" width="320" height="240"></canvas>
        <input type="hidden" id="captured_image" name="captured_image">
        <button id="snap" type="submit" onclick="save()">Submit</button>
    </form>

</div>
<div>
</div>
<script>
    let stopVideo = document.querySelector("#stop");
    let startVideo = document.querySelector("#start");
    let snapPicture = document.querySelector("#snap");
    let video = document.querySelector("#videoElement");
    let canvas = document.querySelector("#canvas");
    let submit = document.querySelector("#submit");

    function start(e){
        if (navigator.mediaDevices.getUserMedia) {
        navigator.mediaDevices.getUserMedia({ video: true, audio: false })
            .then(function (stream) {
            video.srcObject = stream;
            })
            .catch(function (err0r) {
            console.log("Something went wrong!");
            });
        }
    }

    startVideo.addEventListener("click", start, false)


    function snap(e){
        canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
        var image_data_url = canvas.toDataURL('image/jpeg');
        console.log(image_data_url);
    }

    snapPicture.addEventListener('click', snap, false)


    function stop(e) {
        var stream = video.srcObject;
        if (stream != null){
            var tracks = stream.getTracks();

            for (var i = 0; i < tracks.length; i++) {
            var track = tracks[i];
            track.stop();
            }

            video.srcObject = null;
        }
    }

    stopVideo.addEventListener("click", stop, false);

    function save(){
        document.getElementById('captured_image').value = canvas.toDataURL('image/jpeg');
        }
  </script>
</body>
</html>