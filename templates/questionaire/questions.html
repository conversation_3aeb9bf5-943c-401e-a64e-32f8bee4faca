{% extends 'base.html' %}
{% load static %}

{% block title %}
Question form - Cosmetrics AI
{% endblock %}

{% block head %}
<style>
    #videoElement {
        background-color: #383636;
        padding: 0;
        height: 100%;
        width: 100%;
    }

    .instruction-images img {
        width: 150px;
        margin-right: 10px;
        border-radius: 50%;
    }

    .instruction-text ul {
        padding-left: 20px;
        list-style: none;
    }

    .instruction-text ul li:before {
        content: "✔️ ";
        padding-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<section class="container-fluid d-flex justify-content-center align-items-center bg-light vh-100">
    <div class="form-container p-4 bg-white rounded-3 shadow-sm">
        <form method="POST" enctype="multipart/form-data" onsubmit="return save();" id="form-submit">
            {% csrf_token %}
            <div class="mb-3">
                <label for="{{ form.id_for_category }}" class="form-label fs-4 text-secondary">
                    {{ form.category }}
                </label>
                {% if form.advice %}
                <label for="{{ form.id_for_category }}" class="form-text text-danger">
                    {{ form.advice }}
                </label>
                {% endif %}
            </div>

            {% if current_question.type == 'IU' %}
            <div class="instruction-container mb-3">
                <div class="instruction-text mb-3">
                    <ul>
                        <li>Take pictures in good light</li>
                        <li>Hair should not be wet</li>
                        <li>Pictures should be clear and visible</li>
                        <li>Submit 3 pictures (Top, back, and side)</li>
                        <li>When you have finished taking 3 pictures the quiz will continue</li>
                    </ul>
                </div>
            </div>

            <div class="d-flex justify-content-center mb-3">
                <video id="videoElement" class="rounded"></video>
            </div>

            <div class="d-flex justify-content-around align-items-center mb-3">
                <input type="hidden" id="captured_image" name="captured_image"/>
                <canvas id="image_canvas" width="320" height="240" class="d-none" required></canvas>
                <div class="d-flex gap-3">
                    <button id="toggleButton" type="button" class="btn btn-outline-warning">Start Video</button>
                    <button id="snap" type="button" class="btn btn-outline-success">Take a picture!</button>
                </div>
            </div>

            <small class="d-block text-center text-muted mb-3">Disclaimer - by sharing your image you are granting permission for us to use your image for internal use for technology development.</small>

            {% else %}
            <div class="mb-3">
                {% for field in form %}
                <div class="mb-2">
                    <label for="{{ field.id_for_label }}" class="form-label fw-bold text-primary">{{ field.label }}</label>
                    <div>{{ field }}</div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <div class="text-center">
                <input type="submit" class="btn btn-primary rounded-3" value="Submit"/>
            </div>
        </form>
    </div>
</section>

<script type="text/javascript">
    const questionType = '{{ current_question.type|escapejs }}';

    function save() {
        if (questionType === 'MC') {
            const form = document.getElementById('form-submit');
            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
            let checkedCount = 0;
            checkboxes.forEach(checkbox => { if (checkbox.checked) checkedCount++; });
            if (checkedCount === 0) {
                alert('Please select at least one option');
                return false;
            }
        }
        const isIUType = questionType === 'IU';
        if (!isIUType) return true;

        const canvas = document.getElementById('image_canvas');
        if (canvasIsEmpty(canvas)) {
            alert('Please take a picture before submitting!');
            return false;
        }

        document.getElementById('captured_image').value = canvas.toDataURL('image/jpeg');
        return true;
    }

    function canvasIsEmpty(canvas) {
        const context = canvas.getContext('2d');
        const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
        const pixels = imageData.data;
        for (let i = 0; i < pixels.length; i += 4) {
            if (pixels[i] !== 0 || pixels[i + 1] !== 0 || pixels[i + 2] !== 0 || pixels[i + 3] !== 0) {
                return false;
            }
        }
        return true;
    }

    document.getElementById('toggleButton').addEventListener('click', startVideo);
    const snapPicture = document.getElementById('snap');
    snapPicture.addEventListener('click', snap);
    const video = document.getElementById('videoElement');

    function startVideo() {
        if (navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({video: true, audio: false})
                .then(stream => video.srcObject = stream)
                .catch(error => console.log('Something went wrong!', error));
        }
        video.play();
    }

    function snap() {
        const canvas = document.getElementById('image_canvas');
        if (!video.paused) {
            canvas.getContext('2d').drawImage(video, 0, 0, canvas.width, canvas.height);
            snapPicture.textContent = 'Retake Picture';
            snapPicture.classList.replace('btn-outline-success', 'btn-outline-warning');
            video.pause();
        } else {
            snapPicture.textContent = 'Take a picture!';
            snapPicture.classList.replace('btn-outline-warning', 'btn-outline-success');
            video.play();
        }
    }

    window.onload = startVideo;
</script>
{% endblock %}
