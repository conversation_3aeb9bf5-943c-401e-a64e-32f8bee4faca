{% extends 'base.html' %} {% load static %} {% block title %} Question form -
Cosmetrics AI {% endblock %} {% block head %}
<style>
    #videoElement {
        background-color: rgb(56, 54, 54);
        padding: 0;
        height: 100%;
        width: 100%;
        margin: auto;
    }

    .container {
        flex-direction: column;
        align-items: center;
        padding: 0;
    }

    .instruction-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0;
    }

    .instruction-images {
        display: flex;
    }

    img {
        width: 150px;
        /* height: 150px; */
        margin-right: 10px;
        border-radius: 50%;
    }

    p {
        color: #7b8ede;
    }

    small {
        margin-top: 20px;
        font-style: italic;
    }
</style>
{% endblock %}

{% block content %}
<div
        class="container-fluid container-background d-flex justify-content-center align-items-center profile-section"
>
    <!--  <div class="form-container question-container col-md-8 offset-md-2">-->
    <div class="col">
        <div class="form-container">
            <form
                    method="POST"
                    enctype="multipart/form-data"
                    onsubmit="return save();"
                    id="form-submit"
            >
                {% csrf_token %}
                <div class="container">
                    <div class="row mb-3">
                        <label
                                for="{{ form.id_for_category }}"
                                class="question-label text-dark fw-bold"
                                ,
                                style="text-align: start"
                        >
                            {{ form.category }}
                        </label>
                        {% if form.advice %}
                        <label for="{{ form.id_for_category }}" style="text-align: center" class="text-danger fw-normal">
                            {{ form.advice }}
                        </label>
                        {% endif %}
                        <!-- {% if question.image %}
                        <img src="{{ question.image.url }}" alt="{{ question.title }}" />
                        {% endif %} -->
                        {% if current_question.type == 'IU' %}
                        <div class="instruction-container">
                            <div class="instruction-text">
                                <ul>
                                    <li>Take pictures in good light</li>
                                    <li>Hair should not be wet</li>
                                    <li>Pictures should be clear and visible</li>
                                    <li>Submit 3 pictures (Top, back, and side)</li>
                                    <li>When you have finished taking 3 pictures the quiz will continue</li>
                                </ul>
                            </div>
                            <div class="instruction-images">
                                <!-- <img src="{% static 'images/top.jpeg' %}" alt="" />
                                <img src="{% static 'images/side.jpeg' %}" alt="" />
                                <img src="{% static 'images/back.jpeg' %}" alt="" /> -->
                            </div>
                        </div>

                        <video id="videoElement"></video>
                        <div class="container mt-3 d-flex justify-content-around">
                            <input type="hidden" id="captured_image" name="captured_image"/>
                            <canvas
                                    class="mt-3"
                                    name="image"
                                    id="image_canvas"
                                    width="320"
                                    height="240"
                                    max-width="320"
                                    max-height="240"
                                    style="display: none"
                                    required
                            >
                            </canvas>

                            <div class="gap-4">
                                <button id="toggleButton" type="button" class="btn btn-outline-warning rounded gap-4">
                                    Start Video
                                </button>
                                <button id="snap" type="button" class="btn btn-outline-success rounded">Take a
                                    picture!
                                </button>
                            </div>

                            <small
                            >Disclaimer - by sharing your image you are granting permission
                                for us to use your image for internal use for technology
                                development</small
                            >
                        </div>
                        {% else %}
                        {% for field in form %}
                        <label for="{{ form.id_for_label }}" class="question-label fw-medium text-primary">
                            {{ field.label }}
                        </label>
                        <label class="answer-label fw-normal"> {{ field }} </label>
                        {% endfor %}
                        {% endif %}
                    </div>
                </div>
                <div class="row question-btn-container">
                    <input type="submit" class="btn btn-primary rounded-3"/>
                </div>
            </form>
        </div>
    </div>
</div>
<script type="text/javascript">
    const questionType = '{{ current_question.type|escapejs }}';

    function save() {
        if (questionType === 'MC') {
            const form = document.getElementById('form-submit');
            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
            let checkedCount = 0;
            checkboxes.forEach(function (checkbox) {
                if (checkbox.checked) {
                    checkedCount++;
                }
            });
            if (checkedCount === 0) {
                alert('Please select at least one option');
                return false;
            }
        }
        const isIUType = questionType === 'IU';
        if (!isIUType) return true;

        const canvas = document.querySelector('#image_canvas');
        if (canvasIsEmpty()) {
            alert('Please take a picture before submitting!');
            return false;
        }

        function canvasIsEmpty() {
            const context = canvas.getContext('2d');
            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            const pixels = imageData.data;
            for (let i = 0; i < pixels.length; i += 4) {
                if (
                    pixels[i] !== 0 ||
                    pixels[i + 1] !== 0 ||
                    pixels[i + 2] !== 0 ||
                    pixels[i + 3] !== 0
                ) {
                    return false; // If a non-transparent pixel is found, canvas is not empty
                }
            }

            return true; // If all pixels are transparent, canvas is empty
        }

        document.getElementById('captured_image').value =
            canvas.toDataURL('image/jpeg');
        // If everything is fine, submit the form
        return true; // Submit the form
    }

    document
        .getElementById('toggleButton')
        .addEventListener('click', function () {
            startVideo();
        });

    const snapPicture = document.querySelector('#snap');
    snapPicture.addEventListener('click', snap);
    const video = document.querySelector('#videoElement');
    const canvas = document.querySelector('#image_canvas');

    function startVideo() {
        // Your function code goes here
        if (navigator.mediaDevices.getUserMedia) {
            navigator.mediaDevices.getUserMedia({video: true, audio: false})
                .then(function (stream) {
                    video.srcObject = stream;
                })
                .catch(function (error) {
                    console.log('Something went wrong!', error);
                });
        }
        video.play();
    }

    function snap(e) {
        if (!video.paused) {
            console.log('Pausing');
            canvas
                .getContext('2d')
                .drawImage(video, 0, 0, canvas.width, canvas.height);
            const image_data_url = canvas.toDataURL('image/jpeg');
            snapPicture.textContent = 'Retake Picture';
            snapPicture.classList.remove('btn-outline-success');
            snapPicture.classList.add('btn-outline-warning');
            video.pause();
        } else {
            snapPicture.classList.remove('btn-outline-warning');
            snapPicture.classList.add('btn-outline-success');
            snapPicture.textContent = 'Snap Picture';
            video.play();
        }
    }
</script>
{% endblock %}
