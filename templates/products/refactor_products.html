{% extends 'base.html' %}
{% load static %}
{% block title %}Products - Cosmetrics AI{% endblock %}
{% block content %}

<style>
    .product-image {
        width: 100%;
        height: 300px;
        object-fit: cover;
    }
</style>

<div class="overlay"></div>

<div class="container mt-4">
    <div class="row row-cols-md-4 g-2 mt-2">

        {% for product in products %}
        <div class="col-md-4 card h-50 border-0 p-3">

            {% if product.image %}
            <a href="{% url 'product_detail' product.id %}">
                <img
                        class="card-img-top img-fluid product-image"
                        src="{% static product.image_url %}"
                        alt="{{ product.name }}"
                />
            </a>
            {% else %}
            <a href="{% url 'product_detail' product.id %}">
                <img
                        class="card-img-top img-fluid product-image"
                        src="{% static 'images/shampoo.jpg' %}"
                        alt="{{ product.name }}"
                />
            </a>
            {% endif %}
            <div class="card-body p-2">
                <p class="mb-0">{{ product.name }}</p>
            </div>
            <div class="card-footer bg-white p-2 border-0 text-left">
                <div class="row">
                    <div class="col">
                        <p class="h5 mt-1 mb-0 text-left font-weight-bold h6">
                            £{{ product.price }}
                        </p>
                        {% if product.category %}
                        <p class="small mt-1 mb-0">
                            <a
                                    class="text-muted h6"
                                    href="{% url 'products' %}?category={{ product.category.name }}"
                            >
                                <i class="fas fa-tag mr-1"></i>{{ product.category.friendly_name }}
                            </a>
                        </p>
                        {% endif %}
                        {% if product.rating %}
                        <small class="text-secondary">
                            <i class="fas fa-star mr-1 text-warning"></i>{{ product.rating }} / 5
                        </small>
                        {% else %}
                        <small class="text-muted">No Rating</small>
                        {% endif %}
                        {% if request.user.is_superuser %}
                        <small class="ml-3">
                            <a href="{% url 'product_detail' product.id %}">Edit</a> |
                            <a class="text-danger" href="">Delete</a>
                        </small>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="card-buy btn btn-primary p-2 mb-2">
                <a href="{{ product.link }}" class="text-white"> Buy </a>
                <i class="fa-solid fa-cart-shopping shop ms-2"></i>
            </div>
        </div>

        {% endfor %}
    </div>

</div>
{% endblock %}

{% block postloadjs %}
{{ block.super }}
<script type="text/javascript">
    $('.btt-link').click(function (e) {
      window.scrollTo(0, 0);
    });
</script>

<script type="text/javascript">
    $('#sort-selector').change(function () {
      var selector = $(this);
      var currentUrl = new URL(window.location);

      var selectedVal = selector.val();
      if (selectedVal != 'reset') {
        var sort = selectedVal.split('_')[0];
        var direction = selectedVal.split('_')[1];

        currentUrl.searchParams.set('sort', sort);
        currentUrl.searchParams.set('direction', direction);

        window.location.replace(currentUrl);
      } else {
        currentUrl.searchParams.delete('sort');
        currentUrl.searchParams.delete('direction');

        window.location.replace(currentUrl);
      }
    });
</script>
{% endblock %}
