{% extends "base.html" %}
{% load static %}

{% block page_header %}
<div class="container header-container">
    <div class="row">
        <div class="col"></div>
    </div>
</div>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mt-3">
        <div class="col-md-8 offset-md-2">
            <div class="col-md-5 image-container">
                {% if product.image %}
                <a href="{% static product.image_url %}" target="_blank">
                    <img class="img-fluid" style="min-height:10vh; max-height: 30vh;" src="{% static product.image_url %}" alt="{{ product.name }}">
                </a>
                {% else %}
                <a href="#">
                    <img class="card-img-top img-fluid" style="min-height:10vh; max-height: 30vh;" src="{{ MEDIA_URL }}noimage.png" alt="No image available">
                </a>
                {% endif %}
            </div>

            <div class="col-md-7 product-details-container right">
                <h2 class="mb-3">{{ product.name }}</h2>
                <p class="lead mb-0 text-left font-weight-bold">£{{ product.price }}</p>

                {% if product.category %}
                <p class="small mt-1 mb-0">
                    <a class="text-muted" href="{% url 'products' %}?category={{ product.category.name }}">
                        <i class="fas fa-tag mr-1"></i>{{ product.category.friendly_name }}
                    </a>
                </p>
                {% endif %}

                {% if product.rating %}
                <small class="text-warning bg-black p-2 rounded-3 m-3">
                    <i class="fas fa-star mr-1"></i>{{ product.rating }} / 5
                </small>
                {% else %}
                <small class="text-warning bg-secondary p-2 rounded-3">No Rating</small>
                {% endif %}

                {% if product.ingredients %}
                <p class="cut-off text-capitalize">{{ product.ingredients }}</p>
                <input class="expand-btn" type="checkbox"/>
                {% endif %}

                <div class="row bg-light rounded-5 p-3 mt-3">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="quantity_{{ product.id }}">Quantity</label>
                            <div class="input-group">
                                <button class="decrement-qty btn btn-black rounded-0" data-item_id="{{ product.id }}" id="decrement-qty_{{ product.id }}">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input class="form-control text-center" type="number" name="quantity" value="1" min="1" max="99" data-item_id="{{ product.id }}" id="id_qty_{{ product.id }}">
                                <button class="increment-qty btn btn-black rounded-0" data-item_id="{{ product.id }}" id="increment-qty_{{ product.id }}">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        {% if user.is_authenticated %}
                        <form method="POST" action="" enctype="multipart/form-data">
                            {% csrf_token %}
                            <a class="btn btn-danger btn-lg rounded-4" href="{{ product.link }}">Buy</a>
                        </form>
                        {% endif %}
                        <div class="more-btn mt-2">
                            <a href="{% url 'products' %}" class="btn bg-primary rounded-4 text-uppercase text-white">
                                <i class="fas fa-chevron-left"></i> More Products
                            </a>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    {% if request.user.is_superuser %}
                    <small class="ml-3">
                        <a href="{% url 'edit_product' product.id %}">Edit</a> |
                        <a class="text-danger" href="{% url 'delete_product' product.id %}">Delete</a>
                    </small>
                    {% endif %}
                    <p class="mt-3">{{ product.description }}</p>

                    {% if product.has_sizes %}
                    <div class="form-group mt-2">
                        <label for="id_product_size"><strong>Size:</strong></label>
                        <select class="form-control rounded-0 w-50" name="product_size" id="id_product_size">
                            <option value="xs">XS</option>
                            <option value="s">S</option>
                            <option value="m" selected>M</option>
                            <option value="l">L</option>
                            <option value="xl">XL</option>
                        </select>
                    </div>
                    {% endif %}

                    <input type="hidden" name="redirect_url" value="{{ request.path }}">
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block postloadjs %}
{{ block.super }}
{% include 'products/includes/quantity_input_script.html' %}
{% endblock %}
