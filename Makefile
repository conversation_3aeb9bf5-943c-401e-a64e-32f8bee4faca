.PHONY: help makemigrations \
	migrate createsuperuser products \
	questions run start_frontend frontend_tests \
	precommit_all rename_assets collectstatic

# Stating with Make - For Django - React project
# Help command to list available commands
help:
	@echo "Available commands:"
	@echo "  make print_settings"
	@echo "  make makemigrations  - Apply Django makemigrations"
	@echo "  make migrate     - Apply Django migrations"
	@echo "  make products   - Seed Products table"
	@echo "  make questions   - Seed Questionnaire table"
	@echo "  make seed_users   - Seed Users table"
	@echo "  make run - Start the backend Django server"
	@echo "  make collectstatic   - Collect media assets into media dir"
	@echo "  make run_tests   - Run Django configured tests"

print_settings:
	python manage.py print_settings

makemigrations:
	uv python manage.py makemigrations

migrate:
	uv python manage.py migrate

createsuperuser:
	python manage.py createsuperuser

products:
	python manage.py populate_default_products

questions:
	python manage.py populate_default_questions

seed_users:
	python manage.py populate_users

run:
	python manage.py runserver

start_frontend:
	cd ui && npm start

frontend_tests:
	cd ui && npm test

precommit:
	uv run precommit run

collectstatic:
	python manage.py collectstatic --noinput

rename_media_assets:
	python rename_assets.py

run_tests:
	python manage.py test

lint:
	uv run ruff check --fix

format:
	uv run ruff format --fix
