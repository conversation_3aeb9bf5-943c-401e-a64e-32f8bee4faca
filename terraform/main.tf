terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">=4.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
    kubernetes = {
        source = "hashicorp/kubernetes"
        version = ">=2.24"
    }
  }
}


provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

provider "aws" {
  version = "~> 5.0"
  region  = var.aws_region
}

provider "kubernetes" {
  host = var.kubernetes_url

#  client_certificate     = file("~/.kube/client-cert.pem")
#  client_key             = file("~/.kube/client-key.pem")
#  cluster_ca_certificate = file("~/.kube/cluster-ca-cert.pem")
}
