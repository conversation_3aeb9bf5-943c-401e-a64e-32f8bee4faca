# COSMETRICS AI README
[![Deploy to AWS EKS](https://github.com/Cosmetrics-Ai/Cosm2-Software-Development/actions/workflows/deploy-to-eks.yml/badge.svg)](https://github.com/Cosmetrics-Ai/Cosm2-Software-Development/actions/workflows/deploy-to-eks.yml)

## Requirements

Docker

1. Install Docker desktop and open it to start docker daemon

- https://docs.docker.com/get-docker/

2. Install Docker without GUI

- https://docs.docker.com/get-docker/ (find download instruction a bit further down after selecting OS)

## Setup

1. Pick a location for git repository
2. Download this git repo

- https : git clone https://github.com/Cosm1/Software-Development-.git
- ssh : <NAME_EMAIL>:Cosm1/Software-Development-.git

3. Change into git repo directory

- cd Software-Development-

4. Build image
   - docker compose build
5. Run container(make sure you are in repo volume command is relative)
   - docker compose up
6. Check if there are no issues by going to http:localhost:8000
7. Activate pip environment by typing "python3 -m pipenv shell" from base folder with Pipfile and manage.py
8. run 'pip install -r requirements.txt' to install project dependencies
9. While in pip environment. You can run project commands as normal with python i.e

- python manage.py startapp [name of app]
- run: [WINDOWS] python manage.py runserver [to start the development server]
- run: [MAC] python3 manage.py runserver [to start the development server]

## If Docker fails

- Use a virtual environment with requirements file. i.e pipenv, virtualenv(venv) etc.
- You can install the library through pip
  - pipenv
  - python3 -m pip install pipenv
- Create and enter environment
  - python3 -m pipenv shell
- Create a postgres database
- Create a .env file and copy the environment variables in env.sample to your .env file
- Install requirements
  - pip install -r requirements.txt
- Make sure to enter environment everytime, some editors will allow you to set them i.e vscode

## Extra

Run these commands while in pip environment
To make migrations after 'saving' changes

- python manage.py makemigrations

Updating requirements.txt

- pip freeze > requirements.txt

To apply migration to database

- python manage.py migrate

To create admin user

- python manage.py createsuperuser

To access the shell on container while docker container is running

- docker compose exec django_server bash

To get logs

- docker logs cosmetrics_server

To check info on container

- docker inspec cosmetrics_server

To stop container

- docker stop cosmetrics_server

To remove container

- docker rm cosmetrics_server

## Frontend Tooling

cd into the src folder where we have all the frontend tooling including a package.json file

- npm install [to install dependencies]
- npm run compile:css:watch [to start watching for file changes in scss]

## Operational Commands
```bash

djoser  - logout
#TODO: Retake questionnaire: uri: /retake
#
docker compose exec -it web python manage.py makemigrations
docker compose exec -it web python manage.py migrate

docker compose exec -it web python manage.py createsuperuser

docker compose exec -it web python manage.py runserver
docker compose exec -it web python manage.py populate_default_products
docker compose exec -it web python manage.py populate_default_questions
docker compose exec -it web python manage.py populate_corporate_questions
docker compose exec -it web python manage.py collectstatic
docker compose exec -it web python manage.py findstatic

docker compose exec -it web python manage.py python manage.py export_questions
docker compose exec -it web python manage.py dumpdata -o data.json
docker compose exec -it cosm_web python manage.py loaddata data.json
docker compose exec -it web python manage.py loaddata users.json



docker compose exec -it web python manage.py check -deploy

# Underlining commanded
django-admin dumpdata --format=json --database=test app_label.ModelName | django-admin loaddata --format=json --database=prod -

#use after destructive db changes
docker compose exec -it web python manage.py migrate --run-syncdb
docker compose exec -it web python manage.py flush

#Postgres drop database
docker compose exec db psql -U postgres -d postgres -c "DROP DATABASE cosmetrics;"


docker exec -it <container_id/container_name> psql -U <user_name> <database_name>


docker exec -it cosm_db psql -U postgres cosmetrics

#test: [ "CMD", "pg_isready", "-q", "-d", "postgres", "-U", "postgres" ]




# Postgres Troubleshooting
psql -h localhost -U postgres



```

## Acceptance Testing
We are using Selenium library for test automation and will focus on PageObject model to ensure easy
maintenance

### Deployment Runbook (Manual steps) - Not CI/CD yet
1.  Push latest code changes to Github
2. Merge code into main branch
3. Download the main repo as a zip file
4. Assuming you have the .pem from AWS EC2 creation, copy zipped repo to home directory on EC2.
Sample command:
```bash
scp -i $HOME/secrets/my_ec2_key.pem $HOME/Downloads/main_code.zip ec2-user@ec2_public_ip:~/.
```
5. Connect via ssh to AWS EC2 to finalise deployment

## Code Static Analysis
```bash
bandit -r path_to_code ```
