
services:
  db:
    container_name: cosm_db
    restart: unless-stopped
    image: postgres:16.3-alpine
    volumes:
      - postgres_volume:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=cosmetrics
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=cosmetrics_pass
    networks:
      - default
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -d $${POSTGRES_DB} -U $${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5
  web:
    container_name: cosm_web
    restart: unless-stopped
    image: ghcr.io/cosm1/software-development:latest
    build: .
    ports:
      - 80:80
    environment:
      - DB_NAME=cosmetrics
      - DB_USER=postgres
      - DB_PASSWORD=cosmetrics_pass
      - DB_HOST=cosm_db
      - ALLOWED_HOSTS=0.0.0.0
    depends_on:
      - db
    networks:
      - default
    develop:
      watch:
        - path: ./requirements.txt
          action: rebuild
        - path: ./
          action: sync+restart
          target: /app

volumes:
  postgres_volume:
networks:
  default:
    name: internal
    driver: bridge
