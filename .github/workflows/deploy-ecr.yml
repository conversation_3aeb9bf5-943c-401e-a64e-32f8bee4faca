name: Push to Amazon ECR

on:
  push:
    branches: [ "main" ]

env:
  AWS_REGION: eu-west-3                  # set this to your preferred AWS region, e.g. us-west-1
  ECR_REPOSITORY: django-ec2             # set this to your Amazon ECR repository name

permissions:
  contents: read

jobs:
  push-to-ecr:
    name: Push to ECR
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: 471112819534.dkr.ecr.eu-west-3.amazonaws.com
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG" > image-details.txt

    - name: Upload image details
      uses: actions/upload-artifact@v3
      with:
        name: image-details
        path: image-details.txt


