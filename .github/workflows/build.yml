name: Build and Deploy Docker Container

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main
  workflow_dispatch:

permissions:
  actions: none
  checks: none
  contents: read
  deployments: none
  id-token: none
  issues: none
  discussions: none
  packages: write
  pages: none
  pull-requests: none
  repository-projects: none
  security-events: none
  statuses: none

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Repository name case change
      id: string
      uses: ASzc/change-string-case-action@v5
      with:
        string: ${{ github.repository }}
    - name: Set up Docker Buildx
      id: buildx
      uses: docker/setup-buildx-action@v3
    - name: Cache docker layers
      uses: actions/cache@v3
      with:
        path: /tmp/.buildx-cache
        key: ${{ runner.os }}-buildx-${{ github.sha }}
        restore-keys: |
          ${{ runner.os }}-buildx-
    - name: Login to Docker Hub
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    - name: Set tags and labels for docker image
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ghcr.io/${{ steps.string.outputs.lowercase }}
        tags: |
          type=raw,value=latest,enable=${{ github.ref == format('refs/heads/{0}', github.event.repository.default_branch) }}
          type=ref,event=branch
          type=ref,event=pr
          type=sha
    - name: Build and push
      uses: docker/build-push-action@v5
      with:
        context: .
        builder: ${{ steps.buildx.outputs.name }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
