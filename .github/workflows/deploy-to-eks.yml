name: Deploy to A<PERSON> EKS

on:
  push:
    branches: [ "main" ]

env:
  AWS_REGION: eu-west-3
  CLUSTER_NAME: cosm-dev-cluster
  KUBERNETES_NAMESPACE: default

jobs:
  deploy-to-eks:
    name: Deploy to E<PERSON>
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Update kubeconfig
      run: |
        aws eks update-kubeconfig --region ${{ env.AWS_REGION }} --name ${{ env.CLUSTER_NAME }}

    - name: Verify kubeconfig
      run: |
        cat ~/.kube/config

    - name: Test kubectl connection
      run: |
        kubectl get nodes

    - name: Deploy to Kubernetes
      run: |
        # Fetch the latest image tag from ECR
        IMAGE_TAG=$(aws ecr describe-images --repository-name django-ec2 --query 'sort_by(imageDetails,& imagePushedAt)[-1].imageTags[0]' --output text)
        IMAGE_URI="471112819534.dkr.ecr.eu-west-3.amazonaws.com/django-ec2:$IMAGE_TAG"
        echo "Using image URI: $IMAGE_URI"

        # Replace the image tag in deployment.yaml with the latest one
        sed -i "s|471112819534.dkr.ecr.eu-west-3.amazonaws.com/django-ec2:[^ ]*|$IMAGE_URI|" k8s/deployment.yaml

        # Apply the updated deployment
        kubectl apply --validate=false -f k8s/deployment.yaml -f k8s/service.yaml

    - name: Verify Deployment
      run: kubectl get deployments

    - name: Get Pods Status
      run: kubectl get pods

    - name: Describe Pods for Detailed Errors
      run: |
        kubectl get pods -o json | jq -r '.items[].metadata.name' | while read pod_name; do
          echo "Description for pod: $pod_name"
          kubectl describe pod $pod_name
        done
      continue-on-error: true

    - name: Fetch and Print Logs
      run: |
        kubectl get pods -o json | jq -r '.items[].metadata.name' | while read pod_name; do
          echo "Logs for pod: $pod_name"
          kubectl logs $pod_name
        done
      continue-on-error: true
