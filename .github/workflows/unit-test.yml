name: Unit test

on:
  pull_request:

permissions:
  actions: none
  checks: none
  contents: read
  deployments: none
  id-token: none
  issues: none
  discussions: none
  packages: none
  pages: none
  pull-requests: none
  repository-projects: none
  security-events: none
  statuses: none

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Repository name case change
      id: string
      uses: ASzc/change-string-case-action@v5
      with:
        string: ${{ github.repository }}
    - name: Set up Docker Buildx
      id: buildx
      uses: docker/setup-buildx-action@v3
    - name: Set tags and labels for docker image
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ghcr.io/${{ steps.string.outputs.lowercase }}
        tags: |
          type=raw,value=test
    - name: Build and load
      uses: docker/build-push-action@v5
      with:
        context: .
        builder: ${{ steps.buildx.outputs.name }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        push: false
        load: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
    - name: Run tests
      uses: adambirds/docker-compose-action@v1.3.0
      with:
        compose-file: "./docker-compose.test.yml"
        down-flags: "--volumes"
        test-container: "web"
        test-command: "/app/manage.py test"
