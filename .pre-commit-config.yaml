repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: no-commit-to-branch
    hooks:
      - id: check-added-large-files
        args: ["--maxkb=5000"]
      - id: check-toml
      - id: check-json
      - id: check-symlinks
      - id: debug-statements
      - id: detect-private-key
      - id: check-yaml
        args:
          - --unsafe
      - id: trailing-whitespace
  - repo: https://github.com/psf/black
    rev: 23.11.0
    hooks:
      - id: black
        language_version: python3.12
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.11
    hooks:
      - id: ruff
        types_or: [python, pyi, jupyter]
        args: [--fix]
      - id: ruff-format
        types_or: [python, pyi, jupyter]
  - repo: local
    hooks:
      - id: check-requirements-txt
        name: check-requirements-txt
        description: Check the missing packages in requirements.txt.
        entry: check-requirements-txt
        args: ['--dst_dir', '.', '--ignore', 'pip,whatever,modules,you,want,to,ignore,with,comma,separated']
        language: python
        types: [python]
