import json

from django.core.files import File
from django.core.management.base import BaseCommand

from products.models import Product


class Command(BaseCommand):
    """
    populate default question from tilda
    """

    def handle(self, *args, **options):
        # Opening JSON file
        Product.objects.all().delete()
        f = open("products/fixtures/product.json")

        # returns JSON object as
        # a dictionary
        product_data = json.load(f)
        amazon_query_link = "https://www.amazon.co.uk/s?k="
        for product in product_data:
            name = product["fields"]["name"].strip()
            price = product["fields"]["price"]
            ingredients = product["fields"]["Ingredients"]
            protein_moisture = product["fields"]["protein_moisture"]
            texture = product["fields"]["texture"]
            special_instruction = product["fields"]["special_instruction"]
            hair_issues = product["fields"]["hair_issues"]
            rating = product["fields"]["rating"]
            hairtype = product["fields"]["hairtype"]
            hair_goals = product["fields"]["hair_goals"]
            image_url = product["fields"]["image_url"]

            product = Product(
                name=name,
                image_url=image_url,
                price=price,
                ingredients=ingredients,
                protein_moisture=protein_moisture,
                texture=texture,
                special_instruction=special_instruction,
                hair_issues=hair_issues,
                rating=rating,
                hairtype=hairtype,
                hair_goals=hair_goals,
                link=amazon_query_link + name,
            )
            try:
                product.image.save(name, File(open(image_url, "rb")))
            except FileNotFoundError:
                image_url = "images/shampoo.jpg"
                product.image.save(name, File(open(image_url, "rb")))

        print("Products created !")

        f.close()
