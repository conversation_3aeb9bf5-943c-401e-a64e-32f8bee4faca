from django.contrib import admin

from .models import Category, Product

# Register your models here.


class ProductAdmin(admin.ModelAdmin):
    list_display = (
        "sku",
        "name",
        "ingredients",
        "insights",
        "Protein/Moisture",
        "HairType",
        "Porosity",
        "Texture",
        "Special-Instruction",
        "Hair-Goals",
        "Hair-Issues",
        "Internal-Insights",
        "category",
        "price",
        "rating",
        "image",
    )

    ordering = ("sku",)


class CategoryAdmin(admin.ModelAdmin):
    list_display = (
        "friendly_name",
        "name",
    )


admin.site.register(Product)
admin.site.register(Category)
