# Generated by Django 4.1.2 on 2023-02-05 15:42

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Category",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.<PERSON>r<PERSON><PERSON>(max_length=254)),
                (
                    "friendly_name",
                    models.CharField(blank=True, max_length=254, null=True),
                ),
            ],
            options={
                "verbose_name_plural": "Categories",
            },
        ),
        migrations.CreateModel(
            name="Product",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("sku", models.CharField(blank=True, max_length=254, null=True)),
                ("name", models.Char<PERSON>ield(max_length=254)),
                ("ingredients", models.TextField(max_length=1024)),
                ("insights", models.TextField(max_length=1024)),
                ("protein_moisture", models.TextField(max_length=1024)),
                ("hairtype", models.TextField(max_length=1024)),
                ("porosity", models.TextField(max_length=1024)),
                ("texture", models.TextField(max_length=1024)),
                ("special_instruction", models.TextField(max_length=1024)),
                ("hair_goals", models.TextField(max_length=1024)),
                ("hair_issues", models.TextField(max_length=1024)),
                ("internal_insights", models.TextField(max_length=1024)),
                ("category", models.TextField(max_length=1024)),
                (
                    "has_sizes",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                ("price", models.DecimalField(decimal_places=2, max_digits=8)),
                (
                    "rating",
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=6, null=True
                    ),
                ),
                ("image_url", models.URLField(blank=True, max_length=1024, null=True)),
                (
                    "image",
                    models.ImageField(
                        blank=True, max_length=1024, null=True, upload_to=""
                    ),
                ),
            ],
        ),
    ]
