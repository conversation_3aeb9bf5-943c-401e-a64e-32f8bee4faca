from ninja import Schema, ModelSchema
from pydantic import HttpUrl
from typing import Optional
from products.models import Product


class CategorySchema(Schema):
    id: int
    name: str
    friendly_name: Optional[str] = None


class CategoryCreateSchema(Schema):
    name: str
    friendly_name: Optional[str] = None


class ProductOutSchema(ModelSchema):
    class Config:
        from_attributes = True
        model = Product
        model_fields = "__all__"


class ProductInSchema(Schema):
    id: int
    category: Optional[CategorySchema] = None
    sku: Optional[str] = None
    name: str
    ingredients: str
    insights: str
    protein_moisture: str
    hairtype: str
    porosity: str
    texture: str
    special_instruction: str
    link: Optional[str] = None
    hair_goals: str
    hair_issues: str
    internal_insights: str
    has_sizes: Optional[bool] = None
    price: float
    rating: Optional[str] = None
    image_url: Optional[HttpUrl] = None
    image: Optional[str] = None


class ProductCreateSchema(Schema):
    category_id: Optional[int] = None
    sku: Optional[str] = None
    name: str
    ingredients: str
    insights: str
    protein_moisture: str
    hairtype: str
    porosity: str
    texture: str
    special_instruction: str
    link: Optional[str] = None
    hair_goals: str
    hair_issues: str
    internal_insights: str
    has_sizes: Optional[bool] = None
    price: float
    rating: Optional[str] = None
    image_url: Optional[HttpUrl] = None
    image: Optional[str] = None
