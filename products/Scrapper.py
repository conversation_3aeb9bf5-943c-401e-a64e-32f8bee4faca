#!/usr/bin/env python
# coding: utf-8

# In[ ]:


import requests
from bs4 import BeautifulSoup
import pandas as pd

reviewlist = []
# get the url link from the local host of the product we are recommending, accessing the product page within the system


def get_soup(url):
    r = requests.get(
        "http://localhost:8050/render.html", params={"url": url, "wait": 2}
    )
    soup = BeautifulSoup(r.text, "html.parser")
    return soup


# function to get
def get_reviews(soup, soup1):
    price = soup1.find(id="corePriceDisplay_desktop_feature_div").get_text().strip()
    desc = soup1.find(id="productDescription").get_text().strip()

    # review={
    #'Product-Price':price,
    #'Product_Description':desc,
    #       }
    # reviewlist.append(review)

    reviews = soup.find_all("div", {"data-hook": "review"})
    # part that gather all details from the product
    try:
        for item in reviews:
            review = {
                "Product-Name": soup.title.text.replace(
                    "Amazon.co.uk:Customer reviews:", ""
                ).strip(),
                "Product-Price": soup1.sns - base - price.text(),
                "Product-Description": desc,
                #'Product-Description':soup.productDescription.text().strip(),
                "Review_Title": item.find(
                    "a", {"data-hook": "review-title"}
                ).text.strip(),
                "Rating": float(
                    item.find("i", {"data-hook": "review-star-rating"})
                    .text.replace("out of 5 stars", "")
                    .strip()
                ),
                "Review-Body": item.find(
                    "span", {"data-hook": "review-body"}
                ).text.strip(),
            }
            reviewlist.append(review)

    except:
        pass


# enter the actual product URL
# one of them gets only the info/details, the other gets all the comments
for x in range(1, 5):  # Here Range Corresponds to number of review pages.
    soup = get_soup(
        f"https://www.amazon.co.uk/Aussie-Blonde-Shampoo-Conditioner-Miracle/product-reviews/B09X1TMRD2/ref=cm_cr_dp_d_show_all_btm?ie=UTF8&reviewerType=all_reviews={x}"
    )
    # Enter reviews URL here
    soup1 = get_soup(
        "https://www.amazon.co.uk/Aussie-Blonde-Shampoo-Conditioner-Miracle/dp/B09X1TMRD2/ref=sr_1_3_sspa?crid=1W2SHDCYIF6T2&keywords=shampoo%2Band%2Bconditioner&qid=1657049565&sprefix=shampoo%2Caps%2C79&sr=8-3-spons&spLa=ZW5jcnlwdGVkUXVhbGlmaWVyPUFYMEpPTEtJMDNNMkwmZW5jcnlwdGVkSWQ9QTA4OTU1MDlSRFY2MFc5QUVIMUgmZW5jcnlwdGVkQWRJZD1BMDEyMzU4NDM2WVdPMlpYR0pGVEEmd2lkZ2V0TmFtZT1zcF9hdGYmYWN0aW9uPWNsaWNrUmVkaXJlY3QmZG9Ob3RMb2dDbGljaz10cnVl&th=1"
    )
    # Enter Product details URL here
    print(f"Getting page: {x}")
    get_reviews(soup, soup1)
    print(len(reviewlist))
    if not soup.find("li", {"class": "a-disabled a-last"}):
        pass
    else:
        break

df = pd.DataFrame(reviewlist)
df.to_excel("Aussie2.xlsx", index=False)
print("Fin.")
