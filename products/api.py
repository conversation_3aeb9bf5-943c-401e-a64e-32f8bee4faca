from typing import List

from ninja import Router, Schema
from django.shortcuts import get_object_or_404
from .models import Product, Category
from .schemas import (
    ProductInSchema,
    ProductCreateSchema,
    CategorySchema,
    CategoryCreateSchema,
    ProductOutSchema,
)

router = Router()


# Category Endpoints


@router.get("/categories/{category_id}", response=CategorySchema)
def get_category(request, category_id: int):
    category = get_object_or_404(Category, id=category_id)
    return category


@router.post("/categories/", response=CategorySchema)
def create_category(request, payload: CategoryCreateSchema):
    category = Category.objects.create(**payload.dict())
    return category


@router.put("/categories/{category_id}", response=CategorySchema)
def update_category(request, category_id: int, payload: CategoryCreateSchema):
    category = get_object_or_404(Category, id=category_id)
    for attr, value in payload.dict().items():
        setattr(category, attr, value)
    category.save()
    return category


@router.delete("/categories/{category_id}", response={204: None})
def delete_category(request, category_id: int):
    category = get_object_or_404(Category, id=category_id)
    category.delete()
    return 204


# Product Endpoints


@router.get("/", response=List[ProductOutSchema])
def get_all_products(request):
    products = Product.objects.all()
    return 200, products


@router.get("/{int:product_id}", response=ProductOutSchema)
def get_product(request, product_id: int):
    product = get_object_or_404(Product, id=product_id)
    return 200, product


# @router.get("/recommendations", response=List[ProductOutSchema])
# def get_recommened_products(request, product_ids: List[int] = Query(...)):
#     products = Product.objects.filter(id__in=product_ids)
#     return products

#
# @router.get("/recommendations", response=List[ProductOutSchema])
# def get_recommended_products(request, product_ids: List[int] = Query(...)):
#     if not product_ids:
#         return []  # or raise an exception
#
#     # Use select_related/prefetch_related if needed to avoid N+1 queries
#     products = Product.objects.filter(id__in=product_ids).select_related()  # adjust based on model relationships
#
#     if not products.exists():
#         return []  # Optionally, handle empty result set
#
#     return products


# Define the Pydantic schema for the request body
class ProductIdsSchema(Schema):
    id: List[int]


@router.post("/recommendations", response=List[ProductOutSchema])
def get_recommended_products(request, payload: ProductIdsSchema):
    # Extract the list of product_ids from the payload
    product_ids = payload.id

    # Check if the product_ids list is not empty
    if not product_ids:
        return []  # or raise an exception

    # Use select_related/prefetch_related if needed to optimize the query
    products = Product.objects.filter(
        id__in=product_ids
    ).select_related()  # Adjust based on model relationships

    # Check if products exist
    if not products.exists():
        return []  # Optionally handle empty result set

    return products


@router.get("/{str:product_name}", response=ProductOutSchema)
def get_product_by_name(request, product_name: str):
    product = get_object_or_404(Product, name=product_name)
    return product


@router.post("/", response=ProductOutSchema)
def create_product(request, payload: ProductCreateSchema):
    category = None
    if payload.category_id:
        category = get_object_or_404(Category, id=payload.category_id)
    product = Product.objects.create(
        category=category,
        sku=payload.sku,
        name=payload.name,
        ingredients=payload.ingredients,
        insights=payload.insights,
        protein_moisture=payload.protein_moisture,
        hairtype=payload.hairtype,
        porosity=payload.porosity,
        texture=payload.texture,
        special_instruction=payload.special_instruction,
        link=payload.link,
        hair_goals=payload.hair_goals,
        hair_issues=payload.hair_issues,
        internal_insights=payload.internal_insights,
        has_sizes=payload.has_sizes,
        price=payload.price,
        rating=payload.rating,
        image_url=payload.image_url,
        image=payload.image,
    )
    return product


@router.put("/{product_id}", response=ProductInSchema)
def update_product(request, product_id: int, payload: ProductCreateSchema):
    product = get_object_or_404(Product, id=product_id)
    if payload.category_id:
        product.category = get_object_or_404(Category, id=payload.category_id)
    for attr, value in payload.dict().items():
        if attr != "category_id":
            setattr(product, attr, value)
    product.save()
    return product


@router.delete("/{product_id}", response={204: None})
def delete_product(request, product_id: int):
    product = get_object_or_404(Product, id=product_id)
    product.delete()
    return 204
