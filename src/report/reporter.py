from django.contrib.auth.models import User

from questionaire.models import Question, Reply
from users.models import UserProfile


class Reporter:
    def get_answer_score(self, user, question):
        answer = Reply.objects.get(user=user, question=question).answer
        return answer.score

    def create_report(self, user_id):
        user = User.objects.get(id=user_id)
        user_profile = UserProfile.objects.get(user=user)
        if not user_profile.completed_questions >= Question.objects.count():
            return {"error": "questionaire not completed"}
        issue = self.get_answer_score(user, Question.objects.get(position=2))
        hair_overall = self.get_answer_score(user, Question.objects.get(position=3))
        split_ends = self.get_answer_score(user, Question.objects.get(position=7))
        daily_water = self.get_answer_score(user, Question.objects.get(position=22))
        hair_feels = self.get_answer_score(user, Question.objects.get(position=28))
        treatments = self.get_answer_score(user, Question.objects.get(position=32))
        enhancers = self.get_answer_score(user, Question.objects.get(position=33))
        hair_behaviour = self.get_answer_score(user, Question.objects.get(position=35))
        styling_hair = self.get_answer_score(user, Question.objects.get(position=36))
        apply_heat = self.get_answer_score(user, Question.objects.get(position=37))
        scalp_feeling = self.get_answer_score(user, Question.objects.get(position=39))
        scalp_flaky = self.get_answer_score(user, Question.objects.get(position=40))
        oily_scalp = self.get_answer_score(user, Question.objects.get(position=41))
        dry_scalp = self.get_answer_score(user, Question.objects.get(position=42))

        dry_score = (
            issue
            + split_ends
            + hair_overall
            + daily_water
            + hair_feels
            + scalp_flaky
            + dry_scalp
        )
        dry_score_max = 13
        dry_score_percentage = min(dry_score, dry_score_max) / dry_score_max

        damage_score = (
            split_ends
            + treatments
            + enhancers
            + hair_behaviour
            + styling_hair
            + apply_heat
        )
        damage_score_max = 15
        damage_score_percentage = min(damage_score, damage_score_max) / damage_score_max

        sensitivity_score = scalp_feeling
        sensitivity_score_max = 4.4
        sensitivity_percentage = (
            min(sensitivity_score, sensitivity_score_max) / sensitivity_score_max
        )

        sebum_oily_score = oily_scalp
        sebum_oily_score_max = 4.2
        sebum_oily_percentage = (
            min(sebum_oily_score, sebum_oily_score_max) / sebum_oily_score_max
        )

        sebum_dry_score = dry_scalp
        sebum_dry_score_max = 4.2
        sebum_dry_percentage = (
            min(sebum_dry_score, sebum_dry_score_max) / sebum_dry_score_max
        )

        dsc_percentage = sebum_dry_score / sebum_dry_score_max

        flake_score = scalp_flaky
        flake_score_max = 4.1
        flake_score_percentage = min(flake_score, flake_score_max) / flake_score_max
        return {
            "dry_score_percentage": {
                "value": round(dry_score_percentage * 100, 2),
                "description": "Dry",
            },
            "damage_score_percentage": {
                "value": round(damage_score_percentage * 100, 2),
                "description": "Damage",
            },
            "sensitivity_percentage": {
                "value": round(sensitivity_percentage * 100, 2),
                "description": "Sensitivity",
            },
            "sebum_oily_percentage": {
                "value": round(sebum_oily_percentage * 100, 2),
                "description": "Sebum Oily",
            },
            "sebum_dry_percentage": {
                "value": round(sebum_dry_percentage * 100, 2),
                "description": "Sebum Dry",
            },
            "dsc_percentage": {
                "value": round(dsc_percentage * 100, 2),
                "description": "Dry Scalp",
            },
            "flake_score_percentage": {
                "value": round(flake_score_percentage * 100, 2),
                "description": "Flake",
            },
        }
        # return {
        #     "dry_score_percentage": [
        #         round(dry_score_percentage * 100, 2),
        #         "Dry Score Percentage",
        #     ],
        #     "damage_score_percentage": [
        #         round(damage_score_percentage * 100, 2),
        #         "Damage Score Percentage",
        #     ],
        #     "sensitivity_percentage": [
        #         round(sensitivity_percentage * 100, 2),
        #         "Sensitivity Percentage",
        #     ],
        #     "sebum_oily_percentage": [
        #         round(sebum_oily_percentage * 100, 2),
        #         "Sebum Oily Percentage",
        #     ],
        #     "sebum_dry_percentage": [
        #         round(sebum_dry_percentage * 100, 2),
        #         "Sebum Dry Percentage",
        #     ],
        #     "dsc_percentage": [round(dsc_percentage * 100, 2), "DSC Percentage"],
        #     "flake_score_percentage": [
        #         round(flake_score_percentage * 100, 2),
        #         "Flake Score Percentage",
        #     ],
        # }
