.question-container {
  min-width: 65%;

  .question-label {
    font-size: 2rem;

    @media screen and (max-width: 480px) {
      font-size: 1.3rem;
    }
  }

  label {
    font-size: 1.5rem;
    margin: 5px 0;

    @media screen and (max-width: 480px) {
      font-size: 1.1rem;
    }
  }

  input[type='radio'] {
    height: 25px;
    width: 25px;
    border: none;
    transform: translateY(5px);
  }

  input[type='checkbox'] {
    height: 25px;
    width: 25px;
    border: none;
  }

  .question-btn-container {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
