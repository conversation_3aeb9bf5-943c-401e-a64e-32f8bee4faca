.about-banner {
  background: linear-gradient(
      0deg,
      rgba(76, 200, 242, 0.95),
      rgba(76, 200, 242, 0.3)
    ),
    url('../../../static/images/noroot.png');
  background-position: top;
  background-size: cover;
  height: 70vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  @media (max-width: 960px) {
    display: none;
  }

  .about-heading {
    color: #fff;
    text-align: center;
    h1 {
      font-size: 5.5rem;
    }

    h2 {
      font-size: 3.5rem;
      border-bottom: 2px solid #fff;
    }

    p {
      font-size: 1.5rem;
      width: 60%;
      margin: auto;
    }
  }
}

.about-section {
  margin: 40px auto;

  @media (max-width: 960px) {
    text-align: center;
  }

  .about-text {
    @media (max-width: 960px) {
      h4 {
        font-size: 1.8rem;

        @media (max-width: 960px) {
          font-size: 1.5rem;
          margin: 2rem 0;
        }
      }
      width: 100%;
    }
  }

  .about-img {
    width: 100%;

    @media (max-width: 960px) {
      display: none;
    }
  }
}

.section-heading {
  font-weight: 100;
}

.about-info {
  width: 100vw;
  h2 {
    text-align: center;
    margin: 30px auto 60px;
    font-size: 2.5rem;
    border-bottom: 2px solid $blue;
    max-width: 600px;
    padding-bottom: 3rem;

    @media (max-width: 960px) {
      font-size: 2rem;
      max-width: 300px;
      margin: 0 auto 0;
    }
  }

  h3 {
    color: #4cc7f2;
    font-size: 2.5rem;

    @media (max-width: 960px) {
      font-size: 2rem;
    }
  }

  p {
    margin-bottom: 30px;
    font-size: 1.5rem;
    font-weight: 100;
  }
}

.image-about {
  background: url('../../../static/images/photo-about-4.jpeg');
  background-position: center;
  height: 65vh;
  position: relative;

  @media (max-width: 960px) {
    display: none;
  }

  @media (max-width: 960px) {
    p {
      display: none;
    }
  }

  p {
    color: #fff;
    position: absolute;
    top: 80%;
    width: 600px;
  }
}

.more-info {
  width: 60vw;
  margin: auto;
  background-color: rgb(242, 238, 238);
  padding: 30px;

  @media (max-width: 960px) {
    width: 90vw;
  }
  h2 {
    text-align: center;
    color: #4cc7f2;
    font-size: 2.5rem;
    margin: 80px auto;
  }

  h4 {
    text-align: center;
    margin-bottom: 40px;
  }
  h5 {
    text-align: center;
    margin-bottom: 40px;
  }

  p {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 100;
    margin-bottom: 30px;
  }
}

.final-info {
  margin-top: 80px;
  width: 60vw;

  @media (max-width: 960px) {
    width: 90vw;
    text-align: center;
  }

  h2 {
    font-size: 2.2rem;
    font-weight: 100;
    text-align: center;
    margin-bottom: 60px;
  }

  h3 {
    color: #4cc7f2;
    font-size: 2.5rem;
    text-align: center;
    margin: 50px auto;

    @media (max-width: 960px) {
      font-size: 1.5rem;
      text-align: center;
      margin: 0;
      display: none;
    }
  }

  p {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 100;
  }
}

.idea {
  margin: 3rem auto;

  h3 {
    text-align: center;
    margin: 2rem auto;
    color: #4cc7f2;
    font-size: 2.5rem;
  }

  @media (max-width: 960px) {
    width: 100vw;
    text-align: center;
  }

  p {
    font-size: 1.5rem;
    font-weight: 100;
    @media (max-width: 960px) {
      text-align: center;
      width: 90vw;
      display: none;
    }
  }

  .image-container {
    max-width: 320px;
    border-radius: 5%;
    float: left;
    margin-right: 20px;

    @media (max-width: 960px) {
      display: none;
    }
  }
}

.qoute-sect {
  text-align: center;
  .qoute-heading {
    color: #4cc7f2;
    font-size: 1.3rem;
  }
}
