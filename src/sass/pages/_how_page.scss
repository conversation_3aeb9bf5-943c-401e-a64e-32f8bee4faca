.how-header {
  height: 50vh;
  background: linear-gradient(
      0deg,
      rgba(76, 200, 242, 0.3),
      rgba(76, 200, 242, 0.3)
    ),
    url('../../../static/images/how-page.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;

  h1 {
    color: $orange;
    font-size: 6rem;
    position: absolute;
    top: 50%;
    left: 10%;
  }
}

.steps {
  max-width: 60%;
  margin: 5rem auto;
  text-align: right;

  @media (max-width: 960px) {
    text-align: center;
    max-width: 100%;
    margin: 2rem 0;
  }
  h2 {
    font-size: 3rem;
  }

  ul {
    list-style: none;

    li {
      font-size: 1.5rem;
      font-weight: 700;
    }
  }

  .survey {
    margin: 15px 0;
    a {
      color: #fff;
      background-color: $orange;
      padding: 15px 40px;
      font-size: 1.2rem;
      margin-top: 40px;
    }
  }
}

.steps-details {
  max-width: 80%;
  margin: 5rem auto;

  @media (max-width: 960px) {
    max-width: 100%;
  }
  .steps-heading {
    h3 {
      font-size: 1.8rem;
      text-align: center;
      margin-bottom: 10px;
    }

    h4 {
      text-align: center;
    }
  }

  .questionnaire-sec {
    margin-top: 5rem;
    height: 550px;

    @media (max-width: 960px) {
      margin: 3rem 0;
      height: auto;
      text-align: center;

      .quest-image-container {
        display: none;
      }

      .gradient-btn {
        padding: 10px 15px;
        font-weight: 100;
      }
    }

    .quest-text {
      h5 {
        font-size: 2rem;
      }

      p {
        font-size: 1.3rem;
        margin-bottom: 40px;
      }

      a {
        font-size: 1.25rem;
      }
    }

    .quest-image-container {
      overflow: hidden;
      height: 100%;

      .quest-image {
        width: 80%;
      }
    }
  }
}
