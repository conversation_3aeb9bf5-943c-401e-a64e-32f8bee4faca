.recommendations-container {
  width: 100vw;
  overflow: hidden;
}

.rec-header {
  height: 40vh;
  background: center url('../../../static/images/rec-header.jpg');
  padding: 0;
}

.rec-heading {
  text-align: center;
  color: #fff;
  background-color: $darkBlue;
  height: 300px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  margin: 0 auto;

  h1 {
    font-size: 3.5rem;

    @media screen and (max-width: 960px) {
      font-size: 1.7rem;
    }
  }

  p {
    font-size: 1.5rem;

    @media screen and (max-width: 960px) {
      font-size: 1.2rem;
    }
  }

  .rec-desc {
    font-size: 1rem;

    @media (max-width: 480px) {
      font-size: 0.7rem;
    }
  }

  .error-message {
    background-color: $pink;
  }
}

.rec-section {
  max-width: 70%;
  margin: 5rem auto;
  display: flex;
  justify-content: center;
  align-items: center;

  .products-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin: 0 20px;
    padding: 0 60px;

    @media screen and (max-width: 760px) {
      flex-direction: column;
    }
  }
}

.product-item {
  width: 350px;

  margin: 0 30px;
  display: flex;
  flex-direction: column;
  transform: scale(0.95);
  transition: box-shadow 0.5s, transform 0.5s;

  cursor: pointer;

  &:hover {
    transform: scale(1);
    box-shadow: 5px 20px 30px rgba(0, 0, 0, 0.2);
  }

  .image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px;
    border-bottom: 1px solid $blue;

    .card-img-top {
      width: 100%;
      height: 400px;

      @media (max-width: 960px) {
        height: 240px;
        width: 100px;
      }
    }
  }

  .text-container {
    padding: 30px;
    display: flex;
    flex-direction: column;

    p {
      max-width: 100%;
      padding: 10px;
      font-weight: 100;
      font-weight: bold;
      text-transform: uppercase;
    }

    .icon-container {
      width: 100%;
      background-color: $darkBlue;
      height: 50px;
      position: relative;

      p {
        color: #fff;
        position: absolute;
        left: 0;
        text-transform: uppercase;
      }

      .shop {
        color: #fff;
        position: absolute;
        right: 20px;
        top: 15px;
        font-size: 1.3rem;
      }
    }

    // .right {
    //   position: relative;
    //   width: 25%;
    //   transition: background 0.5s;

    //   &:hover {
    //     background: $darkBlue;
    //   }

    //   .shop {
    //     color: #fff;
    //     position: absolute;
    //     top: 40px;
    //     right: 25px;
    //     font-size: 1.7rem;

    //     i {
    //       &:hover i {
    //         transform: translateY(15px);
    //       }
    //     }
    //   }
    // }
  }
}
