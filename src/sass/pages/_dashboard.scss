.dashboard-header {
  background: linear-gradient(
      0deg,
      rgba(76, 200, 242, 0.3),
      rgba(76, 200, 242, 0.3)
    ),
    url('../../../static/images/dasboard-background.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;

  height: 60vh;
}

.dashboard-section {
  padding: 10rem auto;
  background: linear-gradient(0deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
    url('../../../static/images/dashboard-2.jpg');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  height: 70vh;
  display: flex;

  justify-content: center;
  align-items: center;
}

.dashboard-container {
  width: 80%;
  margin: 0 auto;
  z-index: 100;
  display: flex;
  flex-wrap: wrap;

  @media screen and (max-width: 960px) {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 0;
  }
}

.item-container {
  width: 33.3%;
  height: 150px;
  padding: 0;
  margin-bottom: 10px;
  z-index: 100;

  @media screen and (max-width: 960px) {
    height: 100px;
    width: 100%;
  }

  .dashboard-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    margin: 0 5px;

    &:hover {
      transform: scale(1.1);
      box-shadow: 5px 20px 30px rgba(0, 0, 0, 0.2);
      transition: all 0.5s;
    }

    .dashboard-icon {
      font-size: 2.2rem;
      color: #fff;
      margin-bottom: 15px;
    }

    .item-heading {
      text-align: center;
      color: #fff;
      text-transform: uppercase;
      text-align: center;
      font-size: 1.3rem;

      @media screen and (max-width: 960px) {
        font-size: 1rem;
      }

      @media screen and (max-width: 480px) {
      }
    }
  }
}

.dash-1 {
  background-color: rgba(181, 82, 198, 0.6);
}

.dash-2 {
  background-color: rgba(76, 200, 242, 0.6);
}

.dash-3 {
  background-color: rgba(102, 58, 144, 0.6);
}
