.home-section-container {
  margin-top: 50px;
  .section-heading {
    text-align: center;
  }

  .section-sub-heading {
    text-align: center;
  }

  .section-text {
    text-align: center;
  }

  span {
    color: aqua;
  }

  .section-text {
    font-size: 1.3rem;

    span {
      font-weight: bold;
      color: black;
    }
  }
}

.home-section {
  margin: 60px auto;

  @media screen and(max-width: 960px) {
    display: flex;
    flex-direction: column;
  }
}

.left {
  @media screen and(max-width: 960px) {
    display: none;
  }

  h4 {
    font-size: 2.5rem;
    font-weight: bold;
  }

  p {
    font-size: 1.3rem;
    font-weight: 100;
    line-height: 2rem;
    margin-bottom: 30px;

    span {
      font-weight: bold;
      color: #000;
    }
  }

  ul {
    font-size: 1.3rem;
    list-style: none;
    margin-bottom: 30px;
  }
}

section {
  blockquote {
    text-align: center;

    p {
      font-size: 2rem;
      font-weight: 100;

      @media screen and(max-width: 480px) {
        font-size: 1.2rem;
      }
    }
  }

  blockquote p:before {
    content: open-quote;

    font-size: 100px;
    color: gray;
  }
}

.love-hair {
  background: url('../../../static/images/photo-section-quote.jpeg');
  background-position: center;
  height: 40vh;
  position: relative;

  p {
    position: absolute;
    top: 50%;
    left: 50%;
    color: #fff;
    font-size: 3rem;

    @media screen and(max-width: 480px) {
      font-size: 1.2rem;
    }
  }
}

.love-hair-text {
  margin-top: 3rem;
  max-width: 600px;

  @media screen and(max-width: 480px) {
    font-size: 1.2rem;
  }

  h3 {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 2rem;
  }

  p {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 100;
    line-height: 50px;
    margin-bottom: 3rem;

    @media screen and(max-width: 480px) {
      font-size: 1.1rem;
      line-height: 1.5;
    }
  }
}

.love-info {
  display: flex;
}

.love-thumbnail {
  border-radius: 50%;
  height: 95px;
  width: 80px;
}

.love-text {
  text-align: start;
  margin-left: 20px;
  max-width: 350px;

  p {
    text-align: start;
  }
}

.love-improve {
  max-width: 700px;
  margin: 20px auto;
  h4 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: bold;
  }

  p {
    text-align: center;
    font-size: 2rem;
    font-style: italic;
    font-weight: 100;
  }
}
