@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

$blue: rgb(76, 200, 242);
$darkBlue: #7b8ede;
$purple: #673a90;
$pink: #b452c6;
$orange: #dc934e;
$boxShadow: 1px 3px 5px rgba(0, 0, 0, 0.05);
$borderRadius: 5px;

*,
*::before,
*::after {
  font-family: 'Inter', sans-serif;
}

a {
  text-decoration: none;
}

.custom-btn {
  background: $darkBlue;
  border: 0;
  color: #fff;
  padding: 10px 15px;
  transition: all 250ms ease-in-out;
  width: 110px;
  display: inline-block;
  text-align: center;
}

.custom-btn:hover {
  background: #673a90;
  color: white;
}

.gradient-btn {
  background-image: linear-gradient(to right, $blue, $darkBlue);
  padding: 10px 20px;
  color: white;
  text-decoration: none;
  font-weight: bold;
  font-size: 1.6rem;
  transition: all 500ms ease-in-out;
  padding: 20px 40px;

  &:hover {
    background-image: linear-gradient(to right, $darkBlue, $blue);
    color: white;
  }
}

@import './components/_header';
@import './components/_banner';
@import './components/_steps-intro';
@import './components/_discover-your-hair';
@import './components//footer';
@import './pages/_register_page';
@import './pages/_dashboard';
@import './pages/_profile';
@import './pages/_recommendations';
@import './pages/_how_page';
@import './pages/_home';
@import './pages/_report';
@import './pages/_about';
@import './pages/_condition';
@import './pages/_products';
@import './pages/_questions';
@import './pages/_hair_id';
