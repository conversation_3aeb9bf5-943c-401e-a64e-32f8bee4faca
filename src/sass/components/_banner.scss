.banner {
  /* background: #7B8EDE; */
  background: linear-gradient(
      180deg,
      rgba(76, 200, 242, 0.95),
      rgba(4, 76, 134, 0.7)
    ),
    url('../../../static/images/dasboard-background.jpg');
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;

  @media screen and (max-width: 480px) {
    width: 100%;
    margin: 0 auto;
  }
}

.text-center {
  color: #fff;

  h1 {
    font-size: 6.5rem;
    border-bottom: 4px solid #fff;

    @media screen and (max-width: 480px) {
      font-size: 1.8rem;
      border-bottom: 1px solid #fff;
      font-weight: bold;
      padding-bottom: 20px;
    }
  }

  h2 {
    margin: 30px 0;
    font-size: 2.5rem;

    @media screen and (max-width: 480px) {
      font-size: 1.1rem;
    }
  }

  p {
    font-size: 1.5rem;
    margin-bottom: 60px;

    @media screen and (max-width: 480px) {
      font-size: 0.8rem;
    }
  }

  a {
    @media screen and (max-width: 480px) {
      padding: 10px 20px;
    }
  }
}
