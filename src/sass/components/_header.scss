.site-header {
  width: 100%;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 5rem;
  position: sticky;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #fff;
  z-index: 100;
}

.site-header .logo img {
  width: 210px;
}

nav a {
  color: $darkBlue;
  margin-left: 20px;
  font-size: 1.2rem;

  transition: all 250ms ease-in-out;

  svg {
    font-size: 1.8rem;
  }
}

nav a:hover {
  color: $blue;
}

.burger {
  position: fixed;
  top: 3rem;
  bottom: 50%;
  right: 20px;
  width: 35px;
  height: 35px;
  cursor: pointer;
  transition: all 500ms ease-in-out;
  z-index: 40;
  background-color: #fff;
  &:hover {
    height: 30px;
  }
}

.burger-bar {
  background-color: $darkBlue;
  display: block;
  width: 100%;
  height: 2px;
  border-radius: 3px;

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: $darkBlue;
    border-radius: 3px;
    transition: all 500ms ease-in-out;
  }

  &::before {
    transform: translateY(-10px);
  }

  &::after {
    transform: translateY(10px);
  }
}

.burger.open .burger-bar {
  background: transparent;
}

.burger.open .burger-bar::before {
  transform: rotate(45deg);
  background: $darkBlue;
}

.burger.open .burger-bar::after {
  transform: rotate(-45deg);
  background: $darkBlue;
}

@media (min-width: 960px) {
  .burger {
    display: none;
  }
}

@media (max-width: 960px) {
  nav {
    background: transparent;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-top: 2rem;
    background-color: #fff;
    position: fixed;
    right: -250px;
    top: 90px;
    width: 250px;
    height: 100%;

    z-index: 5;
    transition: all 350ms cubic-bezier(0.075, 0.82, 0.165, 1);

    a {
      padding: 20px 0;
    }

    .custom-btn {
      margin: 20px 0;
    }
  }

  nav.active {
    right: 0;
  }

  .site-header .logo img {
    width: 180px;
    position: absolute;
    left: 20px;
    top: 10px;
  }
}
