absl-py==1.0.0
alabaster==0.7.12
argon2-cffi==21.3.0
argon2-cffi-bindings==21.2.0
arrow==1.2.2
astroid==2.9.3
astunparse==1.6.3
atomicwrites==1.4.0
attrs==21.4.0
autopep8==1.6.0
Babel==2.9.1
backcall==0.2.0
batchgenerators==0.23
binaryornot==0.4.4
black==24.3.0
bleach==4.1.0
cachetools==5.0.0
certifi==2024.6.2
cffi==1.15.0
chardet==4.0.0
charset-normalizer==2.0.12
click==8.0.4
cloudpickle==2.0.0
cookiecutter==2.1.1
cryptography==42.0.4
cycler==0.11.0
debugpy==1.5.1
decorator==5.1.1
defusedxml==0.7.1
dicom2nifti==2.3.0
diff-match-patch==20200713
docutils==0.17.1
entrypoints==0.4
flake8==4.0.1
flatbuffers==2.0
fonttools==4.43.0
future==0.18.3
gast==0.5.3
google-auth==2.6.0
google-auth-oauthlib==0.4.6
google-pasta==0.2.0
grpcio==1.53.2
h5py==3.6.0
hiddenlayer @ git+https://github.com/FabianIsensee/hiddenlayer.git@4b98f9e5cccebac67368f02b95f4700b522345b1
idna==3.7
imageio==2.16.0
imagesize==1.3.0
importlib-metadata==4.11.1
importlib-resources==5.4.0
inflection==0.5.1
intervaltree==3.1.0
ipykernel==6.9.1
ipython==8.10.0
ipython-genutils==0.2.0
ipywidgets==7.7.0
isort==5.10.1
jedi==0.18.1
jeepney==0.7.1
jellyfish==0.9.0
Jinja2==3.1.4
jinja2-time==0.2.0
joblib==1.2.0
jsonschema==4.4.0
jupyter==1.0.0
jupyter-client==7.1.2
jupyter-console==6.4.3
jupyter-core==4.11.2
jupyterlab-pygments==0.1.2
jupyterlab-widgets==1.1.0
keras==2.8.0
Keras-Preprocessing==1.1.2
keyring==23.5.0
kiwisolver==1.3.2
lazy-object-proxy==1.7.1
libclang==13.0.0
linecache2==1.0.0
Markdown==3.3.6
MarkupSafe==2.1.0
matplotlib==3.5.1
matplotlib-inline==0.1.3
mccabe==0.6.1
MedPy==0.4.0
mistune==0.8.4
mkl-fft==1.3.1
mkl-random @ file:///tmp/build/80754af9/mkl_random_1626186064646/work
mkl-service==2.4.0
mypy-extensions==0.4.3
nbclient==0.5.11
nbconvert==6.5.1
nbformat==5.1.3
nest-asyncio==1.5.4
networkx==2.6.3
nibabel==3.2.2
notebook==6.4.12
numpy==1.22.2
numpydoc==1.2
oauthlib==3.2.2
olefile @ file:///Users/<USER>/demo/mc3/conda-bld/olefile_1629805411829/work
opencv-python==********
opt-einsum==3.3.0
packaging==21.3
pandas==1.4.1
pandocfilters==1.5.0
parso==0.8.3
pathspec==0.9.0
pexpect==4.8.0
pickleshare==0.7.5
Pillow==10.3.0
platformdirs==2.5.1
plotly==5.6.0
pluggy==1.0.0
poyo==0.5.0
prettytable==3.2.0
prometheus-client==0.14.1
prompt-toolkit==3.0.28
protobuf==3.19.5
psutil==5.9.0
ptyprocess==0.7.0
pyasn1==0.4.8
pyasn1-modules==0.2.8
pycodestyle==2.8.0
pycparser==2.21
pydicom==2.2.2
pydocstyle==6.1.1
pyflakes==2.4.0
Pygments==2.15.0
pylint==2.12.2
pyls-spyder==0.4.0
pyparsing==3.0.7
PyQt5==5.12.3
PyQt5-sip==12.9.1
PyQtWebEngine==5.12.1
pyrsistent==0.18.1
python-dateutil==2.8.2
python-lsp-black==1.1.0
python-lsp-jsonrpc==1.0.0
python-lsp-server==1.3.3
python-slugify==6.0.1
pytz==2021.3
PyWavelets==1.2.0
pyxdg==0.27
pyzmq==22.3.0
QDarkStyle==3.0.2
qstylizer==0.2.1
QtAwesome==1.1.1
qtconsole==5.2.2
QtPy==2.0.1
requests==2.32.2
requests-oauthlib==1.3.1
rope==0.22.0
rsa==4.8
Rtree==0.9.7
scikit-image==0.19.2
scikit-learn==1.5.0
scipy==1.10.0
seaborn==0.11.2
SecretStorage==3.3.1
Send2Trash==1.8.0
SimpleITK==2.1.1
six @ file:///tmp/build/80754af9/six_1644875935023/work
sklearn==0.0
snowballstemmer==2.2.0
sortedcontainers==2.4.0
Sphinx==4.4.0
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==2.0.0
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.5
spyder==5.2.2
spyder-kernels==2.2.1
statannot==0.2.3
tabulate==0.8.9
tenacity==8.0.1
tensorboard==2.8.0
tensorboard-data-server==0.6.1
tensorboard-plugin-wit==1.8.1
tensorflow-io-gcs-filesystem==0.24.0
termcolor==1.1.0
terminado==0.13.3
testpath==0.5.0
text-unidecode==1.3
textdistance==4.2.2
tf-estimator-nightly==2.8.0.dev2021122109
threadpoolctl==3.1.0
three-merge==0.1.1
tifffile==2022.2.9
tinycss2==1.1.1
toml==0.10.2
tomli==2.0.1
torch==1.13.1
torchaudio==0.8.2
torchsummary==1.5.1
torchvision==0.11.3
tornado==6.4.1
tqdm==4.66.3
traceback2==1.4.0
traitlets==5.1.1
typing_extensions==4.1.1
ujson==5.4.0
unittest2==1.1.0
urllib3==1.26.19
watchdog==2.1.6
wcwidth==0.2.5
webencodings==0.5.1
# Werkzeug==2.0.3
Werkzeug==3.0.3
widgetsnbextension==3.6.0
wrapt==1.13.3
wurlitzer==3.0.2
yapf==0.32.0
zipp==3.19.1
