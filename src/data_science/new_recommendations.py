import itertools

# Standard library imports
import pickle  # If needed
import warnings  # If needed
from typing import Any

# Third-party imports
import numpy as np  # If needed
import pandas as pd  # If needed

# Django imports
from django.contrib.auth.models import User
from loguru import logger
from numpy import ndarray, dtype
from openpyxl import load_workbook

# Local application imports
from questionaire.models import Question
from users.models import UserProfile
from .condition_score_card import ConditionScores

warnings.filterwarnings("ignore")


# get_values is a function that must be run on github sheets.
def get_values(sheet):
    results = []
    for row in sheet.values:
        if not any(value for value in row):
            continue
        else:
            results.append(row)
    return results


# noinspection PyPep8Naming
class NewRecommender:
    # noinspection PyPep8Naming
    @staticmethod
    def get_recommendations(id):
        # importing products dataset what are still in spreadsheets form (for now)
        product_data = load_workbook(
            filename="src/data_science/product_data.xlsx", data_only=True
        )
        shampoo = get_values(product_data["Shampoo"])
        conditioner = get_values(product_data["Conditioners"])
        shamp_info = get_values(product_data["Shampoo_o"])
        cond_info = get_values(product_data["Conditioners_o"])

        # creating the dataset used for condition score with the variables created with answers of the questionnaire
        user = User.objects.get(id=id)
        user_profile = UserProfile.objects.get(user=user)

        goal = Question.objects.get(position=1)
        issue = Question.objects.get(position=2)
        hair_overall = Question.objects.get(position=3)
        hair_type = Question.objects.get(position=6)
        split_ends = Question.objects.get(position=7)
        density = Question.objects.get(position=8)
        hair_texture = Question.objects.get(position=9)
        where_workout = Question.objects.get(position=28)
        hair_feels = Question.objects.get(position=30)
        washing_frequency = Question.objects.get(position=31)
        treatments = Question.objects.get(position=34)
        enhancers = Question.objects.get(position=35)
        porosity = Question.objects.get(position=37)
        styling_hair = Question.objects.get(position=38)
        scalp_feeling = Question.objects.get(position=41)
        scalp_flaky = Question.objects.get(position=42)
        scalp_oily = Question.objects.get(position=43)
        scalp_dry = Question.objects.get(position=44)
        preferences = Question.objects.get(position=45)

        health_condition = Question.objects.get(position=14)
        fever = Question.objects.get(position=15)

        allergies = Question.objects.get(position=15)
        prescription = Question.objects.get(position=15)
        iron_def = Question.objects.get(position=18)
        diet = Question.objects.get(position=20)
        supplementation = Question.objects.get(position=21)
        skip_meals = Question.objects.get(position=22)
        general_health = Question.objects.get(position=25)
        fitness = Question.objects.get(position=26)
        workout_freq = Question.objects.get(position=27)
        stress = Question.objects.get(position=29)
        water_damage = Question.objects.get(position=31)
        product_usage = Question.objects.get(position=33)
        heat_freq = Question.objects.get(position=39)
        routine_suggestion = Question.objects.get(position=40)

        # creating the dataset used for condition score with the variables created with answers of the questionnaire

        q_answers = [
            (
                "Customer_ID",
                "Goal ",
                "Issue ",
                "Hair Type ",
                "Hair Texture",
                "hair_feels",
                "treatments",
                "washing_frequency",
                "enhancers",
                "styling_hair",
                "split_ends",
                "health_condition",
                "fever",
                "allergies",
                "prescription",
                "iron_def",
                "diet",
                "supplementation",
                "skip_meals",
                "general_health",
                "fitness",
                "workout_freq",
                "where_workout",
                "stress",
                "water_damage",
                "product_usage",
                "heat_freq",
                "scalp_feeling",
                "scalp_flaky",
                "scalp_oily",
                "scalp_dry",
                "preferences",
                "routine_suggestion",
            ),
            (
                user.id,
                goal,
                issue,
                user_profile,
                hair_type,
                hair_texture,
                hair_feels,
                treatments,
                washing_frequency,
                enhancers,
                styling_hair,
                split_ends,
                health_condition,
                fever,
                allergies,
                prescription,
                iron_def,
                diet,
                supplementation,
                skip_meals,
                general_health,
                fitness,
                workout_freq,
                where_workout,
                stress,
                water_damage,
                product_usage,
                heat_freq,
                scalp_feeling,
                scalp_flaky,
                scalp_oily,
                scalp_dry,
                preferences,
                routine_suggestion,
            ),
        ]

        # defining a DataFrame/Dataset with those answers
        d_a = pd.DataFrame(data=q_answers)

        # Convert to a DataFrame
        df__s = pd.DataFrame(shampoo)
        df__c = pd.DataFrame(conditioner)
        ds__i = pd.DataFrame(shamp_info)
        dc__i = pd.DataFrame(cond_info)

        # line 1 of spreadsheets contains the name of the columns
        header_row1 = df__s.iloc[0]
        header_row2 = df__c.iloc[0]
        header_row3 = ds__i.iloc[0]
        header_row4 = dc__i.iloc[0]

        # changing the name of the columns in the DataFrames
        ds = pd.DataFrame(df__s.values[1:], columns=header_row1)
        dc = pd.DataFrame(df__c.values[1:], columns=header_row2)
        ds_i = pd.DataFrame(ds__i.values[1:], columns=header_row3)
        dc_i = pd.DataFrame(dc__i.values[1:], columns=header_row4)

        # HERE SHOULD IMPORT THE OUTPUT FROM THE CONDITION SCORE FROM CLASS

        conditional_score = ConditionScores()
        # BUG: fix issue
        """

        """
        dcs = conditional_score.get_condition_scores(user)

        # getting the highest scores within the DSC dataset
        dcs["highest_issue"] = dcs.idxmax(axis=1)

        # merging answers dataset with ConditionScore dataset
        d_a = pd.merge(
            d_a,
            dcs[["customer_ID", "highest_issue"]],
            left_on="Customer_ID",
            right_on="customer_ID",
            how="left",
        )

        # organising the User Rating for product
        dc["User_Rating"] = np.where(
            dc["User Rating"].str.contains("Good"),
            1,
            np.where(dc["User Rating"].str.contains("High"), 2, 0),
        )

        ds["User_Rating"] = np.where(
            ds["User Rating"].str.contains("Good"),
            1,
            np.where(ds["User Rating"].str.contains("High"), 2, 0),
        )

        # renaming columns
        ds = ds.rename(
            columns={
                "Hair Type": "Hair_Type",
                "Product ": "Shampoo_name",
                "CATEGORY": "Category",
            }
        )
        dc = dc.rename(
            columns={
                "Hair Type": "Hair_Type",
                "Product ": "Conditioner_name",
                "CATEGORY": "Category",
            }
        )
        ds_i = ds_i.rename(
            columns={
                "Hair Type": "Hair_Type",
                "Product ": "Shampoo_name",
                "CATEGORY": "Category",
            }
        )
        dc_i = dc_i.rename(
            columns={
                "Hair Type": "Hair_Type",
                "Product ": "Conditioner_name",
                "CATEGORY": "Category",
            }
        )

        # reorganizing the product dataset

        ds = ds.merge(ds_i, how="left", on=["Shampoo_name"])
        ds = ds[
            [
                "shampooID",
                "Category",
                "Shampoo_name",
                "User Rating",
                "Hair_Type",
                "Porosity",
                "Texture ",
                "Hair Goals",
                "Hair Issues",
                "User_Rating",
                "Insights	",
                "Protein / Moisture ",
                "Internal Insights ",
            ]
        ]
        ds["prod_info"] = (
            ds["Insights	"]
            + " , "
            + ds["Protein / Moisture "]
            + " , "
            + ds["Category"]
            + " , "
            + ds["Shampoo_name"]
        )

        dc = dc.merge(dc_i, how="left", on=["Conditioner_name"])
        dc = dc[
            [
                "shampooID",
                "Category",
                "Conditioner_name",
                "User Rating",
                "Hair_Type",
                "Porosity",
                "Texture ",
                "Hair Goals",
                "Hair Issues",
                "User_Rating",
                "Insights ",
                "Protein / Moisture ",
                "Internal Insights ",
            ]
        ]
        dc["prod_info"] = (
            dc["Insights	"]
            + " , "
            + dc["Protein / Moisture "]
            + " , "
            + dc["Category"]
            + " , "
            + dc["Conditioner_name"]
        )

        # Classification part of the Recomendation System
        # organising and replacing

        # Hair Type
        # NEEDS TO CHECK THE D_A FORMAT THAT THE USER INSERT INTO THE ANSWER, IT IS A OPEN QUESTION NOT A MULTIPLE CHOICE ON HAIR TYPE QUESTION ONLY

        d_a["Hair_TypeID"] = np.where(
            d_a["hair_type"].str.contains("Type 2a|2A"),
            1,
            np.where(
                d_a["hair_type"].str.contains("Type 2b|2B"),
                2,
                np.where(
                    d_a["hair_type"].str.contains("Type 2c|2C"),
                    3,
                    np.where(
                        d_a["hair_type"].str.contains("Type 3a|3A"),
                        4,
                        np.where(
                            d_a["hair_type"].str.contains("Type 3b|3B"),
                            5,
                            np.where(
                                d_a["hair_type"].str.contains("Type 3c|3C"),
                                6,
                                np.where(
                                    d_a["hair_type"].str.contains("Type 4a|4A"),
                                    7,
                                    np.where(
                                        d_a["hair_type"].str.contains("Type 4b|4B"),
                                        8,
                                        np.where(
                                            d_a["hair_type"].str.contains("Type 4c|4C"),
                                            9,
                                            0,
                                        ),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )
        ds["Hair_TypeID"] = np.where(
            ds["Hair_Type"].str.contains("2A"),
            1,
            np.where(
                ds["Hair_Type"].str.contains("2B"),
                2,
                np.where(
                    ds["Hair_Type"].str.contains("2C"),
                    3,
                    np.where(
                        ds["Hair_Type"].str.contains("3A"),
                        4,
                        np.where(
                            ds["Hair_Type"].str.contains("3B"),
                            5,
                            np.where(
                                ds["Hair_Type"].str.contains("3C"),
                                6,
                                np.where(
                                    ds["Hair_Type"].str.contains("4A"),
                                    7,
                                    np.where(
                                        ds["Hair_Type"].str.contains("4B"),
                                        8,
                                        np.where(
                                            ds["Hair_Type"].str.contains("4C"), 9, 0
                                        ),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )
        dc["Hair_TypeID"] = np.where(
            dc["Hair_Type"].str.contains("2A"),
            1,
            np.where(
                dc["Hair_Type"].str.contains("2B"),
                2,
                np.where(
                    dc["Hair_Type"].str.contains("2C"),
                    3,
                    np.where(
                        dc["Hair_Type"].str.contains("3A"),
                        4,
                        np.where(
                            dc["Hair_Type"].str.contains("3B"),
                            5,
                            np.where(
                                dc["Hair_Type"].str.contains("3C"),
                                6,
                                np.where(
                                    dc["Hair_Type"].str.contains("4A"),
                                    7,
                                    np.where(
                                        dc["Hair_Type"].str.contains("4B"),
                                        8,
                                        np.where(
                                            dc["Hair_Type"].str.contains("4C"), 9, 0
                                        ),
                                    ),
                                ),
                            ),
                        ),
                    ),
                ),
            ),
        )
        # TextureID
        ds["TextureID"] = np.where(
            ds["Texture "].str.contains("Fine|Thin"),
            10,
            np.where(
                ds["Texture "].str.contains("Medium"),
                11,
                np.where(ds["Texture "].str.contains("Thick"), 12, 0),
            ),
        )
        d_a["TextureID"] = np.where(
            d_a["Hair Texture"].str.contains("Fine|Thin"),
            10,
            np.where(
                d_a["Hair Texture"].str.contains("Medium"),
                11,
                np.where(d_a["Hair Texture"].str.contains("Thick"), 12, 0),
            ),
        )
        dc["TextureID"] = np.where(
            dc["Texture "].str.contains("Fine|Thin"),
            10,
            np.where(
                dc["Texture "].str.contains("Medium"),
                11,
                np.where(dc["Texture "].str.contains("Thick"), 12, 0),
            ),
        )

        # PorosityID
        # CHECK IF THE QUESTIONNAIRE BRINGS HERE THE WAY IT IS IMPLEMENTED ON d_a DATASET
        d_a["PorosityID"] = np.where(
            d_a["hair_behaviour"].str.contains("A -Repels moisture when wet"),
            13,
            np.where(
                d_a["hair_behaviour"].str.contains("B - Its bouncy and elastic"),
                14,
                np.where(
                    d_a["hair_behaviour"].str.contains("C- Easily absorbs water"), 15, 0
                ),
            ),
        )
        ds["PorosityID"] = np.where(
            ds["Porosity"].str.contains("Low"),
            13,
            np.where(
                ds["Porosity"].str.contains("Medium"),
                14,
                np.where(ds["Porosity"].str.contains("High"), 15, 0),
            ),
        )
        dc["PorosityID"] = np.where(
            dc["Porosity"].str.contains("Low"),
            13,
            np.where(
                dc["Porosity"].str.contains("Medium"),
                14,
                np.where(dc["Porosity"].str.contains("High"), 15, 0),
            ),
        )
        # GoalsID
        d_a["GoalsID"] = np.where(
            d_a["Goal "].str.contains("Growth"),
            16,
            np.where(
                d_a["Goal "].str.contains("Healthy"),
                17,
                np.where(
                    d_a["Goal "].str.contains("Moisture"),
                    18,
                    np.where(d_a["Goal "].str.contains("Ultra"), 19, 0),
                ),
            ),
        )
        ds["GoalsID"] = np.where(
            ds["Hair Goals"].str.contains("Growth"),
            16,
            np.where(
                ds["Hair Goals"].str.contains("Healthy"),
                17,
                np.where(
                    ds["Hair Goals"].str.contains("Moisture"),
                    18,
                    np.where(ds["Hair Goals"].str.contains("Ultra"), 19, 0),
                ),
            ),
        )
        dc["GoalsID"] = np.where(
            dc["Hair Goals"].str.contains("Growth"),
            16,
            np.where(
                dc["Hair Goals"].str.contains("Healthy"),
                17,
                np.where(
                    dc["Hair Goals"].str.contains("Moisture"),
                    18,
                    np.where(dc["Hair Goals"].str.contains("Ultra"), 19, 0),
                ),
            ),
        )
        # IssueID
        d_a["IssuesID"] = np.where(
            d_a["Issue "].str.contains("Scalp dryness"),
            20,
            np.where(
                d_a["Issue "].str.contains("breakage|Split"),
                21,
                np.where(
                    d_a["Issue "].str.contains("Thinning"),
                    22,
                    np.where(d_a["Issue "].str.contains("Not me"), 23, 0),
                ),
            ),
        )
        ds["IssuesID"] = np.where(
            ds["Hair Issues"].str.contains("Scalp dryness"),
            20,
            np.where(
                ds["Hair Issues"].str.contains("Breakage|Shedding"),
                21,
                np.where(
                    ds["Hair Issues"].str.contains("Thinning"),
                    22,
                    np.where(ds["Hair Issues"].str.contains("Not me"), 23, 0),
                ),
            ),
        )
        dc["IssuesID"] = np.where(
            dc["Hair Issues"].str.contains("Scalp dryness"),
            20,
            np.where(
                dc["Hair Issues"].str.contains("Breakage|Shedding"),
                21,
                np.where(
                    dc["Hair Issues"].str.contains("Thinning"),
                    22,
                    np.where(dc["Hair Issues"].str.contains("Not me"), 23, 0),
                ),
            ),
        )
        # HairFeelsID
        d_a["HairFeelsID"] = np.where(
            d_a["hair_feels"].str.contains("Weighed down"),
            24,
            np.where(
                d_a["hair_feels"].str.contains("Coarse and dry"),
                25,
                np.where(
                    d_a["hair_feels"].str.contains("Limp"),
                    26,
                    np.where(
                        d_a["hair_feels"].str.contains("Not responding to anything"),
                        27,
                        np.where(
                            d_a["hair_feels"].str.contains("None of these / Normal"),
                            28,
                            0,
                        ),
                    ),
                ),
            ),
        )
        ds["HairFeelsID"] = np.where(
            ds["prod_info"].str.contains("Weighed down"),
            24,
            np.where(
                ds["prod_info"].str.contains("Coarse and dry"),
                25,
                np.where(
                    ds["prod_info"].str.contains("Limp"),
                    26,
                    np.where(
                        ds["prod_info"].str.contains("Not responding to anything"),
                        27,
                        np.where(
                            ds["prod_info"].str.contains("None of these / Normal"),
                            28,
                            0,
                        ),
                    ),
                ),
            ),
        )
        dc["HairFeelsID"] = np.where(
            dc["prod_info"].str.contains("Weighed down"),
            24,
            np.where(
                dc["prod_info"].str.contains("Coarse and dry"),
                25,
                np.where(
                    dc["prod_info"].str.contains("Limp"),
                    26,
                    np.where(
                        dc["prod_info"].str.contains("Not responding to anything"),
                        27,
                        np.where(
                            dc["prod_info"].str.contains("None of these / Normal"),
                            28,
                            0,
                        ),
                    ),
                ),
            ),
        )
        # WashingFrequencyID
        d_a["WashingFrequencyID"] = np.where(
            d_a["washing_frequency"].str.contains("More than once a week"),
            29,
            np.where(
                d_a["washing_frequency"].str.contains("Once a week"),
                30,
                np.where(
                    d_a["washing_frequency"].str.contains("Twice a month"),
                    31,
                    np.where(
                        d_a["washing_frequency"].str.contains("About once a month"),
                        32,
                        0,
                    ),
                ),
            ),
        )
        ds["WashingFrequencyID"] = np.where(
            ds["prod_info"].str.contains("More than once a week"),
            29,
            np.where(
                ds["prod_info"].str.contains("Weekly|weekly"),
                30,
                np.where(
                    ds["prod_info"].str.contains("Twice a month"),
                    31,
                    np.where(ds["prod_info"].str.contains("Monthly|monthly"), 32, 0),
                ),
            ),
        )
        dc["WashingFrequencyID"] = np.where(
            dc["prod_info"].str.contains("More than once a week"),
            29,
            np.where(
                dc["prod_info"].str.contains("Weekly|weekly"),
                30,
                np.where(
                    dc["prod_info"].str.contains("Twice a month"),
                    31,
                    np.where(dc["prod_info"].str.contains("Monthly|monthly"), 32, 0),
                ),
            ),
        )
        # TreatmentID
        d_a["TreatmentID"] = np.where(
            d_a["treatments"].str.contains("Relaxers"),
            33,
            np.where(
                d_a["treatments"].str.contains("Perm"),
                34,
                np.where(
                    d_a["treatments"].str.contains("Keratin"),
                    35,
                    np.where(
                        d_a["treatments"].str.contains("Bleach"),
                        36,
                        np.where(
                            d_a["treatments"].str.contains("Colour / Dyed"),
                            37,
                            np.where(
                                d_a["treatments"].str.contains("No not me"), 38, 0
                            ),
                        ),
                    ),
                ),
            ),
        )
        ds["TreatmentID"] = np.where(
            ds["prod_info"].str.contains("Relaxed|relaxed"),
            33,
            np.where(
                ds["prod_info"].str.contains("Permed|permed"),
                34,
                np.where(
                    ds["prod_info"].str.contains("Keratin"),
                    35,
                    np.where(
                        ds["prod_info"].str.contains("Bleached|bleached"),
                        36,
                        np.where(
                            ds["prod_info"].str.contains("Coloured|coloured|Dyed|dyed"),
                            37,
                            np.where(ds["prod_info"].str.contains("No not me"), 38, 0),
                        ),
                    ),
                ),
            ),
        )
        dc["TreatmentID"] = np.where(
            dc["prod_info"].str.contains("Relaxed|relaxed"),
            33,
            np.where(
                dc["prod_info"].str.contains("Permed|permed"),
                34,
                np.where(
                    dc["prod_info"].str.contains("Keratin"),
                    35,
                    np.where(
                        dc["prod_info"].str.contains("Bleached|bleached"),
                        36,
                        np.where(
                            dc["prod_info"].str.contains("Coloured|coloured|Dyed|dyed"),
                            37,
                            np.where(dc["prod_info"].str.contains("No not me"), 38, 0),
                        ),
                    ),
                ),
            ),
        )
        # StyleID
        d_a["StyleID"] = np.where(
            d_a["styling_hair"].str.contains(
                "Blow dryer|Straightening iron|Curling iron/wand"
            ),
            41,
            np.where(d_a["styling_hair"].str.contains("No stylers for me"), 42, 0),
        )
        ds["StyleID"] = np.where(
            ds["prod_info"].str.contains("Heat protector|heat protector"),
            41,
            np.where(ds["prod_info"].str.contains("No stylers for me"), 42, 0),
        )
        dc["StyleID"] = np.where(
            dc["prod_info"].str.contains("Heat protector|heat protector"),
            41,
            np.where(dc["prod_info"].str.contains("No stylers for me"), 42, 0),
        )
        # SplitEnds
        d_a["split_endsID"] = np.where(
            d_a["split_ends"].str.contains("Yes"),
            43,
            np.where(d_a["split_ends"].str.contains("No"), 44, 0),
        )
        ds["split_endsID"] = np.where(
            ds["prod_info"].str.contains("Yes"),
            43,
            np.where(ds["prod_info"].str.contains("No"), 44, 0),
        )
        dc["split_endsID"] = np.where(
            dc["prod_info"].str.contains("Yes"),
            43,
            np.where(dc["prod_info"].str.contains("No"), 44, 0),
        )

        # allergies
        d_a["alergiesID"] = np.where(
            d_a["allergies"].str.contains("Lactose|dairy"),
            55,
            np.where(
                d_a["allergies"].str.contains("Caffeine"),
                56,
                np.where(
                    d_a["allergies"].str.contains("Nuts"),
                    57,
                    np.where(d_a["allergies"].str.contains("No not me"), 58, 0),
                ),
            ),
        )
        ds["alergiesID"] = np.where(
            d_a["prod_info"].str.contains("No Lactose| No dairy"),
            55,
            np.where(
                d_a["prod_info"].str.contains("No Caffeine"),
                56,
                np.where(
                    d_a["prod_info"].str.contains("No Nuts"),
                    57,
                    np.where(d_a["prod_info"].str.contains("No not me"), 58, 0),
                ),
            ),
        )
        dc["alergiesID"] = np.where(
            d_a["prod_info"].str.contains("No Lactose|No dairy"),
            55,
            np.where(
                d_a["prod_info"].str.contains("No Caffeine"),
                56,
                np.where(
                    d_a["prod_info"].str.contains("No Nuts"),
                    57,
                    np.where(d_a["prod_info"].str.contains("No not me"), 58, 0),
                ),
            ),
        )
        # where_workout
        d_a["where_workoutID"] = np.where(
            d_a["where_workout"].str.contains("Indoors/gym|Outdoors/Boot camps"),
            82,
            np.where(
                d_a["where_workout"].str.contains("Chlorine swimming pool"),
                83,
                np.where(
                    d_a["where_workout"].str.contains("I dont exercise often"), 84, 0
                ),
            ),
        )
        ds["where_workoutID"] = np.where(
            d_a["prod_info"].str.contains("Indoors/gym|Outdoors/Boot camps"),
            82,
            np.where(
                d_a["prod_info"].str.contains("Chlorine"),
                83,
                np.where(d_a["prod_info"].str.contains("I dont exercise often"), 84, 0),
            ),
        )
        dc["where_workoutID"] = np.where(
            d_a["prod_info"].str.contains("Indoors/gym|Outdoors/Boot camps"),
            82,
            np.where(
                d_a["prod_info"].str.contains("Chlorine"),
                83,
                np.where(d_a["prod_info"].str.contains("I dont exercise often"), 84, 0),
            ),
        )
        # scalp_feeling
        d_a["scalp_feelingID"] = np.where(
            d_a["scalp_feeling"].str.contains("A little bit tight"),
            93,
            np.where(
                d_a["scalp_feeling"].str.contains("Tight & Itchy"),
                94,
                np.where(
                    d_a["scalp_feeling"].str.contains("Itchy & painful"),
                    95,
                    np.where(
                        d_a["scalp_feeling"].str.contains("Itchy"),
                        96,
                        np.where(
                            d_a["scalp_feeling"].str.contains("Sensitive"),
                            97,
                            np.where(
                                d_a["scalp_feeling"].str.contains("None of the above"),
                                98,
                                0,
                            ),
                        ),
                    ),
                ),
            ),
        )
        ds["scalp_feelingID"] = np.where(
            d_a["prod_info"].str.contains("A little bit tight"),
            93,
            np.where(
                d_a["prod_info"].str.contains("Tight & Itchy"),
                94,
                np.where(
                    d_a["prod_info"].str.contains("Itchy & painful"),
                    95,
                    np.where(
                        d_a["prod_info"].str.contains("Itchy"),
                        96,
                        np.where(
                            d_a["prod_info"].str.contains("Sensitive"),
                            97,
                            np.where(
                                d_a["prod_info"].str.contains("None of the above"),
                                98,
                                0,
                            ),
                        ),
                    ),
                ),
            ),
        )
        dc["scalp_feelingID"] = np.where(
            d_a["prod_info"].str.contains("A little bit tight"),
            93,
            np.where(
                d_a["prod_info"].str.contains("Tight & Itchy"),
                94,
                np.where(
                    d_a["prod_info"].str.contains("Itchy & painful"),
                    95,
                    np.where(
                        d_a["prod_info"].str.contains("Itchy"),
                        96,
                        np.where(
                            d_a["prod_info"].str.contains("Sensitive"),
                            97,
                            np.where(
                                d_a["prod_info"].str.contains("None of the above"),
                                98,
                                0,
                            ),
                        ),
                    ),
                ),
            ),
        )
        # scalp_flaky
        d_a["scalp_flakyID"] = np.where(
            d_a["scalp_flaky"].str.contains("Rarely"),
            99,
            np.where(
                d_a["scalp_flaky"].str.contains("Usually"),
                100,
                np.where(
                    d_a["scalp_flaky"].str.contains("Always"),
                    101,
                    np.where(d_a["scalp_flaky"].str.contains("No Not Me"), 102, 0),
                ),
            ),
        )
        ds["scalp_flakyID"] = np.where(
            d_a["prod_info"].str.contains("Rarely"),
            99,
            np.where(
                d_a["prod_info"].str.contains("Usually"),
                100,
                np.where(
                    d_a["prod_info"].str.contains("Always"),
                    101,
                    np.where(d_a["prod_info"].str.contains("No Not Me"), 102, 0),
                ),
            ),
        )
        dc["scalp_flakyID"] = np.where(
            d_a["prod_info"].str.contains("Rarely"),
            99,
            np.where(
                d_a["prod_info"].str.contains("Usually"),
                100,
                np.where(
                    d_a["prod_info"].str.contains("Always"),
                    101,
                    np.where(d_a["prod_info"].str.contains("No Not Me"), 102, 0),
                ),
            ),
        )
        # scalp_oily
        d_a["scalp_oilyID"] = np.where(
            d_a["scalp_oily"].str.contains("1 - 2 Days"),
            103,
            np.where(
                d_a["scalp_oily"].str.contains("3 - 4 Days"),
                104,
                np.where(
                    d_a["scalp_oily"].str.contains("5 + Days"),
                    105,
                    np.where(
                        d_a["scalp_oily"].str.contains("My scalp gets dry"), 106, 0
                    ),
                ),
            ),
        )
        ds["scalp_oilyID"] = np.where(
            d_a["prod_info"].str.contains("1 - 2 Days"),
            103,
            np.where(
                d_a["prod_info"].str.contains("3 - 4 Days"),
                104,
                np.where(
                    d_a["prod_info"].str.contains("5 + Days"),
                    105,
                    np.where(
                        d_a["prod_info"].str.contains("My scalp gets dry"), 106, 0
                    ),
                ),
            ),
        )
        dc["scalp_oilyID"] = np.where(
            d_a["prod_info"].str.contains("1 - 2 Days"),
            103,
            np.where(
                d_a["prod_info"].str.contains("3 - 4 Days"),
                104,
                np.where(
                    d_a["prod_info"].str.contains("5 + Days"),
                    105,
                    np.where(
                        d_a["prod_info"].str.contains("My scalp gets dry"), 106, 0
                    ),
                ),
            ),
        )
        # scalp_dry
        d_a["scalp_dryID"] = np.where(
            d_a["scalp_dry"].str.contains("Within hours"),
            107,
            np.where(
                d_a["scalp_dry"].str.contains("1 - 2 Days"),
                108,
                np.where(
                    d_a["scalp_dry"].str.contains("3 - 5 Days"),
                    109,
                    np.where(
                        d_a["scalp_dry"].str.contains("My scalp gets oily"), 110, 0
                    ),
                ),
            ),
        )
        ds["scalp_dryID"] = np.where(
            d_a["prod_info"].str.contains("Within hours"),
            107,
            np.where(
                d_a["prod_info"].str.contains("1 - 2 Days"),
                108,
                np.where(
                    d_a["prod_info"].str.contains("3 - 5 Days"),
                    109,
                    np.where(
                        d_a["prod_info"].str.contains("My scalp gets oily"), 110, 0
                    ),
                ),
            ),
        )
        dc["scalp_dryID"] = np.where(
            d_a["prod_info"].str.contains("Within hours"),
            107,
            np.where(
                d_a["prod_info"].str.contains("1 - 2 Days"),
                108,
                np.where(
                    d_a["prod_info"].str.contains("3 - 5 Days"),
                    109,
                    np.where(
                        d_a["prod_info"].str.contains("My scalp gets oily"), 110, 0
                    ),
                ),
            ),
        )
        # preferences
        d_a["preferencesID"] = np.where(
            dc["preferences"].str.contains("Vegan|Vegeterian"),
            111,
            np.where(
                dc["preferences"].str.contains("Protein free|No Protein"),
                112,
                np.where(
                    dc["preferences"].str.contains("Silicone-free|No silicone"),
                    113,
                    np.where(
                        dc["preferences"].str.contains("Nut free|No nuts"), 114, 0
                    ),
                ),
            ),
        )
        ds["preferencesID"] = np.where(
            dc["prod_info"].str.contains("Vegan|Vegeterian"),
            111,
            np.where(
                dc["prod_info"].str.contains("Protein free|No Protein"),
                112,
                np.where(
                    dc["prod_info"].str.contains("Silicone-free|No silicone"),
                    113,
                    np.where(dc["prod_info"].str.contains("Nut free|No nuts"), 114, 0),
                ),
            ),
        )
        dc["preferencesID"] = np.where(
            dc["prod_info"].str.contains("Vegan|Vegeterian"),
            111,
            np.where(
                dc["prod_info"].str.contains("Protein free|No Protein"),
                112,
                np.where(
                    dc["prod_info"].str.contains("Silicone-free|No silicone"),
                    113,
                    np.where(dc["prod_info"].str.contains("Nut free|No nuts"), 114, 0),
                ),
            ),
        )
        # densityID
        # CHECK IF THE QUESTIONNAIRE BRINGS HERE THE WAY IT IS IMPLEMENTED ON d_a DATASET
        d_a["densityID"] = np.where(
            d_a["density"].str.contains("Low"),
            125,
            np.where(
                d_a["density"].str.contains("Medium"),
                126,
                np.where(d_a["density"].str.contains("High"), 127, 0),
            ),
        )
        ds["densityID"] = np.where(
            ds["density"].str.contains("Low"),
            125,
            np.where(
                ds["density"].str.contains("Medium"),
                126,
                np.where(ds["density"].str.contains("High"), 127, 0),
            ),
        )
        dc["densityID"] = np.where(
            dc["density"].str.contains("Low"),
            125,
            np.where(
                dc["density"].str.contains("Medium"),
                126,
                np.where(dc["density"].str.contains("High"), 127, 0),
            ),
        )
        # highest_issue
        d_a["highest_issueID"] = np.where(
            d_a["highest_issue"].str.contains("Percentage_Dry1"),
            128,
            np.where(
                d_a["highest_issue"].str.contains("Percentage_Dam"),
                129,
                np.where(
                    d_a["highest_issue"].str.contains("Percentage_SEN"),
                    130,
                    np.where(
                        d_a["highest_issue"].str.contains("Percentage_Oil"),
                        131,
                        np.where(
                            d_a["highest_issue"].str.contains("Percentage_DSC"),
                            132,
                            np.where(
                                d_a["highest_issue"].str.contains("Percentage_FL"),
                                133,
                                0,
                            ),
                        ),
                    ),
                ),
            ),
        )
        ds["highest_issueID"] = np.where(
            ds["prod_info"].str.contains(
                "Moisturing|moisturing|moisture|Moisture|moisture spray|oil sealent"
            ),
            128,
            np.where(
                ds["prod_info"].str.contains("Protein|protein|healthy hair"),
                129,
                np.where(
                    ds["prod_info"].str.contains("soothing|Soothing"),
                    130,
                    np.where(
                        ds["prod_info"].str.contains(
                            "Clarifying|clarifying|ultra clean"
                        ),
                        131,
                        np.where(
                            ds["prod_info"].str.contains("nourishing|Nourishing"),
                            132,
                            np.where(
                                ds["prod_info"].str.contains(
                                    "Anti dandruff| ultra clean"
                                ),
                                133,
                                0,
                            ),
                        ),
                    ),
                ),
            ),
        )
        dc["highest_issueID"] = np.where(
            dc["prod_info"].str.contains(
                "Moisturing|moisturing|moisture|Moisture|moisture spray|oil sealent"
            ),
            128,
            np.where(
                dc["prod_info"].str.contains("Protein|protein|healthy hair"),
                129,
                np.where(
                    dc["prod_info"].str.contains("soothing|Soothing"),
                    130,
                    np.where(
                        dc["prod_info"].str.contains(
                            "Clarifying|clarifying|ultra clean"
                        ),
                        131,
                        np.where(
                            dc["prod_info"].str.contains("nourishing|Nourishing"),
                            132,
                            np.where(
                                dc["prod_info"].str.contains(
                                    "Anti dandruff| ultra clean"
                                ),
                                133,
                                0,
                            ),
                        ),
                    ),
                ),
            ),
        )

        # STARTING THE RECOMMENDATION PART

        # CREATING COMBOS FOR EACH UNIQUE SEQUENCE OF ANSWERS ON THE QUESTIONNAIRE

        # working with the dictionary of matches, this will be useful when the datasets are fulfilled

        dict_info = load_workbook("src/data_science/Dict_info.xlsx", data_only=True)
        dict_hair = get_values(dict_info["Hair_Type"])
        dict_texture = get_values(dict_info["Texture"])
        dict_porosity = get_values(dict_info["Porosity"])
        dict_goals = get_values(dict_info["Hair_Goals"])
        dict_issues = get_values(dict_info["Hair_Issues"])
        dict_feels = get_values(dict_info["HairFeels"])
        dict_treatment = get_values(dict_info["Treatment"])
        dict_washing = get_values(dict_info["Washingfrequency"])
        dict_style = get_values(dict_info["Style"])
        dict_split_ends = get_values(dict_info["split_ends"])
        dict_alergies = get_values(dict_info["allergies"])
        dict_where_workout = get_values(dict_info["where_workout"])
        dict_scalp_feeling = get_values(dict_info["scalp_feeling"])
        dict_scalp_flaky = get_values(dict_info["scalp_flaky"])
        dict_scalp_oily = get_values(dict_info["scalp_oily"])
        dict_scalp_dry = get_values(dict_info["scalp_dry"])
        dict_preferences = get_values(dict_info["preferences"])
        dict_density = get_values(dict_info["density"])
        dict_highest_issue = get_values(dict_info["highest_issue"])

        df_dict_hair = pd.DataFrame(dict_hair)
        df_dict_texture = pd.DataFrame(dict_texture)
        df_dict_porosity = pd.DataFrame(dict_porosity)
        df_dict_goals = pd.DataFrame(dict_goals)
        df_dict_issues = pd.DataFrame(dict_issues)
        df_dict_feels = pd.DataFrame(dict_feels)
        df_dict_treatment = pd.DataFrame(dict_treatment)
        df_dict_washing = pd.DataFrame(dict_washing)
        df_dict_style = pd.DataFrame(dict_style)
        df_dict_split_ends = pd.DataFrame(dict_split_ends)
        df_dict_where_workout = pd.DataFrame(dict_where_workout)
        df_dict_scalp_feeling = pd.DataFrame(dict_scalp_feeling)
        df_dict_scalp_flaky = pd.DataFrame(dict_scalp_flaky)
        df_dict_scalp_oily = pd.DataFrame(dict_scalp_oily)
        df_dict_scalp_dry = pd.DataFrame(dict_scalp_dry)
        df_dict_preferences = pd.DataFrame(dict_preferences)
        df_dict_density = pd.DataFrame(dict_density)
        df_dict_highest_issue = pd.DataFrame(dict_highest_issue)

        header_row_hair = df_dict_hair.iloc[0]
        header_row_texture = df_dict_texture.iloc[0]
        header_row_porosity = df_dict_porosity.iloc[0]
        header_row_goals = df_dict_goals.iloc[0]
        header_row_issues = df_dict_issues.iloc[0]
        header_row_feels = df_dict_feels.iloc[0]
        header_row_treatment = df_dict_treatment.iloc[0]
        header_row_washing = df_dict_washing.iloc[0]
        header_row_style = df_dict_style.iloc[0]
        header_row_split_ends = df_dict_split_ends.iloc[0]
        header_row_where_workout = df_dict_where_workout.iloc[0]
        header_row_scalp_feeling = df_dict_scalp_feeling.iloc[0]
        header_row_scalp_flaky = df_dict_scalp_flaky.iloc[0]
        header_row_scalp_oily = df_dict_scalp_oily.iloc[0]
        header_row_scalp_dry = df_dict_scalp_dry.iloc[0]
        header_row_preferences = df_dict_preferences.iloc[0]
        header_row_density = df_dict_density.iloc[0]
        header_row_highest_issue = df_dict_highest_issue.iloc[0]

        ddict_hair = pd.DataFrame(df_dict_hair.values[1:], columns=header_row_hair)
        ddict_texture = pd.DataFrame(
            df_dict_texture.values[1:], columns=header_row_texture
        )
        ddict_porosity = pd.DataFrame(
            df_dict_porosity.values[1:], columns=header_row_porosity
        )
        ddict_goals = pd.DataFrame(df_dict_goals.values[1:], columns=header_row_goals)
        ddict_issues = pd.DataFrame(
            df_dict_issues.values[1:], columns=header_row_issues
        )
        ddict_feels = pd.DataFrame(df_dict_feels.values[1:], columns=header_row_feels)
        ddict_washing = pd.DataFrame(
            df_dict_washing.values[1:], columns=header_row_washing
        )
        ddict_treatment = pd.DataFrame(
            df_dict_treatment.values[1:], columns=header_row_treatment
        )
        ddict_style = pd.DataFrame(df_dict_style.values[1:], columns=header_row_style)
        ddict_split_ends = pd.DataFrame(
            df_dict_split_ends.values[1:], columns=header_row_split_ends
        )
        ddict_where_workout = pd.DataFrame(
            df_dict_style.values[1:], columns=header_row_style
        )
        ddict_scalp_feeling = pd.DataFrame(
            df_dict_scalp_feeling.values[1:], columns=header_row_scalp_feeling
        )
        ddict_scalp_flaky = pd.DataFrame(
            df_dict_scalp_flaky.values[1:], columns=header_row_scalp_flaky
        )
        ddict_scalp_oily = pd.DataFrame(
            df_dict_scalp_oily.values[1:], columns=header_row_scalp_oily
        )
        ddict_scalp_dry = pd.DataFrame(
            df_dict_scalp_dry.values[1:], columns=header_row_scalp_dry
        )
        ddict_preferences = pd.DataFrame(
            df_dict_preferences.values[1:], columns=header_row_preferences
        )
        ddict_density = pd.DataFrame(
            df_dict_density.values[1:], columns=header_row_density
        )
        ddict_highest_issue = pd.DataFrame(
            df_dict_highest_issue.values[1:], columns=header_row_highest_issue
        )

        unique_hair2 = list(ddict_hair["Hair_Type"].unique())
        unique_texture2 = list(ddict_texture["Texture"].unique())
        unique_porosity2 = list(ddict_porosity["Porosity"].unique())
        unique_goals2 = list(ddict_goals["Hair_Goals"].unique())
        unique_issue2 = list(ddict_issues["Hair_Issues"].unique())
        unique_feels2 = list(ddict_feels["HairFeels"].unique())
        unique_washing2 = list(ddict_washing["Washingfrequency"].unique())
        unique_treatment2 = list(ddict_treatment["Treatment"].unique())
        unique_style2 = list(ddict_style["Style"].unique())
        unique_split_ends = list(ddict_split_ends["split_ends"].unique())
        unique_where_workout = list(ddict_where_workout["where_workout"].unique())
        unique_scalp_feeling = list(ddict_scalp_feeling["scalp_feeling"].unique())
        unique_scalp_flaky = list(ddict_scalp_flaky["scalp_flaky"].unique())
        unique_scalp_oily = list(ddict_scalp_oily["scalp_oily"].unique())
        unique_scalp_dry = list(ddict_scalp_dry["scalp_dry"].unique())
        unique_preferences = list(ddict_preferences["preferences"].unique())
        unique_density = list(ddict_density["density"].unique())
        unique_highest_issue = list(ddict_highest_issue["highest_issue"].unique())

        # COMBOS LIST
        combos = []
        _ = [
            combos.append(t)
            for t in itertools.product(
                *[
                    unique_hair2,
                    unique_texture2,
                    unique_porosity2,
                    unique_goals2,
                    unique_issue2,
                    unique_feels2,
                    unique_washing2,
                    unique_treatment2,
                    unique_style2,
                    unique_split_ends,
                    unique_alergies,
                    unique_where_workout,
                    unique_scalp_feeling,
                    unique_scalp_flaky,
                    unique_scalp_oily,
                    unique_scalp_dry,
                    unique_preferences,
                    unique_density,
                    unique_highest_issue,
                ]
            )
        ]

        combos = np.array(combos, dtype="int64")

        # Creating the Recommendation Dataset

        # creating a list with the unique options of shampoos from the shampoos dataset

        unique_shamp = list(
            map(
                dict(zip(pd.unique(ds.Shampoo_name), list(np.arange(1, 16)))).get,
                ds.Shampoo_name,
            )
        )
        unique_hair_type_shamp = list(ds.Hair_TypeID)
        unique_porosity_shamp = list(ds.PorosityID)
        # noinspection PyPep8Naming
        unique_goalsID_shamp = list(ds.GoalsID)
        # noinspection PyPep8Naming
        unique_textureID_shamp = list(ds.TextureID)
        # noinspection PyPep8Naming
        unique_issueID_shamp = list(ds.IssuesID)
        unique_feels_shamp = list(ds.HairFeelsID)
        unique_washing_shamp = list(ds.WashingFrequencyID)
        unique_treatment_shamp = list(ds.TreatmentID)
        unique_style_shamp = list(ds.StyleID)
        unique_split_ends_shamp = list(ds.split_endsID)
        unique_alergies_shamp = list(ds.alergiesID)
        unique_where_workout_shamp = list(ds.where_workoutID)
        unique_scalp_feeling_shamp = list(ds.scalp_feelingID)
        unique_scalp_flaky_shamp = list(ds.scalp_flakyID)
        unique_scalp_oily_shamp = list(ds.scalp_oilyID)
        unique_scalp_dry_shamp = list(ds.scalp_dryID)
        unique_preferences_shamp = list(ds.preferencesID)
        unique_density_shamp = list(ds.densityID)
        unique_highest_issue_shamp = list(ds.highest_issueID)

        shamp_info = np.vstack(
            (
                np.array(unique_hair_type_shamp, dtype="int64"),
                np.array(unique_porosity_shamp, dtype="int64"),
                np.array(unique_goalsID_shamp, dtype="int64"),
                np.array(unique_textureID_shamp, dtype="int64"),
                np.array(unique_issueID_shamp, dtype="int64"),
                np.array(unique_feels_shamp, dtype="int64"),
                np.array(unique_washing_shamp, dtype="int64"),
                np.array(unique_treatment_shamp, dtype="int64"),
                np.array(unique_style_shamp, dtype="int64"),
                np.array(unique_split_ends_shamp, dtype="int64"),
                np.array(unique_alergies_shamp, dtype="int64"),
                np.array(unique_where_workout_shamp, dtype="int64"),
                np.array(unique_scalp_feeling_shamp, dtype="int64"),
                np.array(unique_scalp_flaky_shamp, dtype="int64"),
                np.array(unique_scalp_oily_shamp, dtype="int64"),
                np.array(unique_scalp_dry_shamp, dtype="int64"),
                np.array(unique_preferences_shamp, dtype="int64"),
                np.array(unique_density_shamp, dtype="int64"),
                np.array(unique_highest_issue_shamp, dtype="int64"),
            )
        ).T

        # creating a list with the unique options of conditioners from the conditioners dataset

        unique_cond = list(
            map(
                dict(zip(pd.unique(dc.Conditioner_name), list(np.arange(1, 16)))).get,
                dc.Conditioner_name,
            )
        )
        unique_hair_type_cond = list(dc.Hair_TypeID)
        unique_porosity_cond = list(dc.PorosityID)
        unique_goalsID_cond = list(dc.GoalsID)
        # noinspection PyPep8Naming
        unique_textureID_cond = list(dc.TextureID)
        # noinspection PyPep8Naming
        unique_issueID_cond = list(dc.IssuesID)
        unique_feels_cond = list(dc.HairFeelsID)
        unique_washing_cond = list(dc.WashingFrequencyID)
        unique_treatment_cond = list(dc.TreatmentID)
        unique_style_cond = list(dc.StyleID)
        unique_split_ends_cond = list(dc.split_endsID)
        unique_alergies_cond = list(dc.alergiesID)
        unique_where_workout_cond = list(dc.where_workoutID)
        unique_scalp_feeling_cond = list(dc.scalp_feelingID)
        unique_scalp_flaky_cond = list(dc.scalp_flakyID)
        unique_scalp_oily_cond = list(dc.scalp_oilyID)
        unique_scalp_dry_cond = list(dc.scalp_dryID)
        unique_preferences_cond = list(dc.preferencesID)
        unique_density_cond = list(dc.densityID)
        unique_highest_issue_cond = list(dc.highest_issueID)

        cond_info = np.vstack(
            (
                np.array(unique_hair_type_cond, dtype="int64"),
                np.array(unique_porosity_cond, dtype="int64"),
                np.array(unique_goalsID_cond, dtype="int64"),
                np.array(unique_textureID_cond, dtype="int64"),
                np.array(unique_issueID_cond, dtype="int64"),
                np.array(unique_feels_cond, dtype="int64"),
                np.array(unique_washing_cond, dtype="int64"),
                np.array(unique_treatment_cond, dtype="int64"),
                np.array(unique_style_cond, dtype="int64"),
                np.array(unique_split_ends_cond, dtype="int64"),
                np.array(unique_alergies_cond, dtype="int64"),
                np.array(unique_where_workout_cond, dtype="int64"),
                np.array(unique_scalp_feeling_cond, dtype="int64"),
                np.array(unique_scalp_flaky_cond, dtype="int64"),
                np.array(unique_scalp_oily_cond, dtype="int64"),
                np.array(unique_scalp_dry_cond, dtype="int64"),
                np.array(unique_preferences_cond, dtype="int64"),
                np.array(unique_density_cond, dtype="int64"),
                np.array(unique_highest_issue_cond, dtype="int64"),
            )
        ).T

        # creating a list with the unique answers for each customer from the questionnaire

        unique_hair_type = list(d_a.Hair_TypeID)
        unique_porosity = list(d_a.PorosityID)
        # noinspection PyPep8Naming
        unique_goalsID = list(d_a.GoalsID)
        # noinspection PyPep8Naming
        unique_textureID = list(d_a.TextureID)
        # noinspection PyPep8Naming
        unique_issueID = list(d_a.IssuesID)
        unique_feels = list(d_a.HairFeelsID)
        unique_treatment = list(d_a.TreatmentID)
        unique_washing = list(d_a.WashingFrequencyID)
        unique_style = list(d_a.StyleID)
        unique_split_ends = list(d_a.split_endsID)
        unique_alergies = list(d_a.alergiesID)
        unique_where_workout = list(d_a.where_workoutID)
        unique_scalp_feeling = list(d_a.scalp_feelingID)
        unique_scalp_flaky = list(d_a.scalp_flakyID)
        unique_scalp_oily = list(d_a.scalp_oilyID)
        unique_scalp_dry = list(d_a.scalp_dryID)
        unique_preferences = list(d_a.preferencesID)
        unique_density = list(d_a.densityID)
        unique_highest_issue = list(d_a.highest_issueID)

        customer_info = np.vstack(
            (
                np.array(unique_hair_type, dtype="int64"),
                np.array(unique_porosity, dtype="int64"),
                np.array(unique_goalsID, dtype="int64"),
                np.array(unique_textureID, dtype="int64"),
                np.array(unique_issueID, dtype="int64"),
                np.array(unique_feels, dtype="int64"),
                np.array(unique_washing, dtype="int64"),
                np.array(unique_treatment, dtype="int64"),
                np.array(unique_style, dtype="int64"),
                np.array(unique_split_ends, dtype="int64"),
                np.array(unique_alergies, dtype="int64"),
                np.array(unique_where_workout, dtype="int64"),
                np.array(unique_scalp_feeling, dtype="int64"),
                np.array(unique_scalp_flaky, dtype="int64"),
                np.array(unique_scalp_oily, dtype="int64"),
                np.array(unique_scalp_dry, dtype="int64"),
                np.array(unique_preferences, dtype="int64"),
                np.array(unique_density, dtype="int64"),
                np.array(unique_highest_issue, dtype="int64"),
            )
        ).T

        # converting everything into array
        # TODO: shamp?  - get definition

        shamps_list = shamp.to_numpy()
        shamps_list = np.array(shamps_list)
        # TODO: cond?
        cond_list = cond.to_numpy()
        cond_list = np.array(cond_list)

        # HERE IS WHERE THE 'MAGIC' HAPPENS
        # Now perform dictionary lookup by multiplying each customer's hair values by the values in the products dictionary
        # This multiplication compares the values of the two matrices
        dictionary_lookup_shamp = np.zeros(
            (customer_info.shape[0], shamp_info.shape[0])
        )
        dictionary_lookup_cond = np.zeros((customer_info.shape[0], cond_info.shape[0]))

        for i in range(customer_info.shape[0]):
            dictionary_lookup_shamp[i, :] = np.matmul(shamp_info, customer_info[i, :])
            dictionary_lookup_cond[i, :] = np.matmul(cond_info, customer_info[i, :])

        # Find matching shampoo for each customer

        # The closer the matrices are, the higher the values will be.

        # very big number to ensure there are at least 4 unique shampoos
        num_largest = 100
        # Find indexes of num_largest best recommendations
        selected_shamps_idx = dictionary_lookup_shamp[i, :].argpartition(
            num_largest, axis=None
        )[:num_largest]
        selected_conds_idx = dictionary_lookup_cond[i, :].argpartition(
            num_largest, axis=None
        )[:num_largest]
        # Find the names of the corresponding recommendations
        selected_shamps = np.array(
            [
                np.array(shamps_list[selected_shamps_idx])
                for i in range(customer_info.shape[0])
            ]
        )
        selected_conds = np.array(
            [
                np.array(cond_list[selected_conds_idx])
                for i in range(customer_info.shape[0])
            ]
        )

        # _=[print('Customer {}: Shampoo: {}'. format(customer.Customer_ID[i], selected_shamp2[i], )) for i in range(selected_shamp2.shape[0])]
        # _=[print('Customer {}: Conditioner: {}'. format(customer.Customer_ID[i], selected_cond[i], )) for i in range(selected_cond.shape[0])]

        # Product recommendations

        # THIS COULD BE DONE AS A MATRIX MULTIPLICATION
        # selected_shamps.shape = (no_customers) x (num_largest) x 6

        # All shampoos/conditioners with the same name have the same the same rating
        # Find the unique shampoo names from the recommended list
        unique_selected_shamps = np.array(
            [
                np.unique(np.array(selected_shamps[i, :, 0]))
                for i in range(selected_shamps.shape[0])
            ]
        )
        unique_selected_conds = np.array(
            [
                np.unique(np.array(selected_conds[i, :, 0]))
                for i in range(selected_conds.shape[0])
            ]
        )

        cond_ratings, shamp_ratings = [], []
        # Map the ratings labels to numbers. High - 1, Good - 2, Missing - 3
        new_cond_ratings = list(
            map(
                dict(zip(pd.unique(dc["User Rating"]), list([1, 2]))).get,
                dc["User Rating"],
            )
        )
        new_shamp_ratings = list(
            map(
                dict(zip(pd.unique(ds["User Rating"]), list([1, 2]))).get,
                ds["User Rating"],
            )
        )

        # replace missing values (None) with 3. They will be ranked lower in the list
        new_shamp_ratings = [3 if v is None else v for v in new_shamp_ratings]

        # Find a list of ratings and conditioner names from the spreadsheet
        # delete repetitions by using np.unique
        _ = [
            cond_ratings.append([new_cond_ratings[i], dc["Conditioner_name"][i]])
            for i in range(dc.shape[0])
        ]
        cond_ratings = np.unique(cond_ratings, axis=0)

        _ = [
            shamp_ratings.append([new_shamp_ratings[i], ds["Shampoo_name"][i]])
            for i in range(ds.shape[0])
        ]
        shamp_ratings = np.unique(shamp_ratings, axis=0)

        # HERE WE HAVE THE RECOMENDATIONS ALREADY BUT NOT SORTED
        all_shamp_recommendations: list[ndarray[Any, dtype[Any]]]
        all_cond_recommendations: list[ndarray[Any, dtype[Any]]]
        all_cond_recommendations, all_shamp_recommendations = [], []

        # HERE IS SELECTING THE HIGHEST VALUATION

        for i in range(unique_selected_conds.shape[0]):
            cust_conds = unique_selected_conds[i]
            cust_shamps = unique_selected_shamps[i]
            # find where the recommended product matches the rating and assign the rating
            # Change the rank of the product based on the product rating and its current position
            new_cond_positions = [
                new_cond_ratings[np.where(cust_conds[j] == cond_ratings[:, 1])[0][0]]
                for j in range(cust_conds.shape[0])
            ] * np.linspace(0, 1, len(cust_conds) + 1, endpoint=True)[1:]
            new_shamp_positions = [
                new_shamp_ratings[np.where(cust_shamps[j] == shamp_ratings[:, 1])[0][0]]
                for j in range(cust_shamps.shape[0])
            ] * np.linspace(0, 1, len(cust_shamps) + 1, endpoint=True)[1:]

            # sort the products by the new ranking
            new_cond_positions = np.vstack((new_cond_positions, cust_conds)).T
            all_cond_recommendations.append(
                new_cond_positions[np.argsort(new_cond_positions[:, 0])][:4]
            )

            new_shamp_positions = np.vstack((new_shamp_positions, cust_shamps)).T
            all_shamp_recommendations.append(
                new_shamp_positions[np.argsort(new_shamp_positions[:, 0])][:4]
            )

            # The recommendations have the shape (no_customers) x 4 recommendations x ranking of each product (they're in order)

            # Replace the following lines with the actual output from the last block
            all_shamp_recommendations_output = all_shamp_recommendations[0]
            all_cond_recommendations_output = all_cond_recommendations[0]

            # TODO: Temp fix for Ruff checks
            ruff_temp_fix = {
                "hair_overall": hair_overall,
                "density": density,
                "porosity": porosity,
                "dict_alergies": dict_alergies,
                "header_row_where_workout": header_row_where_workout,
                "unique_alergies": unique_alergies,
                "unique_shamp": unique_shamp,
                "unique_cond": unique_cond,
            }

            logger.info(f"RUFF CATCH: {ruff_temp_fix}")

            # Save the recommendations to pickle files
            with open("shamp_recommendations.pkl", "wb") as shamp_file:
                pickle.dump(all_shamp_recommendations_output, shamp_file)
            with open("cond_recommendations.pkl", "wb") as cond_file:
                pickle.dump(all_cond_recommendations_output, cond_file)
            # SHOULD RETURN THE 2 BEST OPTIONS OF CONDITIONER AND SHAMPOO
            return all_shamp_recommendations[0], all_cond_recommendations[0]
