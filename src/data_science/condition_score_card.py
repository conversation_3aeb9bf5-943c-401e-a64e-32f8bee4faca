import warnings

import numpy as np
import pandas as pd
from django.contrib.auth.models import User

from questionaire.models import Question

warnings.filterwarnings("ignore")


# PLEASE CHECK THE INTEGRATION OF THE CLASS
class ConditionScores:
    def get_condition_scores(self, user):
        # returns a dataframe

        # creating the dataset used for condition score with the variables created with answers of the questionnaire

        user = User.objects.get(id=user.id)
        issue = Question.objects.get(position=2)
        hair_overall = Question.objects.get(position=3)
        split_ends = Question.objects.get(position=7)
        daily_water = Question.objects.get(position=24)
        hair_feels = Question.objects.get(position=30)
        water_damage = Question.objects.get(position=32)
        treatments = Question.objects.get(position=34)
        enhancers = Question.objects.get(position=35)
        porosity = Question.objects.get(position=37)
        styling_hair = Question.objects.get(position=38)
        heat_freq = Question.objects.get(position=39)  # ApplyHeat question
        scalp_feeling = Question.objects.get(position=41)
        scalp_flaky = Question.objects.get(position=42)
        scalp_oily = Question.objects.get(position=43)
        scalp_dry = Question.objects.get(position=44)

        # defining a DataFrame/Dataset with those answers
        q_answers = [
            (
                "customer_id",
                "Issue ",
                "hair_feels",
                "treatments",
                "enhancers",
                "styling_hair",
                "split_ends",
                "heat_freq",
                "scalp_feeling",
                "scalp_flaky",
                "scalp_oily",
                "scalp_dry",
                "water_damage",
            ),
            (
                user.id,
                issue,
                hair_overall,
                split_ends,
                daily_water,
                hair_feels,
                treatments,
                enhancers,
                porosity,
                styling_hair,
                heat_freq,
                scalp_feeling,
                scalp_flaky,
                scalp_oily,
                scalp_dry,
                water_damage,
            ),
        ]

        d_cs = pd.DataFrame(data=q_answers)

        # definitions (business rules)

        Dryness_Max_Score = 13
        Damage_Max_Score = 15
        Sensitivity_Max_Score = 4.4
        Sebum_Max_Score_oily = 4.2
        Sebum_Max_Score_dry = 4.2
        Flakes_Max_Score = 4.1
        # classification part of the condition scoring with them own weights
        # PorositySC
        d_cs["PorositySC"] = np.where(
            d_cs["hair_behaviour"].str.contains("A -Repels moisture when wet"),
            0,
            np.where(
                d_cs["hair_behaviour"].str.contains("B - Its bouncy and elastic"),
                1,
                np.where(
                    d_cs["hair_behaviour"].str.contains("C- Easily absorbs water"),
                    2,
                    np.nan,
                ),
            ),
        )
        # IssueSC
        d_cs["IssuesSC"] = np.where(
            d_cs["Issue "].str.contains("Scalp dryness|breakage|Split|Thinning"),
            2,
            np.where(d_cs["Issue "].str.contains("Not me"), 0, np.nan),
        )
        # hair_overallSC
        d_cs["hair_overalSC"] = np.where(
            d_cs["hair_overal"].str.contains("Generally Dry|Flaky Scalp + Dry Ends"),
            3,
            np.where(
                d_cs["hair_overal"].str.contains("Oily roots + dry ends"),
                2,
                np.where(
                    d_cs["hair_overal"].str.contains(
                        "Fairly balanced / No outstanding issues"
                    ),
                    0,
                    np.nan,
                ),
            ),
        )
        # daily_waterSC
        d_cs["daily_waterSC"] = np.where(
            d_cs["daily_water"].str.contains("Quarter Litre"),
            2,
            np.where(
                d_cs["daily_water"].str.contains("Half a Litre"),
                1,
                np.where(
                    d_cs["daily_water"].str.contains("1 Litre or more"),
                    0,
                    np.where(
                        d_cs["daily_water"].str.contains("I don’t drink water daily"),
                        3,
                        np.nan,
                    ),
                ),
            ),
        )
        # HairFeelsSC
        d_cs["HairFeelsSC"] = np.where(
            d_cs["hair_feels"].str.contains(
                "Weighed down|Limp|Not responding to anything|None of these / Normal"
            ),
            0,
            np.where(d_cs["hair_feels"].str.contains("Coarse and dry"), 1, np.nan),
        )
        # TreatmentSC
        d_cs["TreatmentSC"] = np.where(
            d_cs["treatments"].str.contains(
                "Relaxers|Perm|Keratin|Bleach|Colour / Dyed"
            ),
            1,
            np.where(d_cs["treatments"].str.contains("No not me"), 0, np.nan),
        )
        # EnhancersSC
        d_cs["EnhancersSC"] = np.where(
            d_cs["enhancers"].str.contains(
                "Natural extensions|Synthetic extensions|Weave"
            ),
            1,
            np.where(d_cs["enhancers"].str.contains("No not me"), 0, np.nan),
        )
        # StyleSC
        d_cs["StyleSC"] = np.where(
            d_cs["styling_hair"].str.contains(
                "Blow dryer|Straightening iron|Curling iron/wand"
            ),
            1,
            np.where(d_cs["styling_hair"].str.contains("No stylers for me"), 0, np.nan),
        )
        # SplitEnds
        d_cs["split_endsSC"] = np.where(
            d_cs["split_ends"].str.contains("Yes"),
            1,
            np.where(d_cs["split_ends"].str.contains("No"), 0, np.nan),
        )
        # heat_freq
        d_cs["heat_freqSC"] = np.where(
            d_cs["heat_freq"].str.contains("Everyday or every other day"),
            3,
            np.where(
                d_cs["heat_freq"].str.contains("Once or twice a week"),
                2,
                np.where(
                    d_cs["heat_freq"].str.contains("Once or twice a month"),
                    1,
                    np.where(
                        d_cs["heat_freq"].str.contains("Rarely|I dont do this"),
                        0,
                        np.nan,
                    ),
                ),
            ),
        )
        # scalp_feeling
        d_cs["scalp_feelingSC"] = np.where(
            d_cs["scalp_feeling"].str.contains("A little bit tight"),
            1,
            np.where(
                d_cs["scalp_feeling"].str.contains(
                    "Tight & Itchy|Itchy & painful|Itchy|Sensitive"
                ),
                2,
                np.where(
                    d_cs["scalp_feeling"].str.contains("None of the above"), 0, np.nan
                ),
            ),
        )
        # scalp_flaky
        d_cs["scalp_flakySC"] = np.where(
            d_cs["scalp_flaky"].str.contains("Rarely"),
            1,
            np.where(
                d_cs["scalp_flaky"].str.contains("Usually"),
                2,
                np.where(
                    d_cs["scalp_flaky"].str.contains("Always"),
                    3,
                    np.where(d_cs["scalp_flaky"].str.contains("No Not Me"), 0, np.nan),
                ),
            ),
        )
        # scalp_oily
        d_cs["scalp_oilySC"] = np.where(
            d_cs["scalp_oily"].str.contains("1 - 2 Days"),
            3,
            np.where(
                d_cs["scalp_oily"].str.contains("3 - 4 Days"),
                2,
                np.where(
                    d_cs["scalp_oily"].str.contains("5"),
                    1,
                    np.where(
                        d_cs["scalp_oily"].str.contains("My scalp gets dry"), 0, np.nan
                    ),
                ),
            ),
        )
        # scalp_dry
        d_cs["scalp_drySC"] = np.where(
            d_cs["scalp_dry"].str.contains("Within hours"),
            3,
            np.where(
                d_cs["scalp_dry"].str.contains("1 - 2 Days"),
                2,
                np.where(
                    d_cs["scalp_dry"].str.contains("3 - 5 Days"),
                    1,
                    np.where(
                        d_cs["scalp_dry"].str.contains("My scalp gets oily"), 0, np.nan
                    ),
                ),
            ),
        )
        # water_damage
        d_cs["water_damageID"] = np.where(
            d_cs["water_damage"].str.contains(
                "Your hair colour fades easily or dulls rapidly? | Do you have problems with your shampoo lathering? | Your hair feels really dry no matter how much you wash or deep condition it? | Do you have chronic hair breakage problems that are not fixed by previous treatment or care?"
            ),
            120,
            np.where(
                d_cs["water_damage"].str.contains("None of the above"), 124, np.nan
            ),
        )

        # Starting calculating the totals used for each percentage of the hair

        d_cs["total1"] = (
            d_cs["IssuesSC"]
            + d_cs["hair_overalSC"]
            + d_cs["daily_waterSC"]
            + d_cs["HairFeelsSC"]
            + d_cs["scalp_flakySC"]
            + d_cs["split_endsSC"]
            + d_cs["scalp_drySC"]
        )
        d_cs["total2"] = (
            d_cs["TreatmentSC"]
            + d_cs["EnhancersSC"]
            + d_cs["PorositySC"]
            + d_cs["StyleSC"]
            + d_cs["heat_freqSC"]
            + d_cs["split_endsSC"]
        )
        d_cs["total3"] = d_cs["scalp_feelingSC"]
        d_cs["total4"] = d_cs["scalp_oilySC"]
        d_cs["total5"] = d_cs["scalp_drySC"]
        d_cs["total6"] = d_cs["scalp_flakySC"]

        # converting into percentages and round up to one decimal place

        d_cs["Percentage_Dry1"] = (d_cs["total1"] * 100 / Dryness_Max_Score).round(
            decimals=1
        )  # condition of dryness
        d_cs["Percentage_Dam"] = (d_cs["total2"] * 100 / Damage_Max_Score).round(
            decimals=1
        )  # condition of damage
        d_cs["Percentage_SEN"] = (d_cs["total3"] * 100 / Sensitivity_Max_Score).round(
            decimals=1
        )  # condition of sensitivity
        d_cs["Percentage_Oil"] = (d_cs["total4"] * 100 / Sebum_Max_Score_oily).round(
            decimals=1
        )  # condition of sebum oil
        d_cs["Percentage_DSC"] = (d_cs["total5"] * 100 / Sebum_Max_Score_dry).round(
            decimals=1
        )  # condition of dry scalp
        d_cs["Percentage_FL "] = (d_cs["total6"] * 100 / Flakes_Max_Score).round(
            decimals=1
        )  # condition of flakes

        # returns a dataframe
        return d_cs[
            [
                "customer_id",
                "Percentage_Dry1",
                "Percentage_Dam",
                "Percentage_SEN",
                "Percentage_Oil",
                "Percentage_DSC",
                "Percentage_FL ",
            ]
        ]
