import numpy as np
import pandas as pd
import warnings
from src.data_science.Recomendation_Engine_V3.src import DataLoader

warnings.filterwarnings("ignore")


class FeatureEngineering:
    def __init__(self, user_id):
        self.user_id = user_id
        self.data_loader = DataLoader()
        self.default_value = 0

    def map_categorical_to_id(self, data, column, mappings):
        for i, (pattern, value) in enumerate(mappings, start=1):
            data[f"{column}ID"] = np.where(
                data[column].str.contains(pattern), value, data[f"{column}ID"]
            )
        data[f"{column}ID"] = np.where(
            data[f"{column}ID"] == 0, self.default_value, data[f"{column}ID"]
        )

    def run_feature_engineering(self) -> pd.DataFrame:
        # Load questionnaire data
        # TODO: ds and dc are datasets from Product information otherwise it's derived from load_questionnaire
        recom_answers, ds, dc = self.data_loader.load_questionnaire_data(self.user_id)

        # Hair Type
        hair_type_mappings = [
            ("Type 2a|2A", 1),
            ("Type 2b|2B", 2),
            ("Type 2c|2C", 3),
            ("Type 3a|3A", 4),
            ("Type 3b|3B", 5),
            ("Type 3c|3C", 6),
            ("Type 4a|4A", 7),
            ("Type 4b|4B", 8),
            ("Type 4c|4C", 9),
        ]
        self.map_categorical_to_id(ds, "Hair_Type", hair_type_mappings)
        self.map_categorical_to_id(dc, "Hair_Type", hair_type_mappings)

        # Texture
        texture_mappings = [("Fine|Thin", 10), ("Medium", 11), ("Thick", 12)]
        self.map_categorical_to_id(ds, "Texture ", texture_mappings)
        self.map_categorical_to_id(dc, "Texture ", texture_mappings)

        # Porosity
        porosity_mappings = [
            ("A -Repels moisture when wet", 13),
            ("B - Its bouncy and elastic", 14),
            ("C- Easily absorbs water", 15),
        ]
        self.map_categorical_to_id(ds, "Porosity", porosity_mappings)
        self.map_categorical_to_id(dc, "Porosity", porosity_mappings)

        # Goals
        goals_mappings = [
            ("Growth", 16),
            ("Healthy", 17),
            ("Moisture", 18),
            ("Ultra", 19),
        ]
        self.map_categorical_to_id(ds, "Hair Goals", goals_mappings)
        self.map_categorical_to_id(dc, "Hair Goals", goals_mappings)

        # Issue
        issues_mappings = [
            ("Scalp dryness", 20),
            ("breakage|Split", 21),
            ("Thinning", 22),
            ("Not me", 23),
        ]
        self.map_categorical_to_id(ds, "Hair Issues", issues_mappings)
        self.map_categorical_to_id(dc, "Hair Issues", issues_mappings)

        # Hair Feels
        hair_feels_mappings = [
            ("Weighed down", 24),
            ("Coarse and dry", 25),
            ("Limp", 26),
            ("Not responding to anything", 27),
            ("None of these / Normal", 28),
        ]
        self.map_categorical_to_id(ds, "prod_info", hair_feels_mappings)
        self.map_categorical_to_id(dc, "prod_info", hair_feels_mappings)

        # Washing Frequency
        washing_frequency_mappings = [
            ("More than once a week", 29),
            ("Once a week", 30),
            ("Twice a month", 31),
            ("About once a month", 32),
        ]
        self.map_categorical_to_id(ds, "prod_info", washing_frequency_mappings)
        self.map_categorical_to_id(dc, "prod_info", washing_frequency_mappings)

        # Treatment
        treatment_mappings = [
            ("Relaxers", 33),
            ("Perm", 34),
            ("Keratin", 35),
            ("Bleach", 36),
            ("Colour / Dyed", 37),
            ("No not me", 38),
        ]
        self.map_categorical_to_id(ds, "prod_info", treatment_mappings)
        self.map_categorical_to_id(dc, "prod_info", treatment_mappings)

        # Style
        style_mappings = [
            ("Blow dryer|Straightening iron|Curling iron/wand", 41),
            ("No stylers for me", 42),
        ]
        self.map_categorical_to_id(ds, "prod_info", style_mappings)
        self.map_categorical_to_id(dc, "prod_info", style_mappings)

        # Split Ends
        split_ends_mappings = [("Yes", 43), ("No", 44)]
        self.map_categorical_to_id(ds, "prod_info", split_ends_mappings)
        self.map_categorical_to_id(dc, "prod_info", split_ends_mappings)

        # Allergies
        allergies_mappings = [
            ("Lactose|dairy", 55),
            ("Caffeine", 56),
            ("Nuts", 57),
            ("No not me", 58),
        ]
        self.map_categorical_to_id(ds, "prod_info", allergies_mappings)
        self.map_categorical_to_id(dc, "prod_info", allergies_mappings)

        # Where Workout
        where_workout_mappings = [
            ("Indoors/gym|Outdoors/Boot camps", 82),
            ("Chlorine swimming pool", 83),
            ("I dont exercise often", 84),
        ]
        self.map_categorical_to_id(ds, "prod_info", where_workout_mappings)
        self.map_categorical_to_id(dc, "prod_info", where_workout_mappings)

        # Scalp Feeling
        scalp_feeling_mappings = [
            ("A little bit tight", 93),
            ("Tight & Itchy", 94),
            ("Itchy & painful", 95),
            ("Itchy", 96),
            ("Sensitive", 97),
            ("None of the above", 98),
        ]
        self.map_categorical_to_id(ds, "prod_info", scalp_feeling_mappings)
        self.map_categorical_to_id(dc, "prod_info", scalp_feeling_mappings)

        # Scalp Flaky
        scalp_flaky_mappings = [
            ("Rarely", 99),
            ("Usually", 100),
            ("Always", 101),
            ("No Not Me", 102),
        ]
        self.map_categorical_to_id(ds, "prod_info", scalp_flaky_mappings)
        self.map_categorical_to_id(dc, "prod_info", scalp_flaky_mappings)

        # Scalp Oily
        scalp_oily_mappings = [
            ("1 - 2 Days", 103),
            ("3 - 4 Days", 104),
            ("5 + Days", 105),
            ("My scalp gets dry", 106),
        ]
        self.map_categorical_to_id(ds, "prod_info", scalp_oily_mappings)
        self.map_categorical_to_id(dc, "prod_info", scalp_oily_mappings)

        # Scalp Dry
        scalp_dry_mappings = [
            ("Within hours", 107),
            ("1 - 2 Days", 108),
            ("3 - 5 Days", 109),
            ("6+ Days", 110),
        ]
        self.map_categorical_to_id(ds, "prod_info", scalp_dry_mappings)
        self.map_categorical_to_id(dc, "prod_info", scalp_dry_mappings)

        # Concatenate the DataFrames
        result_dataframe = pd.concat([recom_answers, ds, dc], axis=1)

        return result_dataframe
