import numpy as np
import pandas as pd
import warnings
from django.contrib.auth.models import User
from src.data_science.Recomendation_Engine_V3.src import DataLoader

# from users.models import UserProfile
warnings.filterwarnings("ignore")


class ConditionScores:
    def get_condition_scores(self, user) -> pd.DataFrame:
        # definitions (business rules)

        Dryness_Max_Score = 13
        Damage_Max_Score = 15
        Sensitivity_Max_Score = 4.4
        Sebum_Max_Score_oily = 4.2
        Sebum_Max_Score_dry = 4.2
        Flakes_Max_Score = 4.1

        # returns a dataframe
        # creating the dataset used for condition score with the variables created with answers of the questionnaire

        # Create an instance of the DataLoader class
        data_loader = DataLoader()

        # Replace 'user.id' with the actual user ID
        user_id = User.objects.get(id=user.id)

        # Call the load_questionnaire_data method
        condition_score_answers = data_loader.load_questionnaire_data(user_id)

        d_cs = condition_score_answers

        # classification part of the condition scoring with them own weights

        def map_categorical_to_id(data, column, mappings):
            for i, (pattern, value) in enumerate(mappings, start=1):
                data[f"{column}ID"] = np.where(
                    data[column].str.contains(pattern), value, data[f"{column}ID"]
                )
            data[f"{column}ID"] = np.where(
                data[f"{column}ID"] == 0, default_value, data[f"{column}ID"]
            )

        # Define default value for the cases where no match is found
        default_value = np.nan

        # PorositySC
        porosity_sc_mappings = [
            ("A -Repels moisture when wet", 0),
            ("B - Its bouncy and elastic", 1),
            ("C- Easily absorbs water", 2),
        ]
        map_categorical_to_id(d_cs, "hair_behaviour", porosity_sc_mappings)

        # IssuesSC
        issues_sc_mappings = [
            ("Scalp dryness|breakage|Split|Thinning", 2),
            ("Not me", 0),
        ]
        map_categorical_to_id(d_cs, "Issue ", issues_sc_mappings)

        # hair_overallSC
        hair_overall_sc_mappings = [
            ("Generally Dry|Flaky Scalp + Dry Ends", 3),
            ("Oily roots + dry ends", 2),
            ("Fairly balanced / No outstanding issues", 0),
        ]
        map_categorical_to_id(d_cs, "hair_overal", hair_overall_sc_mappings)

        # daily_waterSC
        daily_water_sc_mappings = [
            ("Quarter Litre", 2),
            ("Half a Litre", 1),
            ("1 Litre or more", 0),
            ("I don’t drink water daily", 3),
        ]
        map_categorical_to_id(d_cs, "daily_water", daily_water_sc_mappings)

        # HairFeelsSC
        hair_feels_sc_mappings = [
            ("Weighed down|Limp|Not responding to anything|None of these / Normal", 0),
            ("Coarse and dry", 1),
        ]
        map_categorical_to_id(d_cs, "hair_feels", hair_feels_sc_mappings)

        # TreatmentSC
        treatment_sc_mappings = [
            ("Relaxers|Perm|Keratin|Bleach|Colour / Dyed", 1),
            ("No not me", 0),
        ]
        map_categorical_to_id(d_cs, "treatments", treatment_sc_mappings)

        # EnhancersSC
        enhancers_sc_mappings = [
            ("Natural extensions|Synthetic extensions|Weave", 1),
            ("No not me", 0),
        ]
        map_categorical_to_id(d_cs, "enhancers", enhancers_sc_mappings)

        # StyleSC
        style_sc_mappings = [
            ("Blow dryer|Straightening iron|Curling iron/wand", 1),
            ("No stylers for me", 0),
        ]
        map_categorical_to_id(d_cs, "styling_hair", style_sc_mappings)

        # split_endsSC
        split_ends_sc_mappings = [("Yes", 1), ("No", 0)]
        map_categorical_to_id(d_cs, "split_ends", split_ends_sc_mappings)

        # heat_freqSC
        heat_freq_sc_mappings = [
            ("Everyday or every other day", 3),
            ("Once or twice a week", 2),
            ("Once or twice a month", 1),
            ("Rarely|I dont do this", 0),
        ]
        map_categorical_to_id(d_cs, "heat_freq", heat_freq_sc_mappings)

        # scalp_feelingSC
        scalp_feeling_sc_mappings = [
            ("A little bit tight", 1),
            ("Tight & Itchy|Itchy & painful|Itchy|Sensitive", 2),
            ("None of the above", 0),
        ]
        map_categorical_to_id(d_cs, "scalp_feeling", scalp_feeling_sc_mappings)

        # scalp_flakySC
        scalp_flaky_sc_mappings = [
            ("Rarely", 1),
            ("Usually", 2),
            ("Always", 3),
            ("No Not Me", 0),
        ]
        map_categorical_to_id(d_cs, "scalp_flaky", scalp_flaky_sc_mappings)

        # scalp_oilySC
        scalp_oily_sc_mappings = [
            ("1 - 2 Days", 3),
            ("3 - 4 Days", 2),
            ("5", 1),
            ("My scalp gets dry", 0),
        ]
        map_categorical_to_id(d_cs, "scalp_oily", scalp_oily_sc_mappings)

        # scalp_drySC
        scalp_dry_sc_mappings = [
            ("Within hours", 3),
            ("1 - 2 Days", 2),
            ("3 - 5 Days", 1),
            ("My scalp gets oily", 0),
        ]
        map_categorical_to_id(d_cs, "scalp_dry", scalp_dry_sc_mappings)

        # water_damageID
        water_damage_mappings = [
            (
                "Your hair colour fades easily or dulls rapidly? | Do you have problems with your shampoo lathering? | Your hair feels really dry no matter how much you wash or deep condition it? | Do you have chronic hair breakage problems that are not fixed by previous treatment or care?",
                "TBD",
            ),
            ("None of the above", "TBD"),
        ]
        map_categorical_to_id(d_cs, "water_damage", water_damage_mappings)

        # Starting calculating the totals used for each percentage of the hair

        d_cs["total1"] = (
            d_cs["IssuesSC"]
            + d_cs["hair_overalSC"]
            + d_cs["daily_waterSC"]
            + d_cs["HairFeelsSC"]
            + d_cs["scalp_flakySC"]
            + d_cs["split_endsSC"]
            + d_cs["scalp_drySC"]
        )
        d_cs["total2"] = (
            d_cs["TreatmentSC"]
            + d_cs["EnhancersSC"]
            + d_cs["PorositySC"]
            + d_cs["StyleSC"]
            + d_cs["heat_freqSC"]
            + d_cs["split_endsSC"]
        )
        d_cs["total3"] = d_cs["scalp_feelingSC"]
        d_cs["total4"] = d_cs["scalp_oilySC"]
        d_cs["total5"] = d_cs["scalp_drySC"]
        d_cs["total6"] = d_cs["scalp_flakySC"]

        # converting into percentages and round up to one decimal place

        d_cs["Percentage_Dry1"] = (d_cs["total1"] * 100 / Dryness_Max_Score).round(
            decimals=1
        )  # condition of dryness
        d_cs["Percentage_Dam"] = (d_cs["total2"] * 100 / Damage_Max_Score).round(
            decimals=1
        )  # condition of damage
        d_cs["Percentage_SEN"] = (d_cs["total3"] * 100 / Sensitivity_Max_Score).round(
            decimals=1
        )  # condition of sensitivity
        d_cs["Percentage_Oil"] = (d_cs["total4"] * 100 / Sebum_Max_Score_oily).round(
            decimals=1
        )  # condition of sebum oil
        d_cs["Percentage_DSC"] = (d_cs["total5"] * 100 / Sebum_Max_Score_dry).round(
            decimals=1
        )  # condition of dry scalp
        d_cs["Percentage_FL "] = (d_cs["total6"] * 100 / Flakes_Max_Score).round(
            decimals=1
        )  # condition of flakes

        # returns a dataframe
        return d_cs[
            [
                "customer_id",
                "Percentage_Dry1",
                "Percentage_Dam",
                "Percentage_SEN",
                "Percentage_Oil",
                "Percentage_DSC",
                "Percentage_FL ",
            ]
        ]
