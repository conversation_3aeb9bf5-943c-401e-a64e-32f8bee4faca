import numpy as np
import pandas as pd
from openpyxl import load_workbook
import itertools
from src.data_science.Recomendation_Engine_V3.src import FeatureEngineering
from src.data_science.Recomendation_Engine_V3.src import ConditionScores


def get_values(sheet) -> list:
    results = []
    for row in sheet.values:
        if not any(value for value in row):
            continue
        else:
            results.append(row)
    return results


class RecommendationEngine:
    def __init__(
        self, feature_engineering_instance, condition_scores_instance, shamp, cond
    ):
        # Get the results_dataframe from the FeatureEngineering instance
        condition_scores_instance = ConditionScores()
        feature_engineering_instance = FeatureEngineering()

        self.results_dataframe = feature_engineering_instance.run_feature_engineering()
        self.condition_score_dataframe = (
            condition_scores_instance.get_condition_scores()
        )
        self.results_dataframe["highest_issue"] = self.condition_score_dataframe.idxmax(
            axis=1
        )

        product_data = load_workbook(
            filename=("src/data_science/product_data.xlsx"), data_only=True
        )
        shamp_info = get_values(product_data["Shampoo"])
        cond_info = get_values(product_data["Conditioners"])
        self.shamp = pd.DataFrame(shamp_info)
        self.cond = pd.DataFrame(cond_info)

        # Load dictionary information
        dict_info = load_workbook("src/data_science/Dict_info.xlsx", data_only=True)
        dict_headers = [
            "Hair_Type",
            "Texture",
            "Porosity",
            "Hair_Goals",
            "Hair_Issues",
            "HairFeels",
            "Treatment",
            "Washingfrequency",
            "Style",
            "split_ends",
            "alergies",
            "where_workout",
            "scalp_feeling",
            "scalp_flaky",
            "scalp_oily",
            "scalp_dry",
            "preferences",
            "density",
            "highest_issue",
        ]

        # Create dataframes for dictionary values
        dict_dfs = {}
        for header in dict_headers:
            values = get_values(dict_info[header])
            df = pd.DataFrame(values[1:], columns=values[0])
            dict_dfs[header] = df

        # Create unique lists for dictionary values
        unique_dict_values = {}
        for header, df in dict_dfs.items():
            unique_dict_values[header] = list(df.iloc[:, 0].unique())

        # Generate combinations for unique values
        combos = list(itertools.product(*unique_dict_values.values()))
        combos = np.array(combos, dtype="int64")

        # Perform dictionary lookup
        dictionary_lookup_shamp, dictionary_lookup_cond = (
            self.perform_dictionary_lookup(self.results_dataframe, shamp, cond)
        )

        # Find matching shampoos and conditioners for each customer
        selected_shamps, selected_conds = self.find_matching_products(
            dictionary_lookup_shamp, dictionary_lookup_cond
        )

        # Get product recommendations
        all_shamp_recommendations, all_cond_recommendations = (
            self.get_product_recommendations(
                selected_shamps, selected_conds, shamp, cond
            )
        )

        # Return the 2 best options of conditioner and shampoo
        return all_shamp_recommendations[0], all_cond_recommendations[0]

    def load_datasets(self) -> list[pd.DataFrame, pd.DataFrame]:
        # Load shampoo and conditioner datasets (replace this with your actual dataset paths)
        ds = pd.read_csv("path_to_shampoo_dataset.csv")
        dc = pd.read_csv("path_to_conditioner_dataset.csv")
        return ds, dc

    def perform_dictionary_lookup(self, results_dataframe, shamp, cond):
        # Perform dictionary lookup by multiplying each customer's hair values by the values in the products dictionary
        customer_info = results_dataframe.to_numpy()
        shamp_info, cond_info = self.get_product_info(shamp, cond)

        dictionary_lookup_shamp = np.zeros(
            (customer_info.shape[0], shamp_info.shape[0])
        )
        dictionary_lookup_cond = np.zeros((customer_info.shape[0], cond_info.shape[0]))

        for i in range(customer_info.shape[0]):
            dictionary_lookup_shamp[i, :] = np.matmul(shamp_info, customer_info[i, :])
            dictionary_lookup_cond[i, :] = np.matmul(cond_info, customer_info[i, :])

        return dictionary_lookup_shamp, dictionary_lookup_cond

    def get_product_info(self, shamp, cond):
        # Get product information for shampoo and conditioner
        shamp_info = self.get_product_values(shamp)
        cond_info = self.get_product_values(cond)
        return shamp_info, cond_info

    def get_product_values(self, df: pd.DataFrame):
        # Get product values for shampoo or conditioner
        unique_values = [list(pd.unique(df[column])) for column in df.columns]
        product_info = np.vstack(unique_values).T
        return product_info

    def find_matching_products(
        self, dictionary_lookup_shamp, dictionary_lookup_cond
    ) -> tuple[np.array, np.array]:
        # Find matching shampoo and conditioner for each customer
        num_largest = 100
        selected_shamps_idx = np.argpartition(
            dictionary_lookup_shamp, num_largest, axis=None
        )[:num_largest]
        selected_conds_idx = np.argpartition(
            dictionary_lookup_cond, num_largest, axis=None
        )[:num_largest]
        selected_shamps = np.array(
            [
                np.array(self.shamp.iloc[selected_shamps_idx])
                for _ in range(dictionary_lookup_shamp.shape[0])
            ]
        )
        selected_conds = np.array(
            [
                np.array(self.cond.iloc[selected_conds_idx])
                for _ in range(dictionary_lookup_cond.shape[0])
            ]
        )
        return selected_shamps, selected_conds

    def get_product_recommendations(
        self, selected_shamps, selected_conds, shamp, cond
    ) -> tuple[list, list]:
        # Get product recommendations
        unique_selected_shamps = np.array(
            [
                np.unique(np.array(selected_shamps[i, :, 0]))
                for i in range(selected_shamps.shape[0])
            ]
        )
        unique_selected_conds = np.array(
            [
                np.unique(np.array(selected_conds[i, :, 0]))
                for i in range(selected_conds.shape[0])
            ]
        )

        cond_ratings, shamp_ratings = self.get_product_ratings(cond, shamp)

        all_cond_recommendations, all_shamp_recommendations = [], []

        for i in range(unique_selected_conds.shape[0]):
            cust_conds = unique_selected_conds[i]
            cust_shamps = unique_selected_shamps[i]

            new_cond_positions = self.calculate_new_positions(cust_conds, cond_ratings)
            new_shamp_positions = self.calculate_new_positions(
                cust_shamps, shamp_ratings
            )

            all_cond_recommendations.append(
                new_cond_positions[np.argsort(new_cond_positions[:, 0])][:4]
            )
            all_shamp_recommendations.append(
                new_shamp_positions[np.argsort(new_shamp_positions[:, 0])][:4]
            )

        return all_shamp_recommendations, all_cond_recommendations

    def get_product_ratings(self, cond, shamp) -> tuple[np.array, np.array]:
        # Map the ratings labels to numbers. High - 1, Good - 2, Missing - 3
        new_cond_ratings = self.map_ratings(cond["User Rating"])
        new_shamp_ratings = self.map_ratings(shamp["User Rating"])

        # Replace missing values (None) with 3. They will be ranked lower in the list
        new_shamp_ratings = [3 if v is None else v for v in new_shamp_ratings]

        # Find a list of ratings and product names from the spreadsheet
        cond_ratings = [
            [new_cond_ratings[i], cond["Conditioner_name"][i]]
            for i in range(cond.shape[0])
        ]
        cond_ratings = np.unique(cond_ratings, axis=0)

        shamp_ratings = [
            [new_shamp_ratings[i], shamp["Shampoo_name"][i]]
            for i in range(shamp.shape[0])
        ]
        shamp_ratings = np.unique(shamp_ratings, axis=0)

        return cond_ratings, shamp_ratings

    def map_ratings(self, ratings) -> list:
        # Map ratings labels to numbers
        return list(map(dict(zip(pd.unique(ratings), list([1, 2]))).get, ratings))

    def calculate_new_positions(self, cust_products, ratings) -> list[np.array]:
        # Calculate new positions based on ratings and current position
        new_positions = [
            ratings[np.where(cust_products[j] == ratings[:, 1])[0][0]]
            * np.linspace(0, 1, len(cust_products) + 1, endpoint=True)[1:]
            for j in range(len(cust_products))
        ]
        new_positions = np.vstack((new_positions, cust_products)).T
        return new_positions


# mmendation_engine = RecommendationEngine(results_dataframe, d_cs)
# Usage example:  TEST ONLY!!!!
# shamp_recommendations, cond_recommendations = RecommendationEngine()
# print("Shampoo Recommendations:", shamp_recommendations)
# print("Conditioner Recommendations:", cond_recommendations)
