# imports
import warnings

import pandas as pd
from openpyxl import load_workbook
from questionaire.models import Question

from django.contrib.auth.models import User
from users.models import UserProfile


# Filter out unnecessary warnings
warnings.filterwarnings("ignore")


class DataLoader:
    # returns dataframes
    def __init__(self):
        pass

    @staticmethod
    def load_product_data(file_path, sheet_names) -> list(pd.DataFrame):
        """
        Load product data from Excel file.

        Parameters:
        - file_path: Path to the Excel file
        - sheet_names: List of sheet names to load

        Returns:
        - List of DataFrames for each sheet
        """
        product_data = load_workbook(filename=file_path, data_only=True)
        dataframes = [
            pd.DataFrame(DataLoader.get_values(product_data[sheet_name]))
            for sheet_name in sheet_names
        ]
        return dataframes

    @staticmethod
    def load_questionnaire_data(user_id) -> tuple[pd.DataFrame, pd.DataFrame]:
        """
        Load questionnaire answers for a specific user.

        Parameters:
        - user_id: ID of the user

        Returns:
        - Data<PERSON>rame with questionnaire answers
        """
        user = User.objects.get(id=user_id)
        user_profile = UserProfile.objects.get(user=user)

        # Define your Question objects here
        goal = Question.objects.get(position=1)
        issue = Question.objects.get(position=2)
        hair_overall = Question.objects.get(position=3)
        hair_type = Question.objects.get(position=6)
        split_ends = Question.objects.get(position=7)
        # density = Question.objects.get(position=8)
        hair_texture = Question.objects.get(position=9)
        health_condition = Question.objects.get(position=14)
        fever = Question.objects.get(position=15)
        alergies = Question.objects.get(position=16)
        prescription = Question.objects.get(position=17)
        iron_def = Question.objects.get(position=18)
        diet = Question.objects.get(position=20)
        suplementation = Question.objects.get(position=21)
        skip_meals = Question.objects.get(position=22)
        daily_water = Question.objects.get(position=24)
        general_health = Question.objects.get(position=25)
        fitness = Question.objects.get(position=26)
        workout_freq = Question.objects.get(position=27)
        where_workout = Question.objects.get(position=28)
        stress = Question.objects.get(position=29)
        hair_feels = Question.objects.get(position=30)
        washingfrequency = Question.objects.get(position=31)
        water_damage = Question.objects.get(position=32)
        product_usage = Question.objects.get(position=33)
        treatments = Question.objects.get(position=34)
        enhancers = Question.objects.get(position=35)
        porosity = Question.objects.get(position=37)
        styling_hair = Question.objects.get(position=38)
        heat_freq = Question.objects.get(position=39)  # ApplyHeat question
        routine_sugestion = Question.objects.get(position=40)
        scalp_feeling = Question.objects.get(position=41)
        scalp_flaky = Question.objects.get(position=42)
        scalp_oily = Question.objects.get(position=43)
        scalp_dry = Question.objects.get(position=44)
        preferences = Question.objects.get(position=45)

        # Define your Question objects here

        recom_answers_columns = [
            "Customer_ID",
            "Goal",
            "Issue",
            "Hair Type",
            "Hair Texture",
            "hair_feels",
            "treatments",
            "washingfrequency",
            "enhancers",
            "styling_hair",
            "split_ends",
            "health_condition",
            "fever",
            "alergies",
            "prescription",
            "iron_def",
            "diet",
            "suplementation",
            "skip_meals",
            "general_health",
            "fitness",
            "workout_freq",
            "where_workout",
            "stress",
            "water_damage",
            "product_usage",
            "heat_freq",
            "scalp_feeling",
            "scalp_flaky",
            "scalp_oily",
            "scalp_dry",
            "preferences",
            "routine_sugestion",
        ]

        condition_score_answers_columns = [
            "customer_id",
            "Issue ",
            "hair_feels",
            "treatments",
            "enhancers",
            "styling_hair",
            "split_ends",
            "heat_freq",
            "scalp_feeling",
            "scalp_flaky",
            "scalp_oily",
            "scalp_dry",
            "water_damage",
        ]

        # The actual data
        recom_answers_data = [
            user.id,
            goal,
            issue,
            user_profile,
            hair_type,
            hair_texture,
            hair_feels,
            treatments,
            washingfrequency,
            enhancers,
            styling_hair,
            split_ends,
            health_condition,
            fever,
            alergies,
            prescription,
            iron_def,
            diet,
            suplementation,
            skip_meals,
            general_health,
            fitness,
            workout_freq,
            where_workout,
            stress,
            water_damage,
            product_usage,
            heat_freq,
            scalp_feeling,
            scalp_flaky,
            scalp_oily,
            scalp_dry,
            preferences,
            routine_sugestion,
        ]

        condition_score_answers_data = [
            user.id,
            issue,
            hair_overall,
            split_ends,
            daily_water,
            hair_feels,
            treatments,
            enhancers,
            porosity,
            styling_hair,
            heat_freq,
            scalp_feeling,
            scalp_flaky,
            scalp_oily,
            scalp_dry,
            water_damage,
        ]

        recom_answers = pd.DataFrame(
            data=[recom_answers_data], columns=recom_answers_columns
        )
        condition_score_answers = pd.DataFrame(
            data=[condition_score_answers_data], columns=condition_score_answers_columns
        )

        return recom_answers, condition_score_answers

    @staticmethod
    def get_values(sheet) -> list:
        """
        Extracts non-empty rows from a sheet.

        Parameters:
        - sheet: Excel sheet object

        Returns:
        - List of non-empty rows
        """
        results = []
        for row in sheet.values:
            if any(value for value in row):
                results.append(row)
        return results
