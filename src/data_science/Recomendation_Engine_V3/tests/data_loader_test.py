import unittest
from unittest.mock import MagicMock
from django.contrib.auth.models import User
from users.models import UserProfile
from questionaire.models import Question
from src.data_science.Recomendation_Engine_V3.src import DataLoader


class TestDataLoader(unittest.TestCase):
    def setUp(self):
        # Create a user for testing
        self.user = User.objects.create_user(username="testuser", password="testpass")
        self.user_profile = UserProfile.objects.create(user=self.user)

        # Create mock Question objects
        self.questions = []
        for position in range(1, 46):
            question = MagicMock(spec=Question)
            question.position = position
            self.questions.append(question)

    def test_load_questionnaire_data(self):
        # Mock the User.objects.get and UserProfile.objects.get methods
        User.objects.get = MagicMock(return_value=self.user)
        UserProfile.objects.get = MagicMock(return_value=self.user_profile)

        # Mock the Question.objects.get method to return the mock questions
        Question.objects.get = MagicMock(
            side_effect=lambda **kwargs: next(
                q for q in self.questions if q.position == kwargs["position"]
            )
        )

        # Call the method being tested
        recom_answers, condition_score_answers = DataLoader.load_questionnaire_data(
            user_id=self.user.id
        )

        # Assert that the returned DataFrames are not empty
        self.assertFalse(recom_answers.empty)
        self.assertFalse(condition_score_answers.empty)

    def test_load_product_data(self):
        # Assuming load_product_data is expected to return a list of non-empty DataFrames
        file_path = "path/to/excel/file.xlsx"
        sheet_names = ["Sheet1", "Sheet2"]

        # Call the method being tested
        dataframes = DataLoader.load_product_data(
            file_path=file_path, sheet_names=sheet_names
        )

        # Assert that the returned list is not empty and contains non-empty DataFrames
        self.assertTrue(dataframes)
        for df in dataframes:
            self.assertFalse(df.empty)


if __name__ == "__main__":
    unittest.main()
