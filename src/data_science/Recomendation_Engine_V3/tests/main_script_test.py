import unittest
import pandas as pd
from src.data_science.Recomendation_Engine_V3.src import RecommendationEngine


class TestRecommendationEngine(unittest.TestCase):
    def setUp(self):
        # Create sample data for testing
        # Replace these with your actual data or use a testing framework for more advanced setups
        self.results_dataframe = pd.DataFrame(
            {
                "Hair_TypeID": [1, 2, 3, 1],
                "PorosityID": [13, 14, 15, 13],
                "GoalsID": [16, 17, 18, 16],
            }
        )

        self.d_cs = pd.DataFrame(
            {
                "Hair_TypeID": [1, 2, 3, 1],
                "PorosityID": [13, 14, 15, 13],
                "GoalsID": [16, 17, 18, 16],
            }
        )

    def test_generate_recommendations(self):
        # Instantiate the RecommendationEngine class
        recommendation_engine = RecommendationEngine()

        # Call the recommendation method with the sample data
        shamp_recommendations, cond_recommendations = (
            recommendation_engine.generate_recommendations(
                self.results_dataframe, self.d_cs
            )
        )

        # Add your assertions based on the expected output
        # For example, check if the output has the expected structure, length, or specific values

        # Assert that the recommendations are not empty
        self.assertTrue(shamp_recommendations)
        self.assertTrue(cond_recommendations)

        # Add more specific assertions based on your requirements
        # For instance, check the length of the recommendations, data types, etc.
        self.assertEqual(len(shamp_recommendations), 4)
        self.assertEqual(len(cond_recommendations), 4)

        # Add more assertions based on your specific requirements


if __name__ == "__main__":
    unittest.main()
