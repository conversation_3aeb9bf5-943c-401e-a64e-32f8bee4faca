import unittest
from unittest.mock import MagicMock
from src.data_science.Recomendation_Engine_V3.src import ConditionScores
from src.data_science.Recomendation_Engine_V3.src import DataLoader
import pandas as pd
from users.models import UserProfile


class YourTestCase(unittest.TestCase):
    def setUp(self):
        # Create an instance of the ConditionScores class
        self.condition_scores = ConditionScores()

    def test_get_condition_scores(self):
        # Mocking the DataLoader class and its method
        with unittest.mock.patch(DataLoader) as mock_data_loader:
            # Mock the load_questionnaire_data method to return a sample DataFrame
            mock_data_loader.return_value.load_questionnaire_data.return_value = (
                self.create_sample_data()
            )

            # Mock the User class and its method
            mock_user = MagicMock()
            mock_user.id = 1
            mock_user_instance = MagicMock(return_value=mock_user)
            with unittest.mock.patch(UserProfile, mock_user_instance):
                # Call the method under test
                result = self.condition_scores.get_condition_scores(mock_user)

        # Assertions based on expected results
        self.assertIsInstance(result, pd.DataFrame)
        self.assertEqual(result.shape, (3, 7))

    def create_sample_data(self):
        # Sample DataFrame for mocking the DataLoader class
        # Adjust the data based on actual questionnaire data structure
        data = {
            "customer_id": [1, 2, 3],
            "hair_behaviour": [
                "A -Repels moisture when wet",
                "B - Its bouncy and elastic",
                "C- Easily absorbs water",
            ],
            "Issue ": [
                "Scalp dryness|breakage|Split|Thinning",
                "Not me",
                "Scalp dryness|breakage|Split|Thinning",
            ],
            "hair_overal": [
                "Generally Dry|Flaky Scalp + Dry Ends",
                "Oily roots + dry ends",
                "Fairly balanced / No outstanding issues",
            ],
            "daily_water": ["Quarter Litre", "Half a Litre", "1 Litre or more"],
            "hair_feels": [
                "Weighed down|Limp|Not responding to anything|None of these / Normal",
                "Coarse and dry",
                "Weighed down|Limp|Not responding to anything|None of these / Normal",
            ],
            "treatments": [
                "Relaxers|Perm|Keratin|Bleach|Colour / Dyed",
                "No not me",
                "Relaxers|Perm|Keratin|Bleach|Colour / Dyed",
            ],
            "enhancers": [
                "Natural extensions|Synthetic extensions|Weave",
                "No not me",
                "Natural extensions|Synthetic extensions|Weave",
            ],
            "styling_hair": [
                "Blow dryer|Straightening iron|Curling iron/wand",
                "No stylers for me",
                "Blow dryer|Straightening iron|Curling iron/wand",
            ],
            "split_ends": ["Yes", "No", "Yes"],
            "heat_freq": [
                "Everyday or every other day",
                "Once or twice a week",
                "Once or twice a month",
            ],
            "scalp_feeling": [
                "A little bit tight",
                "Tight & Itchy|Itchy & painful|Itchy|Sensitive",
                "None of the above",
            ],
            "scalp_flaky": ["Rarely", "Usually", "Always"],
            "scalp_oily": ["1 - 2 Days", "3 - 4 Days", "5"],
            "scalp_dry": ["Within hours", "1 - 2 Days", "3 - 5 Days"],
            "water_damage": [
                "Your hair colour fades easily or dulls rapidly? | Do you have problems with your shampoo lathering? | Your hair feels really dry no matter how much you wash or deep condition it? | Do you have chronic hair breakage problems that are not fixed by previous treatment or care?",
                "None of the above",
                "Your hair colour fades easily or dulls rapidly? | Do you have problems with your shampoo lathering? | Your hair feels really dry no matter how much you wash or deep condition it? | Do you have chronic hair breakage problems that are not fixed by previous treatment or care?",
            ],
        }
        return pd.DataFrame(data)


if __name__ == "__main__":
    unittest.main()
