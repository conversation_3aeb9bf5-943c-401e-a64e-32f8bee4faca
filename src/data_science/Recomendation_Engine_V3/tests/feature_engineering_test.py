import unittest
from unittest.mock import patch, MagicMock
import pandas as pd

# from users.models import UserProfile
from src.data_science.Recomendation_Engine_V3.src import FeatureEngineering


class TestFeatureEngineering(unittest.TestCase):
    @patch("src.data_science.Recomendation_Engine_V3.src.DataLoader")
    @patch("users.models.User.objects.get")
    @patch("users.models.UserProfile.objects.get")
    def test_run_feature_engineering(
        self, mock_user_profile_get, mock_user_get, mock_data_loader
    ):
        # Mocking User and UserProfile objects
        user = MagicMock()
        user.id = 1
        mock_user_get.return_value = user

        user_profile = MagicMock()
        mock_user_profile_get.return_value = user_profile

        # Mocking DataLoader
        mock_data_loader_instance = MagicMock()
        mock_data_loader_instance.load_questionnaire_data.return_value = (
            pd.DataFrame({"Customer_ID": [1]}),
            pd.DataFrame({"Hair_Type": ["Type 3b"]}),
            pd.DataFrame({"Prod_Info": ["Some data"]}),
        )
        mock_data_loader.return_value = mock_data_loader_instance

        # Create an instance of FeatureEngineering
        feature_engineering = FeatureEngineering(user.id)

        # Call run_feature_engineering method
        result_dataframe = feature_engineering.run_feature_engineering()

        # Assert that the result DataFrame has the expected columns and values
        expected_columns = ["Customer_ID", "Hair_Type", "Prod_Info"]
        self.assertListEqual(list(result_dataframe.columns), expected_columns)


if __name__ == "__main__":
    unittest.main()
