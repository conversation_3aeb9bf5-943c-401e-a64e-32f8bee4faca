# from behave import *
# from selenium import webdriver


# @given("the web portal is running")
# def step_impl(context):
#     context.browser.get("http://localhost:8000")
#     # raise NotImplementedError("STEP: Given the web portal is running")


# @when("user lands on homepage")
# def step_impl(context):
#     elements = get_elements
#     # raise NotImplementedError("STEP: When user lands on homepage")


# @then("the Home, About Login and Register links are present")
# def step_impl(context):
#     assert context.failed is False
#     # raise NotImplementedError(
#     # "STEP: Then the Home, About Login and Register links are present"
#     # )
