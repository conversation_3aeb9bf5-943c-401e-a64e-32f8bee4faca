User Journey MVP
Basic user - just browsing every page that does not require login
Visit
User arrive home page.
Can see overview about Cosmetrics ai
Can see links page with further information.
Can use to do research.
Can see how to use the service.
Can register for the service.
Provide information to answer user questions.
Navigation - decides which info pages to review (e.g.) How it works, About us, Hair ID, FAQ,
Discover
Has access to review the available pages.
Can start the first 5 questions on the questionnaire but must login to continue.
Every page they go to has a link to register for the service.
Discuss
Can ask questions or send enquiry.
Is asked to register for the service.
Customer goal
Browse & find information easily.
See the benefits of the service.
Business goal – Provide information that encourages user to register for the service.


User registration journey
Visit
User arrive home page.
Can see a box to register.
Can see links to further information.
Navigation
fill in the form with full name.
Provides <PERSON><PERSON>
Creates a password.


Discover
Has access to review all parts of the site.
Can take the full questionnaire.

Customer goal - Register to use the service fully.
Business goal
Capture user details for login
Create unique ID for each registered user.

A user with login but nothing else
Visit
Can see links to further information.
Is asked to take the questionnaire.
Navigation - User can see all aspects of the site.
Discover
Has access to review all parts of the site.
Can take the full questionnaire.
Customer goal – user the service fully.
Business goal – encourage the user to start the process.

A registered user with submitted questionnaire.

Visit
They have a profile.
They can login to dashboard
Can see the information they have provided
Can see recommendations.
Can see report.
Can see links to further information.
See when questionnaire needs to be taken again (every 16 weeks)


Navigation
User see dashboard Hair ID, Recommendation, Report, Condition score
Provide updates / notes.
Provide feedback about recommendation.

Discover - has access to current information about themselves.
Has advice on what to do next

Customer goal – Review the information about themselves.
Provide feedback to us
Receive updates on what to do next

Business goal – Collect feedback.
Evaluate effectiveness of recommendation

A user recommendation and report
Visit –
The user receives the recommendation via email.
They user received the report via email
On the dashboard they can see recommendations.
On the dashboard they can see the report.
Can see links to further information.
See when questionnaire needs to be taken again (every 16 weeks)
Navigation
On the dashboard they can see recommendations.
On the dashboard they can see the report
Provide updates / notes.
Provide feedback about recommendation.
Discover
Which products to use
What actions to take to improve hair health via the report

Customer goal
Receive product recommendations.
Receive report with insights about them.
Buy recommendations.
feedback about recommendation

Business goal
Seamlessly provide product recommendation and report.
Provide accurate information to the user
Enable user to buy the product.
Collect feedback about recommendation.


A user that wants to de-register/delete account.

Visit -
Login to profile
Can see options menu
Save profile information is an option
Delete account is an option.
User selects delete account.
Account is deleted.
User is moved back to main site / registration page

Customer goal – Delete account.

Business goal – Remove user access to the main site features and insights.
