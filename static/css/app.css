@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap");

*,
*::before,
*::after {
    font-family: 'Inter', sans-serif;
}

a {
    text-decoration: none;
}

.custom-btn {
    background: #7b8ede;
    border: 0;
    color: #fff;
    padding: 10px 15px;
    transition: all 250ms ease-in-out;
    width: 110px;
    display: inline-block;
    text-align: center;
}

.custom-btn:hover {
    background: #673a90;
    color: white;
}

.gradient-btn {
    background-image: linear-gradient(to right, #4cc8f2, #7b8ede);
    color: white;
    text-decoration: none;
    font-weight: bold;
    font-size: 1.6rem;
    transition: all 500ms ease-in-out;
    padding: 20px 40px; /* Fixed duplicate padding property */
}

.gradient-btn:hover {
    background-image: linear-gradient(to right, #7b8ede, #4cc8f2);
    color: white;
}

.site-header {
    width: 80%;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 5rem;
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    z-index: 100;
}

.site-header .logo img {
    width: 210px;
}

nav a {
    color: #7b8ede;
    margin-left: 20px;
    font-size: 1.2rem;
    transition: all 250ms ease-in-out;
}

nav a svg {
    font-size: 1.8rem;
}

nav a:hover {
    color: #4cc8f2;
}

.burger {
    position: fixed;
    top: 3rem;
    bottom: 50%;
    right: 20px;
    width: 35px;
    height: 35px;
    cursor: pointer;
    transition: all 500ms ease-in-out;
    z-index: 40;
    background-color: #fff;
}

.burger:hover {
    height: 30px;
}

.burger-bar {
    background-color: #7b8ede;
    display: block;
    width: 100%;
    height: 2px;
    border-radius: 3px;
    position: relative; /* Added position property */
}

.burger-bar::before,
.burger-bar::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    background-color: #7b8ede;
    border-radius: 3px;
    transition: all 500ms ease-in-out;
}

.burger-bar::before {
    transform: translateY(-10px);
}

.burger-bar::after {
    transform: translateY(10px);
}

.burger.open .burger-bar {
    background: transparent;
}

.burger.open .burger-bar::before {
    transform: rotate(45deg);
    background: #7b8ede;
}

.burger.open .burger-bar::after {
    transform: rotate(-45deg);
    background: #7b8ede;
}

@media (min-width: 960px) {
    .burger {
        display: none;
    }
}

@media (max-width: 960px) {
    nav {
        background: transparent;
        display: flex;
        flex-direction: column;
        align-items: center;
        padding-top: 2rem;
        background-color: #fff;
        position: fixed;
        right: -250px;
        top: 90px;
        width: 250px;
        height: 100%;
        z-index: 5;
        transition: all 350ms cubic-bezier(0.075, 0.82, 0.165, 1);
    }

    nav a {
        padding: 20px 0;
    }

    nav .custom-btn {
        margin: 20px 0;
    }

    nav.active {
        right: 0;
    }

    .site-header .logo img {
        width: 180px;
        position: absolute;
        left: 20px;
        top: 10px;
    }
}

.banner {
    background: linear-gradient(180deg, rgba(76, 200, 242, 0.95), rgba(4, 76, 134, 0.7)), url("../../../static/images/dasboard-background.jpg");
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    width: 100vw;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media screen and (max-width: 480px) {
    .banner {
        width: 100%;
        margin: 0 auto;
    }
}

.text-center {
    color: #fff;
}

.text-center h1 {
    font-size: 6.5rem;
    border-bottom: 4px solid #fff;
}

@media screen and (max-width: 480px) {
    .text-center h1 {
        font-size: 1.8rem;
        border-bottom: 1px solid #fff;
        font-weight: bold;
        padding-bottom: 20px;
    }
}

.text-center h2 {
    margin: 30px 0;
    font-size: 2.5rem;
}

@media screen and (max-width: 480px) {
    .text-center h2 {
        font-size: 1.1rem;
    }
}

.text-center p {
    font-size: 1.5rem;
    margin-bottom: 60px;
}

@media screen and (max-width: 480px) {
    .text-center p {
        font-size: 0.8rem;
    }
}

@media screen and (max-width: 480px) {
    .text-center a {
        padding: 10px 20px;
    }
}

.steps-intro-box {
    background: white;
    padding: 20px;
    margin-top: -3rem;
    border-radius: 5px;
    box-shadow: 1px 3px 5px rgba(0, 0, 0, 0.05);
    position: relative;
}

.steps-intro-box h4 {
    color: #fff;
    font-size: 20px;
}

.steps-intro-box svg {
    color: #fff;
    font-size: 30px;
    margin-bottom: 20px;
}

.steps-intro-box a {
    display: inline-block;
    margin: 20px 0;
}

.discover-your-hair {
    padding: 5rem 0;
}

.discover-your-hair h3 {
    font-size: 50px;
    font-weight: bold;
    color: #fff;
}

.dash-item {
    margin-top: 30px;
}

.hair-illustration {
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 992px) {
    .hair-illustration {
        flex-direction: column;
    }
}

.hair-illustration-left,
.hair-illustration-right,
.hair-illustration-middle {
    color: #fff;
}

.hair-illustration img {
    width: 100%;
}

.hair-illustration p {
    color: #fff;
    font-size: 18px;
    text-align: center;
    margin: 15px 0;
    box-shadow: 1px 3px 5px rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    padding: 10px 20px;
    height: 120px;
}

.hair-illustration h5 {
    color: #7b8ede;
}

.site-footer {
    background-color: #fff;
    color: #7b8ede;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    padding: 20px;
    border-top: 1px solid #4cc8f2;
}

@media (max-width: 960px) {
    .site-footer {
        text-align: center;
    }
}

.site-footer a {
    text-decoration: none;
    color: #7b8ede;
    font-weight: bold;
}

.site-footer p {
    font-weight: bold;
    margin-bottom: 0px;
}

.form-container {
    width: 600px;
    padding: 20px 40px;
    box-shadow: 1px 1px 10px 3px rgba(0, 0, 0, 0.2);
    z-index: 10;
    margin: 100px auto;
    background-color: #fff;
    backdrop-filter: blur(10px);
}

.form-container h1 {
    text-align: center;
    text-transform: uppercase;
    color: #000EBA;
    font-weight: bold;
    margin-bottom: 30px;
}

.form-container label {
    color: #000222;
    font-size: 1.5rem;
    font-weight: 300;
}

.form-container .fieldWrapper {
    margin: 15px 0;
}

.form-container .input_field {
    border: none;
    outline: none;
    width: 100%;
    background: transparent;
    padding: 5px 0;
    font-size: 1.2rem;
    color: #000EBA;
    border-bottom: 1px solid #7b8ede;
}

.form-container .input_field:focus {
    outline: none;
    background: transparent;
}

.form-container .container-btn {
    max-width: 100%;
    display: flex;
    justify-content: center;
}

/* Dashboard Header */
.dashboard-header {
    background: linear-gradient(0deg, rgba(76, 200, 242, 0.3), rgba(76, 200, 242, 0.3)), url("../../../static/images/dasboard-background.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    height: 60vh;
}

/* Dashboard Section */
.dashboard-section {
    padding: 10rem auto;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url("../../../static/images/dashboard-2.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center center;
    height: 70vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

/* Dashboard Container */
.dashboard-container {
    width: 80%;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
}

@media screen and (max-width: 960px) {
    .dashboard-container {
        width: 100%;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
}

/* Item Container */
.item-container {
    width: 33.3%;
    height: 150px;
    margin-bottom: 10px;
}

@media screen and (max-width: 960px) {
    .item-container {
        height: 100px;
        width: 100%;
    }
}

.item-container .dashboard-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    margin: 0 5px;
    transition: transform 0.5s, box-shadow 0.5s;
}

.item-container .dashboard-item:hover {
    transform: scale(1.1);
    box-shadow: 5px 20px 30px rgba(0, 0, 0, 0.2);
}

.item-container .dashboard-icon {
    font-size: 2.2rem;
    color: #fff;
    margin-bottom: 15px;
}

.item-heading {
    text-align: center;
    color: #fff;
    text-transform: uppercase;
    font-size: 1.3rem;
}

@media screen and (max-width: 960px) {
    .item-heading {
        font-size: 1rem;
    }
}

/* Dashboard Item Background Colors */
.dash-1 {
    background-color: rgba(181, 82, 198, 0.6);
}

.dash-2 {
    background-color: rgba(76, 200, 242, 0.6);
}

.dash-3 {
    background-color: rgba(102, 58, 144, 0.6);
}

/* Form Container */
.form-container .profile-heading {
    text-transform: uppercase;
    color: #7b8ede;
    margin-top: 10px;
    text-align: center;
    font-size: 1.8rem;
}

.form-container label {
    color: #7b8ede;
}

.form-container .container-btn {
    margin: 15px 0;
}

/* Recommendations Container */
.recommendations-container {
    width: 100vw;
    overflow: hidden;
}

.rec-header {
    height: 40vh;
    background: center url("../../../static/images/rec-header.jpg");
    padding: 0;
}

.rec-heading {
    text-align: center;
    color: #fff;
    background-color: #7b8ede;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin: 0 auto;
}

.rec-heading h1 {
    font-size: 3.5rem;
}

@media screen and (max-width: 960px) {
    .rec-heading h1 {
        font-size: 1.7rem;
    }
}

.rec-heading p {
    font-size: 1.5rem;
}

@media screen and (max-width: 960px) {
    .rec-heading p {
        font-size: 1.2rem;
    }
}

.rec-desc {
    font-size: 1rem;
}

@media (max-width: 480px) {
    .rec-desc {
        font-size: 0.7rem;
    }
}

.error-message {
    background-color: black;
    padding: 10px 32px;
}

/* Recommendations Section */
.rec-section {
    max-width: 70%;
    margin: 5rem auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.products-container {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin: 0 20px;
    padding: 0 60px;
}

@media screen and (max-width: 760px) {
    .products-container {
        flex-direction: column;
    }
}

.product-item {
    width: 350px;
    margin: 0 30px;
    display: flex;
    flex-direction: column;
    transform: scale(0.95);
    transition: box-shadow 0.5s, transform 0.5s;
    cursor: pointer;
}

.product-item:hover {
    transform: scale(1);
    box-shadow: 5px 20px 30px rgba(0, 0, 0, 0.2);
}

.image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 30px;
    border-bottom: 1px solid #4cc8f2;
}

.card-img-top {
    width: 100%;
    height: 400px;
}

@media (max-width: 960px) {
    .card-img-top {
        height: 240px;
        width: 100px;
    }
}

.text-container {
    padding: 30px;
    display: flex;
    flex-direction: column;
}

.text-container p {
    max-width: 100%;
    padding: 10px;
    font-weight: bold;
    text-transform: uppercase;
}

.icon-container {
    width: 100%;
    background-color: #7b8ede;
    height: 50px;
    position: relative;
}

.icon-container p {
    color: #fff;
    position: absolute;
    left: 0;
    text-transform: uppercase;
}

.shop {
    color: #fff;
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 1.3rem;
}

/* How Header */
.how-header {
    height: 50vh;
    background: linear-gradient(0deg, rgba(76, 200, 242, 0.3), rgba(76, 200, 242, 0.3)), url("../../../static/images/how-page.jpg");
    background-size: cover;
    background-repeat: no-repeat;
    position: relative;
}

.how-header h1 {
    color: #dc934e;
    font-size: 6rem;
    position: absolute;
    top: 50%;
    left: 10%;
}

/* Steps */
.steps {
    max-width: 60%;
    margin: 5rem auto;
    text-align: right;
}

@media (max-width: 960px) {
    .steps {
        text-align: center;
        max-width: 100%;
        margin: 2rem 0;
    }
}

.steps h2 {
    font-size: 3rem;
}

.steps ul {
    list-style: none;
    padding: 0;
}

.steps ul li {
    font-size: 1.5rem;
    font-weight: 700;
}

.survey {
    margin: 15px 0;
}

.survey a {
    color: #fff;
    background-color: #dc934e;
    padding: 15px 40px;
    font-size: 1.2rem;
    margin-top: 40px;
}

.steps-details {
    max-width: 80%;
    margin: 5rem auto;
}


@media (max-width: 960px) {
    .steps-details {
        max-width: 100%;
    }
}

.steps-details .steps-heading h3 {
    font-size: 1.8rem;
    text-align: center;
    margin-bottom: 10px;
}

.steps-details .steps-heading h4 {
    text-align: center;
}

.steps-details .questionnaire-sec {
    margin-top: 5rem;
    height: 550px;
}

@media (max-width: 960px) {
    .steps-details .questionnaire-sec {
        margin: 3rem 0;
        height: auto;
        text-align: center;
    }

    .steps-details .questionnaire-sec .quest-image-container {
        display: none;
    }

    .steps-details .questionnaire-sec .gradient-btn {
        padding: 10px 15px;
        font-weight: 100;
    }
}

.steps-details .questionnaire-sec .quest-text h5 {
    font-size: 2rem;
}

.steps-details .questionnaire-sec .quest-text p {
    font-size: 1.3rem;
    margin-bottom: 40px;
}

.steps-details .questionnaire-sec .quest-text a {
    font-size: 1.25rem;
}

.steps-details .questionnaire-sec .quest-image-container {
    overflow: hidden;
    height: 100%;
}

.steps-details .questionnaire-sec .quest-image-container .quest-image {
    width: 80%;
}

.home-section-container {
    margin-top: 50px;
}

.home-section-container .section-heading {
    text-align: center;
}

.home-section-container .section-sub-heading {
    text-align: center;
}

.home-section-container .section-text {
    text-align: center;
}

.home-section-container span {
    color: aqua;
}

.home-section-container .section-text {
    font-size: 1.3rem;
}

.home-section-container .section-text span {
    font-weight: bold;
    color: black;
}

.home-section {
    margin: 60px auto;
}

@media screen and (max-width: 960px) {
    .home-section {
        display: flex;
        flex-direction: column;
    }
}

@media screen and (max-width: 960px) {
    .left {
        display: none;
    }
}

.left h4 {
    font-size: 2.5rem;
    font-weight: bold;
}

.left p {
    font-size: 1.3rem;
    font-weight: 100;
    line-height: 2rem;
    margin-bottom: 30px;
}

.left p span {
    font-weight: bold;
    color: #000;
}

.left ul {
    font-size: 1.3rem;
    list-style: none;
    margin-bottom: 30px;
}

section blockquote {
    text-align: center;
}

section blockquote p {
    font-size: 2rem;
    font-weight: 100;
}

@media screen and (max-width: 480px) {
    section blockquote p {
        font-size: 1.2rem;
    }
}

section blockquote p:before {
    content: open-quote;
    font-size: 100px;
    color: gray;
}

.love-hair {
    background: url("../../../static/images/photo-section-quote.jpeg");
    background-position: center;
    height: 40vh;
    position: relative;
}

.love-hair p {
    position: absolute;
    top: 50%;
    left: 50%;
    color: #fff;
    font-size: 3rem;
}

@media screen and (max-width: 480px) {
    .love-hair p {
        font-size: 1.2rem;
    }
}

.love-hair-text {
    margin-top: 3rem;
    max-width: 600px;
}

@media screen and (max-width: 480px) {
    .love-hair-text {
        font-size: 1.2rem;
    }
}

.love-hair-text h3 {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 2rem;
}

.love-hair-text p {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 100;
    line-height: 50px;
    margin-bottom: 3rem;
}

@media screen and (max-width: 480px) {
    .love-hair-text p {
        font-size: 1.1rem;
        line-height: 1.5;
    }
}

.love-info {
    display: flex;
}

.love-thumbnail {
    border-radius: 50%;
    height: 95px;
    width: 80px;
}

.love-text {
    text-align: start;
    margin-left: 20px;
    max-width: 350px;
}

.love-text p {
    text-align: start;
}

.love-improve {
    max-width: 700px;
    margin: 20px auto;
}

.love-improve h4 {
    text-align: center;
    font-size: 2.5rem;
    font-weight: bold;
}

.love-improve p {
    text-align: center;
    font-size: 2rem;
    font-style: italic;
    font-weight: 100;
}

/*.report-section {*/
/*    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1002%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(231%2c 235%2c 237%2c 1)'%3e%3c/rect%3e%3cpath d='M21.4 -20.4L86.35 17.1L86.35 92.1L21.4 129.6L-43.55 92.1L-43.55 17.1zM21.4 204.6L86.35 242.1L86.35 317.1L21.4 354.6L-43.55 317.1L-43.55 242.1zM86.35 542.1L151.31 579.6L151.31 654.6L86.35 692.1L21.4 654.6L21.4 579.6zM216.26 317.1L281.21 354.6L281.21 429.6L216.26 467.1L151.31 429.6L151.31 354.6zM411.12 429.6L476.08 467.1L476.08 542.1L411.12 579.6L346.17 542.1L346.17 467.1zM541.03 -20.4L605.98 17.1L605.98 92.1L541.03 129.6L476.08 92.1L476.08 17.1zM670.94 -20.4L735.89 17.1L735.89 92.1L670.94 129.6L605.98 92.1L605.98 17.1zM800.84 429.6L865.8 467.1L865.8 542.1L800.84 579.6L735.89 542.1L735.89 467.1zM865.8 542.1L930.75 579.6L930.75 654.6L865.8 692.1L800.84 654.6L800.84 579.6zM930.75 429.6L995.71 467.1L995.71 542.1L930.75 579.6L865.8 542.1L865.8 467.1zM1060.66 -20.4L1125.61 17.1L1125.61 92.1L1060.66 129.6L995.71 92.1L995.71 17.1zM1320.48 429.6L1385.43 467.1L1385.43 542.1L1320.48 579.6L1255.52 542.1L1255.52 467.1z' stroke='rgba(123%2c 142%2c 222%2c 1)' stroke-width='2'%3e%3c/path%3e%3cpath d='M13.9 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 204.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0z' fill='rgba(123%2c 142%2c 222%2c 1)'%3e%3c/path%3e%3cpath d='M146.22 211.17L189.52 236.17L189.52 286.17L146.22 311.17L102.92 286.17L102.92 236.17zM189.52 136.17L232.82 161.17L232.82 211.17L189.52 236.17L146.22 211.17L146.22 161.17zM232.82 361.17L276.13 386.17L276.13 436.17L232.82 461.17L189.52 436.17L189.52 386.17zM276.13 136.17L319.43 161.17L319.43 211.17L276.13 236.17L232.82 211.17L232.82 161.17zM449.34 436.17L492.64 461.17L492.64 511.17L449.34 536.17L406.03 511.17L406.03 461.17zM492.64 511.17L535.94 536.17L535.94 586.17L492.64 611.17L449.34 586.17L449.34 536.17zM579.24 511.17L622.54 536.17L622.54 586.17L579.24 611.17L535.94 586.17L535.94 536.17zM665.85 361.17L709.15 386.17L709.15 436.17L665.85 461.17L622.54 436.17L622.54 386.17zM795.75 136.17L839.06 161.17L839.06 211.17L795.75 236.17L752.45 211.17L752.45 161.17zM925.66 61.17L968.97 86.17L968.97 136.17L925.66 161.17L882.36 136.17L882.36 86.17zM968.97 -13.83L1012.27 11.17L1012.27 61.17L968.97 86.17L925.66 61.17L925.66 11.17zM1012.27 211.17L1055.57 236.17L1055.57 286.17L1012.27 311.17L968.97 286.17L968.97 236.17zM968.97 286.17L1012.27 311.17L1012.27 361.17L968.97 386.17L925.66 361.17L925.66 311.17zM968.97 436.17L1012.27 461.17L1012.27 511.17L968.97 536.17L925.66 511.17L925.66 461.17zM1185.48 211.17L1228.78 236.17L1228.78 286.17L1185.48 311.17L1142.17 286.17L1142.17 236.17zM1142.17 436.17L1185.48 461.17L1185.48 511.17L1142.17 536.17L1098.87 511.17L1098.87 461.17zM1315.38 -13.83L1358.69 11.17L1358.69 61.17L1315.38 86.17L1272.08 61.17L1272.08 11.17zM1358.69 61.17L1401.99 86.17L1401.99 136.17L1358.69 161.17L1315.38 136.17L1315.38 86.17zM1315.38 436.17L1358.69 461.17L1358.69 511.17L1315.38 536.17L1272.08 511.17L1272.08 461.17zM1401.99 136.17L1445.29 161.17L1445.29 211.17L1401.99 236.17L1358.69 211.17L1358.69 161.17zM1445.29 211.17L1488.6 236.17L1488.6 286.17L1445.29 311.17L1401.99 286.17L1401.99 236.17z' stroke='rgba(76%2c 200%2c 242%2c 1)' stroke-width='2'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1002'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");*/
/*    display: flex;*/
/*    flex-direction: column;*/
/*    justify-content: center;*/
/*    align-items: center;*/
/*    background-repeat: no-repeat;*/
/*    background-size: cover;*/
/*    min-height: 100vh;*/
/*    overflow: auto;*/
/*}*/

.report-section h2 {
    font-size: 3.5rem;
    color: #fff;
    text-align: center;
    text-transform: uppercase;
}

.report-section .report-container {
    margin: 60px auto;
}

.report-section .report-container .report-item-container {
    width: 100%;
    border-bottom: 1px solid #4cc8f2;
    margin-bottom: 5px;
    border-radius: 5px;
    padding: 20px;
    background-color: #4cc8f2;
    display: flex;
    justify-content: space-between;
}

@media (max-width: 960px) {
    .report-section .report-container .report-item-container {
        flex-direction: column;
    }
}

.report-section .report-container .report-item-container p {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 100;
    color: #000;
}

@media (max-width: 960px) {
    .report-section .report-container .report-item-container p {
        border-bottom: 1px solid #fff;
        padding: 5px 0;
    }
}

.report-section .report-container .report-item-container span {
    padding-top: 10px;
    font-weight: bold;
}

.about-banner {
    background: linear-gradient(0deg, rgba(76, 200, 242, 0.95), rgba(76, 200, 242, 0.3)), url("../../../static/images/noroot.png");
    background-position: top;
    background-size: cover;
    height: 70vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

@media (max-width: 960px) {
    .about-banner {
        display: none;
    }
}

.about-banner .about-heading {
    color: #fff;
    text-align: center;
}

.about-banner .about-heading h1 {
    font-size: 5.5rem;
}

.about-banner .about-heading h2 {
    font-size: 3.5rem;
    border-bottom: 2px solid #fff;
}

.about-banner .about-heading p {
    font-size: 1.5rem;
    width: 60%;
    margin: auto;
}

.about-section {
    margin: 40px auto;
}

@media (max-width: 960px) {
    .about-section {
        text-align: center;
    }
}

@media (max-width: 960px) {
    .about-section .about-text {
        width: 100%;
    }

    .about-section .about-text h4 {
        font-size: 1.8rem;
    }
}

@media (max-width: 960px) and (max-width: 960px) {
    .about-section .about-text h4 {
        font-size: 1.5rem;
        margin: 2rem 0;
    }
}

.about-section .about-img {
    width: 100%;
}

@media (max-width: 960px) {
    .about-section .about-img {
        display: none;
    }
}

.section-heading {
    font-weight: 100;
}

.about-info {
    width: 100vw;
}

.about-info h2 {
    text-align: center;
    margin: 30px auto 60px;
    font-size: 2.5rem;
    border-bottom: 2px solid #4cc8f2;
    max-width: 600px;
    padding-bottom: 3rem;
}

@media (max-width: 960px) {
    .about-info h2 {
        font-size: 2rem;
        max-width: 300px;
        margin: 0 auto 0;
    }
}

.about-info h3 {
    color: #4cc7f2;
    font-size: 2.5rem;
}

@media (max-width: 960px) {
    .about-info h3 {
        font-size: 2rem;
    }
}

.about-info p {
    margin-bottom: 30px;
    font-size: 1.5rem;
    font-weight: 100;
}

.image-about {
    background: url("../../../static/images/photo-about-4.jpeg");
    background-position: center;
    height: 65vh;
    position: relative;
}

@media (max-width: 960px) {
    .image-about {
        display: none;
    }
}

@media (max-width: 960px) {
    .image-about p {
        display: none;
    }
}

.image-about p {
    color: #fff;
    position: absolute;
    top: 80%;
    width: 600px;
}

.more-info {
    width: 60vw;
    margin: auto;
    background-color: #f2eeee;
    padding: 30px;
}

@media (max-width: 960px) {
    .more-info {
        width: 90vw;
    }
}

.more-info h2 {
    text-align: center;
    color: #4cc7f2;
    font-size: 2.5rem;
    margin: 80px auto;
}

.more-info h4 {
    text-align: center;
    margin-bottom: 40px;
}

.more-info h5 {
    text-align: center;
    margin-bottom: 40px;
}

.more-info p {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 100;
    margin-bottom: 30px;
}

.final-info {
    margin-top: 80px;
    width: 60vw;
}

@media (max-width: 960px) {
    .final-info {
        width: 90vw;
        text-align: center;
    }
}

.final-info h2 {
    font-size: 2.2rem;
    font-weight: 100;
    text-align: center;
    margin-bottom: 60px;
}

.final-info h3 {
    color: #4cc7f2;
    font-size: 2.5rem;
    text-align: center;
    margin: 50px auto;
}

@media (max-width: 960px) {
    .final-info h3 {
        font-size: 1.5rem;
        text-align: center;
        margin: 0;
        display: none;
    }
}

.final-info p {
    text-align: center;
    font-size: 1.5rem;
    font-weight: 100;
}

.idea {
    margin: 3rem auto;
}

.idea h3 {
    text-align: center;
    margin: 2rem auto;
    color: #4cc7f2;
    font-size: 2.5rem;
}

@media (max-width: 960px) {
    .idea {
        width: 100vw;
        text-align: center;
    }
}

.idea p {
    font-size: 1.5rem;
    font-weight: 100;
}

@media (max-width: 960px) {
    .idea p {
        text-align: center;
        width: 90vw;
        display: none;
    }
}

.idea .image-container {
    max-width: 320px;
    border-radius: 5%;
    float: left;
    margin-right: 20px;
}

@media (max-width: 960px) {
    .idea .image-container {
        display: none;
    }
}

.qoute-sect {
    text-align: center;
}

.qoute-sect .qoute-heading {
    color: #4cc7f2;
    font-size: 1.3rem;
}

.condition-section {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1003%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/rect%3e%3cpath d='M21.4 -20.4L86.35 17.1L86.35 92.1L21.4 129.6L-43.55 92.1L-43.55 17.1zM21.4 204.6L86.35 242.1L86.35 317.1L21.4 354.6L-43.55 317.1L-43.55 242.1zM86.35 542.1L151.31 579.6L151.31 654.6L86.35 692.1L21.4 654.6L21.4 579.6zM216.26 317.1L281.21 354.6L281.21 429.6L216.26 467.1L151.31 429.6L151.31 354.6zM411.12 429.6L476.08 467.1L476.08 542.1L411.12 579.6L346.17 542.1L346.17 467.1zM541.03 -20.4L605.98 17.1L605.98 92.1L541.03 129.6L476.08 92.1L476.08 17.1zM670.94 -20.4L735.89 17.1L735.89 92.1L670.94 129.6L605.98 92.1L605.98 17.1zM800.84 429.6L865.8 467.1L865.8 542.1L800.84 579.6L735.89 542.1L735.89 467.1zM865.8 542.1L930.75 579.6L930.75 654.6L865.8 692.1L800.84 654.6L800.84 579.6zM930.75 429.6L995.71 467.1L995.71 542.1L930.75 579.6L865.8 542.1L865.8 467.1zM1060.66 -20.4L1125.61 17.1L1125.61 92.1L1060.66 129.6L995.71 92.1L995.71 17.1zM1320.48 429.6L1385.43 467.1L1385.43 542.1L1320.48 579.6L1255.52 542.1L1255.52 467.1z' stroke='rgba(123%2c 142%2c 222%2c 1)' stroke-width='2'%3e%3c/path%3e%3cpath d='M13.9 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 204.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0z' fill='rgba(123%2c 142%2c 222%2c 1)'%3e%3c/path%3e%3cpath d='M146.22 211.17L189.52 236.17L189.52 286.17L146.22 311.17L102.92 286.17L102.92 236.17zM189.52 136.17L232.82 161.17L232.82 211.17L189.52 236.17L146.22 211.17L146.22 161.17zM232.82 361.17L276.13 386.17L276.13 436.17L232.82 461.17L189.52 436.17L189.52 386.17zM276.13 136.17L319.43 161.17L319.43 211.17L276.13 236.17L232.82 211.17L232.82 161.17zM449.34 436.17L492.64 461.17L492.64 511.17L449.34 536.17L406.03 511.17L406.03 461.17zM492.64 511.17L535.94 536.17L535.94 586.17L492.64 611.17L449.34 586.17L449.34 536.17zM579.24 511.17L622.54 536.17L622.54 586.17L579.24 611.17L535.94 586.17L535.94 536.17zM665.85 361.17L709.15 386.17L709.15 436.17L665.85 461.17L622.54 436.17L622.54 386.17zM795.75 136.17L839.06 161.17L839.06 211.17L795.75 236.17L752.45 211.17L752.45 161.17zM925.66 61.17L968.97 86.17L968.97 136.17L925.66 161.17L882.36 136.17L882.36 86.17zM968.97 -13.83L1012.27 11.17L1012.27 61.17L968.97 86.17L925.66 61.17L925.66 11.17zM1012.27 211.17L1055.57 236.17L1055.57 286.17L1012.27 311.17L968.97 286.17L968.97 236.17zM968.97 286.17L1012.27 311.17L1012.27 361.17L968.97 386.17L925.66 361.17L925.66 311.17zM968.97 436.17L1012.27 461.17L1012.27 511.17L968.97 536.17L925.66 511.17L925.66 461.17zM1185.48 211.17L1228.78 236.17L1228.78 286.17L1185.48 311.17L1142.17 286.17L1142.17 236.17zM1142.17 436.17L1185.48 461.17L1185.48 511.17L1142.17 536.17L1098.87 511.17L1098.87 461.17zM1315.38 -13.83L1358.69 11.17L1358.69 61.17L1315.38 86.17L1272.08 61.17L1272.08 11.17zM1358.69 61.17L1401.99 86.17L1401.99 136.17L1358.69 161.17L1315.38 136.17L1315.38 86.17zM1315.38 436.17L1358.69 461.17L1358.69 511.17L1315.38 536.17L1272.08 511.17L1272.08 461.17zM1401.99 136.17L1445.29 161.17L1445.29 211.17L1401.99 236.17L1358.69 211.17L1358.69 161.17zM1445.29 211.17L1488.6 236.17L1488.6 286.17L1445.29 311.17L1401.99 286.17L1401.99 236.17z' stroke='rgba(76%2c 200%2c 242%2c 1)' stroke-width='2'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1003'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    min-height: -webkit-fill-available;
}

.condition-section h2 {
    color: #fff;
    font-size: 2.5rem;
    margin: 3rem 0;
}

.condition-section .condition-container {
    width: 80%;
    margin: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
}

.condition-section .condition-container .hexagon {
    position: relative;
    width: 250px;
    height: 250px;
    margin: 0px 5px 80px;
    cursor: pointer;
}

@media (max-width: 760px) {
    .condition-section .condition-container .hexagon {
        width: 150px;
        height: 150px;
    }
}

.condition-section .condition-container .hexagon:hover::before {
    opacity: 0.8;
    transform: scale(0.5);
}

.condition-section .condition-container .hexagon::before {
    content: '';
    position: absolute;
    bottom: -70px;
    width: 100%;
    height: 60px;
    background: radial-gradient(rgba(0, 0, 0, 0.6), transparent);
    border-radius: 50%;
    transition: 0.5s;
}

.condition-section .condition-container .hexagon .shape {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(76, 200, 242, 0.5);
    clip-path: polygon(0 25%, 50% 0, 100% 25%, 100% 75%, 50% 100%, 0 75%);
    transition: all 0.5s;
}

.condition-section .condition-container .hexagon .shape .content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background: linear-gradient(#4cc8f2, rgba(76, 200, 242, 0.6));
    color: #fff;
    text-align: center;
    text-transform: uppercase;
}

.condition-section .condition-container .hexagon .shape .content h3 {
    font-size: 1.3rem;
}

@media (max-width: 760px) {
    .condition-section .condition-container .hexagon .shape .content h3 {
        font-size: 0.9rem;
    }
}

.condition-section .condition-container .hexagon .shape img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.condition-section .condition-container .hexagon .shape:hover {
    transform: translateY(-30px);
}

.products-page {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1003%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/rect%3e%3cpath d='M21.4 -20.4L86.35 17.1L86.35 92.1L21.4 129.6L-43.55 92.1L-43.55 17.1zM21.4 204.6L86.35 242.1L86.35 317.1L21.4 354.6L-43.55 317.1L-43.55 242.1zM86.35 542.1L151.31 579.6L151.31 654.6L86.35 692.1L21.4 654.6L21.4 579.6zM216.26 317.1L281.21 354.6L281.21 429.6L216.26 467.1L151.31 429.6L151.31 354.6zM411.12 429.6L476.08 467.1L476.08 542.1L411.12 579.6L346.17 542.1L346.17 467.1zM541.03 -20.4L605.98 17.1L605.98 92.1L541.03 129.6L476.08 92.1L476.08 17.1zM670.94 -20.4L735.89 17.1L735.89 92.1L670.94 129.6L605.98 92.1L605.98 17.1zM800.84 429.6L865.8 467.1L865.8 542.1L800.84 579.6L735.89 542.1L735.89 467.1zM865.8 542.1L930.75 579.6L930.75 654.6L865.8 692.1L800.84 654.6L800.84 579.6zM930.75 429.6L995.71 467.1L995.71 542.1L930.75 579.6L865.8 542.1L865.8 467.1zM1060.66 -20.4L1125.61 17.1L1125.61 92.1L1060.66 129.6L995.71 92.1L995.71 17.1zM1320.48 429.6L1385.43 467.1L1385.43 542.1L1320.48 579.6L1255.52 542.1L1255.52 467.1z' stroke='rgba(123%2c 142%2c 222%2c 1)' stroke-width='2'%3e%3c/path%3e%3cpath d='M13.9 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 204.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0z' fill='rgba(123%2c 142%2c 222%2c 1)'%3e%3c/path%3e%3cpath d='M146.22 211.17L189.52 236.17L189.52 286.17L146.22 311.17L102.92 286.17L102.92 236.17zM189.52 136.17L232.82 161.17L232.82 211.17L189.52 236.17L146.22 211.17L146.22 161.17zM232.82 361.17L276.13 386.17L276.13 436.17L232.82 461.17L189.52 436.17L189.52 386.17zM276.13 136.17L319.43 161.17L319.43 211.17L276.13 236.17L232.82 211.17L232.82 161.17zM449.34 436.17L492.64 461.17L492.64 511.17L449.34 536.17L406.03 511.17L406.03 461.17zM492.64 511.17L535.94 536.17L535.94 586.17L492.64 611.17L449.34 586.17L449.34 536.17zM579.24 511.17L622.54 536.17L622.54 586.17L579.24 611.17L535.94 586.17L535.94 536.17zM665.85 361.17L709.15 386.17L709.15 436.17L665.85 461.17L622.54 436.17L622.54 386.17zM795.75 136.17L839.06 161.17L839.06 211.17L795.75 236.17L752.45 211.17L752.45 161.17zM925.66 61.17L968.97 86.17L968.97 136.17L925.66 161.17L882.36 136.17L882.36 86.17zM968.97 -13.83L1012.27 11.17L1012.27 61.17L968.97 86.17L925.66 61.17L925.66 11.17zM1012.27 211.17L1055.57 236.17L1055.57 286.17L1012.27 311.17L968.97 286.17L968.97 236.17zM968.97 286.17L1012.27 311.17L1012.27 361.17L968.97 386.17L925.66 361.17L925.66 311.17zM968.97 436.17L1012.27 461.17L1012.27 511.17L968.97 536.17L925.66 511.17L925.66 461.17zM1185.48 211.17L1228.78 236.17L1228.78 286.17L1185.48 311.17L1142.17 286.17L1142.17 236.17zM1142.17 436.17L1185.48 461.17L1185.48 511.17L1142.17 536.17L1098.87 511.17L1098.87 461.17zM1315.38 -13.83L1358.69 11.17L1358.69 61.17L1315.38 86.17L1272.08 61.17L1272.08 11.17zM1358.69 61.17L1401.99 86.17L1401.99 136.17L1358.69 161.17L1315.38 136.17L1315.38 86.17zM1315.38 436.17L1358.69 461.17L1358.69 511.17L1315.38 536.17L1272.08 511.17L1272.08 461.17zM1401.99 136.17L1445.29 161.17L1445.29 211.17L1401.99 236.17L1358.69 211.17L1358.69 161.17zM1445.29 211.17L1488.6 236.17L1488.6 286.17L1445.29 311.17L1401.99 286.17L1401.99 236.17z' stroke='rgba(76%2c 200%2c 242%2c 1)' stroke-width='2'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1003'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-size: cover;
    background-position: top;
    min-height: 100vh;
}

.products-page .product-container {
    height: 600px;
    margin: 40px 20px;
    cursor: pointer;
    -webkit-box-shadow: -2px 13px 48px 0px #857c85;
    -moz-box-shadow: -2px 13px 48px 0px #857c85;
    box-shadow: -2px 13px 48px 0px #857c85;
}

@media screen and (max-width: 480px) {
    .products-page .product-container {
        margin: 20px 0;
        width: 100%;
        padding: 0;
    }
}

.products-page .product-container img {
    width: 150px;
    padding: 20px;
}

.products-page .product-container a {
    display: flex;
    justify-content: center;
    padding: 10px;
}

.products-page .product-container .card-body {
    font-weight: bold;
    border-bottom: 1px solid #4cc8f2;
    border-top: 1px solid #4cc8f2;
    text-transform: uppercase;
    display: flex;
    align-items: center;
    background-color: #7b8ede;
    color: #fff;
    padding: 0 16px;
}

.products-page .product-container .card-footer {
    font-weight: bold;
    margin-top: 10px;
}

.products-page .product-container .card-footer p {
    font-weight: bold;
}

.products-page .product-container .card-buy {
    width: 100%;
    background-color: #7b8ede;
    height: 50px;
    position: relative;
}

.products-page .product-container .card-buy a {
    color: #fff;
    position: absolute;
    left: 0;
    top: 5px;
    text-transform: uppercase;
    font-weight: bold;
}

.products-page .product-container .card-buy .shop {
    color: #fff;
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 1.3rem;
}

.products-page .card-img-top {
    height: 300px;
    width: 200px;
}

.detail-section {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1003%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/rect%3e%3cpath d='M21.4 -20.4L86.35 17.1L86.35 92.1L21.4 129.6L-43.55 92.1L-43.55 17.1zM21.4 204.6L86.35 242.1L86.35 317.1L21.4 354.6L-43.55 317.1L-43.55 242.1zM86.35 542.1L151.31 579.6L151.31 654.6L86.35 692.1L21.4 654.6L21.4 579.6zM216.26 317.1L281.21 354.6L281.21 429.6L216.26 467.1L151.31 429.6L151.31 354.6zM411.12 429.6L476.08 467.1L476.08 542.1L411.12 579.6L346.17 542.1L346.17 467.1zM541.03 -20.4L605.98 17.1L605.98 92.1L541.03 129.6L476.08 92.1L476.08 17.1zM670.94 -20.4L735.89 17.1L735.89 92.1L670.94 129.6L605.98 92.1L605.98 17.1zM800.84 429.6L865.8 467.1L865.8 542.1L800.84 579.6L735.89 542.1L735.89 467.1zM865.8 542.1L930.75 579.6L930.75 654.6L865.8 692.1L800.84 654.6L800.84 579.6zM930.75 429.6L995.71 467.1L995.71 542.1L930.75 579.6L865.8 542.1L865.8 467.1zM1060.66 -20.4L1125.61 17.1L1125.61 92.1L1060.66 129.6L995.71 92.1L995.71 17.1zM1320.48 429.6L1385.43 467.1L1385.43 542.1L1320.48 579.6L1255.52 542.1L1255.52 467.1z' stroke='rgba(123%2c 142%2c 222%2c 1)' stroke-width='2'%3e%3c/path%3e%3cpath d='M13.9 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 204.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0z' fill='rgba(123%2c 142%2c 222%2c 1)'%3e%3c/path%3e%3cpath d='M146.22 211.17L189.52 236.17L189.52 286.17L146.22 311.17L102.92 286.17L102.92 236.17zM189.52 136.17L232.82 161.17L232.82 211.17L189.52 236.17L146.22 211.17L146.22 161.17zM232.82 361.17L276.13 386.17L276.13 436.17L232.82 461.17L189.52 436.17L189.52 386.17zM276.13 136.17L319.43 161.17L319.43 211.17L276.13 236.17L232.82 211.17L232.82 161.17zM449.34 436.17L492.64 461.17L492.64 511.17L449.34 536.17L406.03 511.17L406.03 461.17zM492.64 511.17L535.94 536.17L535.94 586.17L492.64 611.17L449.34 586.17L449.34 536.17zM579.24 511.17L622.54 536.17L622.54 586.17L579.24 611.17L535.94 586.17L535.94 536.17zM665.85 361.17L709.15 386.17L709.15 436.17L665.85 461.17L622.54 436.17L622.54 386.17zM795.75 136.17L839.06 161.17L839.06 211.17L795.75 236.17L752.45 211.17L752.45 161.17zM925.66 61.17L968.97 86.17L968.97 136.17L925.66 161.17L882.36 136.17L882.36 86.17zM968.97 -13.83L1012.27 11.17L1012.27 61.17L968.97 86.17L925.66 61.17L925.66 11.17zM1012.27 211.17L1055.57 236.17L1055.57 286.17L1012.27 311.17L968.97 286.17L968.97 236.17zM968.97 286.17L1012.27 311.17L1012.27 361.17L968.97 386.17L925.66 361.17L925.66 311.17zM968.97 436.17L1012.27 461.17L1012.27 511.17L968.97 536.17L925.66 511.17L925.66 461.17zM1185.48 211.17L1228.78 236.17L1228.78 286.17L1185.48 311.17L1142.17 286.17L1142.17 236.17zM1142.17 436.17L1185.48 461.17L1185.48 511.17L1142.17 536.17L1098.87 511.17L1098.87 461.17zM1315.38 -13.83L1358.69 11.17L1358.69 61.17L1315.38 86.17L1272.08 61.17L1272.08 11.17zM1358.69 61.17L1401.99 86.17L1401.99 136.17L1358.69 161.17L1315.38 136.17L1315.38 86.17zM1315.38 436.17L1358.69 461.17L1358.69 511.17L1315.38 536.17L1272.08 511.17L1272.08 461.17zM1401.99 136.17L1445.29 161.17L1445.29 211.17L1401.99 236.17L1358.69 211.17L1358.69 161.17zM1445.29 211.17L1488.6 236.17L1488.6 286.17L1445.29 311.17L1401.99 286.17L1401.99 236.17z' stroke='rgba(76%2c 200%2c 242%2c 1)' stroke-width='2'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1003'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");
    background-size: cover;
    background-repeat: no-repeat;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
}

.detail-section .detail-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 200px auto;
    width: 50%;
    background-color: #fff;
}

@media screen and (max-width: 960px) {
    .detail-section .detail-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }
}

.detail-section .image-container {
    display: flex;
    justify-content: center;
    width: 50%;
    height: 100%;
    margin-bottom: 60px;
}

@media screen and (max-width: 960px) {
    .detail-section .image-container {
        width: 100%;
        margin-bottom: 40px;
    }
}

@media screen and (max-width: 480px) {
    .detail-section .image-container img {
        height: 280px;
    }
}

.detail-section .product-details-container {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
}

@media screen and (max-width: 760px) {
    .detail-section .product-details-container {
        width: 90%;
    }
}

.detail-section .product-details-container .cut-off {
    max-height: 8.4em;
    line-height: 1.4;
    overflow: hidden;
    position: relative;
    transition: all 0.3s;
}

.detail-section .product-details-container .cut-off:has(+ .expand-btn:not(:checked))::before {
    content: '';
    position: absolute;
    height: 1.4em;
    width: 100%;
    bottom: 0;
    pointer-events: none;
    background: linear-gradient(to bottom, transparent, #fff);
}

.detail-section .product-details-container .expand-btn {
    appearance: none;
    border: 1px solid #4cc8f2;
    width: 5rem;
    padding: 0.5em;
    cursor: pointer;
}

.detail-section .product-details-container .expand-btn:hover {
    background-color: #7b8ede;
}

.detail-section .product-details-container .expand-btn::before {
    content: 'Expand';
}

.detail-section .product-details-container .expand-btn:checked::before {
    content: 'Collapse';
}

.detail-section .product-details-container .cut-off:has(+ .expand-btn:checked) {
    max-height: none;
}

.detail-section small {
    margin: 10px 0;
}

.detail-section p {
    text-transform: uppercase;
    font-weight: bold;
}

@media (max-width: 480px) {
    .detail-section p {
        font-size: 0.8rem;
    }
}

.detail-section .form-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.detail-section .form-group p {
    margin: 0;
}

.detail-section .form-group .form-control {
    width: 60px;
}

@media screen and (max-width: 480px) {
    .detail-section .form-group {
        margin-bottom: 10px;
    }
}

.detail-section .form-check {
    margin: 0;
    padding: 20px 0;
    width: 100%;
}

@media screen and (max-width: 480px) {
    .detail-section .form-check {
        padding: 0;
    }
}

.detail-section .form-check .btn-outline-black {
    background-color: #dc934e;
    color: #fff;
    width: 100%;
}

.detail-section .form-check .btn-black {
    background-color: #096b1c;
    color: #fff;
    width: 100%;
    margin-bottom: 10px;
}

.detail-section .more-btn {
    background-color: #4cc8f2;
    color: #fff;
    text-align: center;
}

@media (max-width: 960px) {
    .detail-section .more-btn {
        margin-top: 20px;
    }
}

.detail-section .more-btn span {
    color: #fff;
}

.question-container {
    min-width: 65%;
}

.question-container .question-label {
    font-size: 2rem;
}

@media screen and (max-width: 480px) {
    .question-container .question-label {
        font-size: 1.3rem;
    }
}

.question-container label {
    font-size: 1.5rem;
    margin: 5px 0;
}

@media screen and (max-width: 480px) {
    .question-container label {
        font-size: 1.1rem;
    }
}

.question-container input[type='radio'] {
    height: 25px;
    width: 25px;
    border: none;
    transform: translateY(5px);
}

.question-container input[type='checkbox'] {
    height: 25px;
    width: 25px;
    border: none;
}

.question-container .question-btn-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.content {
    background: linear-gradient(#7b8ede, #b452c6);
}


/*.container-background {*/
/*    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1003%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(255%2c 255%2c 255%2c 1)'%3e%3c/rect%3e%3cpath d='M21.4 -20.4L86.35 17.1L86.35 92.1L21.4 129.6L-43.55 92.1L-43.55 17.1zM21.4 204.6L86.35 242.1L86.35 317.1L21.4 354.6L-43.55 317.1L-43.55 242.1zM86.35 542.1L151.31 579.6L151.31 654.6L86.35 692.1L21.4 654.6L21.4 579.6zM216.26 317.1L281.21 354.6L281.21 429.6L216.26 467.1L151.31 429.6L151.31 354.6zM411.12 429.6L476.08 467.1L476.08 542.1L411.12 579.6L346.17 542.1L346.17 467.1zM541.03 -20.4L605.98 17.1L605.98 92.1L541.03 129.6L476.08 92.1L476.08 17.1zM670.94 -20.4L735.89 17.1L735.89 92.1L670.94 129.6L605.98 92.1L605.98 17.1zM800.84 429.6L865.8 467.1L865.8 542.1L800.84 579.6L735.89 542.1L735.89 467.1zM865.8 542.1L930.75 579.6L930.75 654.6L865.8 692.1L800.84 654.6L800.84 579.6zM930.75 429.6L995.71 467.1L995.71 542.1L930.75 579.6L865.8 542.1L865.8 467.1zM1060.66 -20.4L1125.61 17.1L1125.61 92.1L1060.66 129.6L995.71 92.1L995.71 17.1zM1320.48 429.6L1385.43 467.1L1385.43 542.1L1320.48 579.6L1255.52 542.1L1255.52 467.1z' stroke='rgba(123%2c 142%2c 222%2c 1)' stroke-width='2'%3e%3c/path%3e%3cpath d='M13.9 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 204.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0z' fill='rgba(123%2c 142%2c 222%2c 1)'%3e%3c/path%3e%3cpath d='M146.22 211.17L189.52 236.17L189.52 286.17L146.22 311.17L102.92 286.17L102.92 236.17zM189.52 136.17L232.82 161.17L232.82 211.17L189.52 236.17L146.22 211.17L146.22 161.17zM232.82 361.17L276.13 386.17L276.13 436.17L232.82 461.17L189.52 436.17L189.52 386.17zM276.13 136.17L319.43 161.17L319.43 211.17L276.13 236.17L232.82 211.17L232.82 161.17zM449.34 436.17L492.64 461.17L492.64 511.17L449.34 536.17L406.03 511.17L406.03 461.17zM492.64 511.17L535.94 536.17L535.94 586.17L492.64 611.17L449.34 586.17L449.34 536.17zM579.24 511.17L622.54 536.17L622.54 586.17L579.24 611.17L535.94 586.17L535.94 536.17zM665.85 361.17L709.15 386.17L709.15 436.17L665.85 461.17L622.54 436.17L622.54 386.17zM795.75 136.17L839.06 161.17L839.06 211.17L795.75 236.17L752.45 211.17L752.45 161.17zM925.66 61.17L968.97 86.17L968.97 136.17L925.66 161.17L882.36 136.17L882.36 86.17zM968.97 -13.83L1012.27 11.17L1012.27 61.17L968.97 86.17L925.66 61.17L925.66 11.17zM1012.27 211.17L1055.57 236.17L1055.57 286.17L1012.27 311.17L968.97 286.17L968.97 236.17zM968.97 286.17L1012.27 311.17L1012.27 361.17L968.97 386.17L925.66 361.17L925.66 311.17zM968.97 436.17L1012.27 461.17L1012.27 511.17L968.97 536.17L925.66 511.17L925.66 461.17zM1185.48 211.17L1228.78 236.17L1228.78 286.17L1185.48 311.17L1142.17 286.17L1142.17 236.17zM1142.17 436.17L1185.48 461.17L1185.48 511.17L1142.17 536.17L1098.87 511.17L1098.87 461.17zM1315.38 -13.83L1358.69 11.17L1358.69 61.17L1315.38 86.17L1272.08 61.17L1272.08 11.17zM1358.69 61.17L1401.99 86.17L1401.99 136.17L1358.69 161.17L1315.38 136.17L1315.38 86.17zM1315.38 436.17L1358.69 461.17L1358.69 511.17L1315.38 536.17L1272.08 511.17L1272.08 461.17zM1401.99 136.17L1445.29 161.17L1445.29 211.17L1401.99 236.17L1358.69 211.17L1358.69 161.17zM1445.29 211.17L1488.6 236.17L1488.6 286.17L1445.29 311.17L1401.99 286.17L1401.99 236.17z' stroke='rgba(76%2c 200%2c 242%2c 1)' stroke-width='2'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1003'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");*/
/*    background-size: cover;*/
/*    background-repeat: no-repeat;*/
/*    width: 100vw;*/
/*    min-height: 100vh;*/
/*}*/


/*.report-section {*/
/*    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.com/svgjs' width='1440' height='560' preserveAspectRatio='none' viewBox='0 0 1440 560'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1002%26quot%3b)' fill='none'%3e%3crect width='1440' height='560' x='0' y='0' fill='rgba(231%2c 235%2c 237%2c 1)'%3e%3c/rect%3e%3cpath d='M21.4 -20.4L86.35 17.1L86.35 92.1L21.4 129.6L-43.55 92.1L-43.55 17.1zM21.4 204.6L86.35 242.1L86.35 317.1L21.4 354.6L-43.55 317.1L-43.55 242.1zM86.35 542.1L151.31 579.6L151.31 654.6L86.35 692.1L21.4 654.6L21.4 579.6zM216.26 317.1L281.21 354.6L281.21 429.6L216.26 467.1L151.31 429.6L151.31 354.6zM411.12 429.6L476.08 467.1L476.08 542.1L411.12 579.6L346.17 542.1L346.17 467.1zM541.03 -20.4L605.98 17.1L605.98 92.1L541.03 129.6L476.08 92.1L476.08 17.1zM670.94 -20.4L735.89 17.1L735.89 92.1L670.94 129.6L605.98 92.1L605.98 17.1zM800.84 429.6L865.8 467.1L865.8 542.1L800.84 579.6L735.89 542.1L735.89 467.1zM865.8 542.1L930.75 579.6L930.75 654.6L865.8 692.1L800.84 654.6L800.84 579.6zM930.75 429.6L995.71 467.1L995.71 542.1L930.75 579.6L865.8 542.1L865.8 467.1zM1060.66 -20.4L1125.61 17.1L1125.61 92.1L1060.66 129.6L995.71 92.1L995.71 17.1zM1320.48 429.6L1385.43 467.1L1385.43 542.1L1320.48 579.6L1255.52 542.1L1255.52 467.1z' stroke='rgba(123%2c 142%2c 222%2c 1)' stroke-width='2'%3e%3c/path%3e%3cpath d='M13.9 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 204.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM-51.05 242.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM78.85 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM13.9 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 317.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM273.71 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM208.76 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM143.81 354.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM403.62 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM338.67 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM598.48 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM533.53 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM468.58 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM663.44 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM728.39 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM858.3 692.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM793.34 654.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM923.25 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 -20.4 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1118.11 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1053.16 129.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 92.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM988.21 17.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 429.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1377.93 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1312.98 579.6 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 542.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0zM1248.02 467.1 a7.5 7.5 0 1 0 15 0 a7.5 7.5 0 1 0 -15 0z' fill='rgba(123%2c 142%2c 222%2c 1)'%3e%3c/path%3e%3cpath d='M146.22 211.17L189.52 236.17L189.52 286.17L146.22 311.17L102.92 286.17L102.92 236.17zM189.52 136.17L232.82 161.17L232.82 211.17L189.52 236.17L146.22 211.17L146.22 161.17zM232.82 361.17L276.13 386.17L276.13 436.17L232.82 461.17L189.52 436.17L189.52 386.17zM276.13 136.17L319.43 161.17L319.43 211.17L276.13 236.17L232.82 211.17L232.82 161.17zM449.34 436.17L492.64 461.17L492.64 511.17L449.34 536.17L406.03 511.17L406.03 461.17zM492.64 511.17L535.94 536.17L535.94 586.17L492.64 611.17L449.34 586.17L449.34 536.17zM579.24 511.17L622.54 536.17L622.54 586.17L579.24 611.17L535.94 586.17L535.94 536.17zM665.85 361.17L709.15 386.17L709.15 436.17L665.85 461.17L622.54 436.17L622.54 386.17zM795.75 136.17L839.06 161.17L839.06 211.17L795.75 236.17L752.45 211.17L752.45 161.17zM925.66 61.17L968.97 86.17L968.97 136.17L925.66 161.17L882.36 136.17L882.36 86.17zM968.97 -13.83L1012.27 11.17L1012.27 61.17L968.97 86.17L925.66 61.17L925.66 11.17zM1012.27 211.17L1055.57 236.17L1055.57 286.17L1012.27 311.17L968.97 286.17L968.97 236.17zM968.97 286.17L1012.27 311.17L1012.27 361.17L968.97 386.17L925.66 361.17L925.66 311.17zM968.97 436.17L1012.27 461.17L1012.27 511.17L968.97 536.17L925.66 511.17L925.66 461.17zM1185.48 211.17L1228.78 236.17L1228.78 286.17L1185.48 311.17L1142.17 286.17L1142.17 236.17zM1142.17 436.17L1185.48 461.17L1185.48 511.17L1142.17 536.17L1098.87 511.17L1098.87 461.17zM1315.38 -13.83L1358.69 11.17L1358.69 61.17L1315.38 86.17L1272.08 61.17L1272.08 11.17zM1358.69 61.17L1401.99 86.17L1401.99 136.17L1358.69 161.17L1315.38 136.17L1315.38 86.17zM1315.38 436.17L1358.69 461.17L1358.69 511.17L1315.38 536.17L1272.08 511.17L1272.08 461.17zM1401.99 136.17L1445.29 161.17L1445.29 211.17L1401.99 236.17L1358.69 211.17L1358.69 161.17zM1445.29 211.17L1488.6 236.17L1488.6 286.17L1445.29 311.17L1401.99 286.17L1401.99 236.17z' stroke='rgba(76%2c 200%2c 242%2c 1)' stroke-width='2'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1002'%3e%3crect width='1440' height='560' fill='white'%3e%3c/rect%3e%3c/mask%3e%3c/defs%3e%3c/svg%3e");*/
/*    display: flex;*/
/*    flex-direction: column;*/
/*    justify-content: center;*/
/*    align-items: center;*/
/*    background-repeat: no-repeat;*/
/*    background-size: cover;*/
/*    min-height: 100vh;*/
/*    overflow: auto;*/
/*}*/