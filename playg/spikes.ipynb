{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["df_raw = pd.read_csv(\"contextual_report_raw.csv\", index_col=False)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>18 - 24</th>\n", "      <th>25 - 34</th>\n", "      <th>35 - 40+</th>\n", "      <th>In my 50s</th>\n", "      <th>Over 60</th>\n", "      <th>Anaemia</th>\n", "      <th>Depression</th>\n", "      <th>Diabetes</th>\n", "      <th><PERSON><PERSON><PERSON> Imbalance</th>\n", "      <th>Polycystic Ovarian Syndrome</th>\n", "      <th>...</th>\n", "      <th>My scalp gets dry not oily</th>\n", "      <th>Within hours</th>\n", "      <th>1 - 2 Days.1</th>\n", "      <th>3 - 5 Days</th>\n", "      <th>My scalp gets oily not dry</th>\n", "      <th>Yes.4</th>\n", "      <th>No.4</th>\n", "      <th>Barley any Scalp</th>\n", "      <th>A little bit of scalp</th>\n", "      <th>Fair amount of scalp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>In Your 20's - Who doesn't experiment with the...</td>\n", "      <td>In Your 30s - Hair can change completely every...</td>\n", "      <td>In Your 40s - As the body goes through numerou...</td>\n", "      <td>Your hair at this age: The average age of meno...</td>\n", "      <td>Your hair at this age: Sebum (oil) secretion t...</td>\n", "      <td>Why does iron deficiency cause hair loss?It ca...</td>\n", "      <td>Many people do not realise that depression can...</td>\n", "      <td>Diabetes can cause a range of symptoms and hea...</td>\n", "      <td>Hormone levels affect both the quality and qua...</td>\n", "      <td>PCOS is caused by an excess of male hormones, ...</td>\n", "      <td>...</td>\n", "      <td>Your Rating – Dry not oily</td>\n", "      <td>Your Rating –Very Dry</td>\n", "      <td>Your Rating –Dry</td>\n", "      <td>Your Rating – Medium Dry</td>\n", "      <td>Your Rating –Oily not dry</td>\n", "      <td>When it comes to the effects of iron deficienc...</td>\n", "      <td>None</td>\n", "      <td>High</td>\n", "      <td>Medium</td>\n", "      <td>Low</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>What does it look like?If you’re experiencing ...</td>\n", "      <td>The physiological states of depression such as...</td>\n", "      <td>Diabetes can cause hair thinning and hair loss...</td>\n", "      <td>Common causes of hormone imbalances that cause...</td>\n", "      <td>For women with polycystic ovary syndrome (PCOS...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Iron deficiency makes it harder for the follic...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Why is Iron Important for Hair?Iron is a miner...</td>\n", "      <td>Side effect of depression can sometimes be hai...</td>\n", "      <td>Damage to blood vessels through diabetes can r...</td>\n", "      <td>Hormone levels affect both the quality and qua...</td>\n", "      <td>What causes hirsutism? Hirsutism is mainly cau...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>An iron deficiency can also affect hair by mak...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>Iron deficiency anaemia is a condition in whic...</td>\n", "      <td>The body can react negatively to the symptoms ...</td>\n", "      <td>Hormones and stress - Diabetes can put a great...</td>\n", "      <td>Common causes of hormone imbalances that cause...</td>\n", "      <td>Women can produce high levels of androgens bec...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>A lack of iron causes hair to shed from the cu...</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>4 rows × 118 columns</p>\n", "</div>"], "text/plain": ["                                             18 - 24  \\\n", "0  In Your 20's - Who doesn't experiment with the...   \n", "1                                                NaN   \n", "2                                                NaN   \n", "3                                                NaN   \n", "\n", "                                             25 - 34  \\\n", "0  In Your 30s - Hair can change completely every...   \n", "1                                                NaN   \n", "2                                                NaN   \n", "3                                                NaN   \n", "\n", "                                            35 - 40+  \\\n", "0  In Your 40s - As the body goes through numerou...   \n", "1                                                NaN   \n", "2                                                NaN   \n", "3                                                NaN   \n", "\n", "                                           In my 50s  \\\n", "0  Your hair at this age: The average age of meno...   \n", "1                                                NaN   \n", "2                                                NaN   \n", "3                                                NaN   \n", "\n", "                                             Over 60  \\\n", "0  Your hair at this age: Sebum (oil) secretion t...   \n", "1                                                NaN   \n", "2                                                NaN   \n", "3                                                NaN   \n", "\n", "                                             Anaemia  \\\n", "0  Why does iron deficiency cause hair loss?It ca...   \n", "1  What does it look like?If you’re experiencing ...   \n", "2  Why is Iron Important for Hair?Iron is a miner...   \n", "3  Iron deficiency anaemia is a condition in whic...   \n", "\n", "                                          Depression  \\\n", "0  Many people do not realise that depression can...   \n", "1  The physiological states of depression such as...   \n", "2  Side effect of depression can sometimes be hai...   \n", "3  The body can react negatively to the symptoms ...   \n", "\n", "                                            Diabetes  \\\n", "0  Diabetes can cause a range of symptoms and hea...   \n", "1  Diabetes can cause hair thinning and hair loss...   \n", "2  Damage to blood vessels through diabetes can r...   \n", "3  Hormones and stress - Diabetes can put a great...   \n", "\n", "                                   <PERSON><PERSON><PERSON> Imbalance  \\\n", "0  Hormone levels affect both the quality and qua...   \n", "1  Common causes of hormone imbalances that cause...   \n", "2  Hormone levels affect both the quality and qua...   \n", "3  Common causes of hormone imbalances that cause...   \n", "\n", "                         Polycystic Ovarian Syndrome  ...  \\\n", "0  PCOS is caused by an excess of male hormones, ...  ...   \n", "1  For women with polycystic ovary syndrome (PCOS...  ...   \n", "2  What causes hirsutism? Hirsutism is mainly cau...  ...   \n", "3  Women can produce high levels of androgens bec...  ...   \n", "\n", "    My scalp gets dry not oily           Within hours      1 - 2 Days.1  \\\n", "0  Your Rating – Dry not oily   Your Rating –Very Dry  Your Rating –Dry   \n", "1                          NaN                    NaN               NaN   \n", "2                          NaN                    NaN               NaN   \n", "3                          NaN                    NaN               NaN   \n", "\n", "                 3 - 5 Days My scalp gets oily not dry  \\\n", "0  Your Rating – Medium Dry  Your Rating –Oily not dry   \n", "1                       NaN                        NaN   \n", "2                       NaN                        NaN   \n", "3                       NaN                        NaN   \n", "\n", "                                               Yes.4  No.4 <PERSON><PERSON> any Scal<PERSON>  \\\n", "0  When it comes to the effects of iron deficienc...  None             High   \n", "1  Iron deficiency makes it harder for the follic...  None              NaN   \n", "2  An iron deficiency can also affect hair by mak...  None              NaN   \n", "3  A lack of iron causes hair to shed from the cu...  None              NaN   \n", "\n", "  A little bit of scalp Fair amount of scalp  \n", "0               Medium                   Low  \n", "1                   NaN                  NaN  \n", "2                   NaN                  NaN  \n", "3                   NaN                  NaN  \n", "\n", "[4 rows x 118 columns]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["df_raw.head(7)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["# Step 2: Transpose the DataFrame\n", "df_transposed = df_raw.T\n", "\n", "new_header = [\"option1\", \"option2\", \"option3\", \"option4\"]\n", "df_transposed.columns = new_header"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>option1</th>\n", "      <th>option2</th>\n", "      <th>option3</th>\n", "      <th>option4</th>\n", "    </tr>\n", "    <tr>\n", "      <th>categories</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18 - 24</th>\n", "      <td>In Your 20's - Who doesn't experiment with the...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25 - 34</th>\n", "      <td>In Your 30s - Hair can change completely every...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35 - 40+</th>\n", "      <td>In Your 40s - As the body goes through numerou...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>In my 50s</th>\n", "      <td>Your hair at this age: The average age of meno...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Over 60</th>\n", "      <td>Your hair at this age: Sebum (oil) secretion t...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Anaemia</th>\n", "      <td>Why does iron deficiency cause hair loss?It ca...</td>\n", "      <td>What does it look like?If you’re experiencing ...</td>\n", "      <td>Why is Iron Important for Hair?Iron is a miner...</td>\n", "      <td>Iron deficiency anaemia is a condition in whic...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Depression</th>\n", "      <td>Many people do not realise that depression can...</td>\n", "      <td>The physiological states of depression such as...</td>\n", "      <td>Side effect of depression can sometimes be hai...</td>\n", "      <td>The body can react negatively to the symptoms ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Diabetes</th>\n", "      <td>Diabetes can cause a range of symptoms and hea...</td>\n", "      <td>Diabetes can cause hair thinning and hair loss...</td>\n", "      <td>Damage to blood vessels through diabetes can r...</td>\n", "      <td>Hormones and stress - Diabetes can put a great...</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON> Imbalance</th>\n", "      <td>Hormone levels affect both the quality and qua...</td>\n", "      <td>Common causes of hormone imbalances that cause...</td>\n", "      <td>Hormone levels affect both the quality and qua...</td>\n", "      <td>Common causes of hormone imbalances that cause...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                             option1  \\\n", "categories                                                             \n", "18 - 24            In Your 20's - Who doesn't experiment with the...   \n", "25 - 34            In Your 30s - Hair can change completely every...   \n", "35 - 40+           In Your 40s - As the body goes through numerou...   \n", "In my 50s          Your hair at this age: The average age of meno...   \n", "Over 60            Your hair at this age: Sebum (oil) secretion t...   \n", "Anaemia            Why does iron deficiency cause hair loss?It ca...   \n", "Depression         Many people do not realise that depression can...   \n", "Diabetes           Diabetes can cause a range of symptoms and hea...   \n", "Hormone Imbalance  Hormone levels affect both the quality and qua...   \n", "\n", "                                                             option2  \\\n", "categories                                                             \n", "18 - 24                                                          NaN   \n", "25 - 34                                                          NaN   \n", "35 - 40+                                                         NaN   \n", "In my 50s                                                        NaN   \n", "Over 60                                                          NaN   \n", "Anaemia            What does it look like?If you’re experiencing ...   \n", "Depression         The physiological states of depression such as...   \n", "Diabetes           Diabetes can cause hair thinning and hair loss...   \n", "Hormone Imbalance  Common causes of hormone imbalances that cause...   \n", "\n", "                                                             option3  \\\n", "categories                                                             \n", "18 - 24                                                          NaN   \n", "25 - 34                                                          NaN   \n", "35 - 40+                                                         NaN   \n", "In my 50s                                                        NaN   \n", "Over 60                                                          NaN   \n", "Anaemia            Why is Iron Important for Hair?Iron is a miner...   \n", "Depression         Side effect of depression can sometimes be hai...   \n", "Diabetes           Damage to blood vessels through diabetes can r...   \n", "Hormone Imbalance  Hormone levels affect both the quality and qua...   \n", "\n", "                                                             option4  \n", "categories                                                            \n", "18 - 24                                                          NaN  \n", "25 - 34                                                          NaN  \n", "35 - 40+                                                         NaN  \n", "In my 50s                                                        NaN  \n", "Over 60                                                          NaN  \n", "Anaemia            Iron deficiency anaemia is a condition in whic...  \n", "Depression         The body can react negatively to the symptoms ...  \n", "Diabetes           Hormones and stress - Diabetes can put a great...  \n", "Hormone Imbalance  Common causes of hormone imbalances that cause...  "]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df_transposed.head(9)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["df_transposed.index.rename(\"categories\", inplace=True)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# df_transposed.rename(columns={'index':'Cats'})\n", "# df_transposed.rename(columns={ df_transposed.columns[1]: \"category_name\" }, inplace=True)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>option1</th>\n", "      <th>option2</th>\n", "      <th>option3</th>\n", "      <th>option4</th>\n", "    </tr>\n", "    <tr>\n", "      <th>categories</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18 - 24</th>\n", "      <td>In Your 20's - Who doesn't experiment with the...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25 - 34</th>\n", "      <td>In Your 30s - Hair can change completely every...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35 - 40+</th>\n", "      <td>In Your 40s - As the body goes through numerou...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>In my 50s</th>\n", "      <td>Your hair at this age: The average age of meno...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Over 60</th>\n", "      <td>Your hair at this age: Sebum (oil) secretion t...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Anaemia</th>\n", "      <td>Why does iron deficiency cause hair loss?It ca...</td>\n", "      <td>What does it look like?If you’re experiencing ...</td>\n", "      <td>Why is Iron Important for Hair?Iron is a miner...</td>\n", "      <td>Iron deficiency anaemia is a condition in whic...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Depression</th>\n", "      <td>Many people do not realise that depression can...</td>\n", "      <td>The physiological states of depression such as...</td>\n", "      <td>Side effect of depression can sometimes be hai...</td>\n", "      <td>The body can react negatively to the symptoms ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Diabetes</th>\n", "      <td>Diabetes can cause a range of symptoms and hea...</td>\n", "      <td>Diabetes can cause hair thinning and hair loss...</td>\n", "      <td>Damage to blood vessels through diabetes can r...</td>\n", "      <td>Hormones and stress - Diabetes can put a great...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                      option1  \\\n", "categories                                                      \n", "18 - 24     In Your 20's - Who doesn't experiment with the...   \n", "25 - 34     In Your 30s - Hair can change completely every...   \n", "35 - 40+    In Your 40s - As the body goes through numerou...   \n", "In my 50s   Your hair at this age: The average age of meno...   \n", "Over 60     Your hair at this age: Sebum (oil) secretion t...   \n", "Anaemia     Why does iron deficiency cause hair loss?It ca...   \n", "Depression  Many people do not realise that depression can...   \n", "Diabetes    Diabetes can cause a range of symptoms and hea...   \n", "\n", "                                                      option2  \\\n", "categories                                                      \n", "18 - 24                                                   NaN   \n", "25 - 34                                                   NaN   \n", "35 - 40+                                                  NaN   \n", "In my 50s                                                 NaN   \n", "Over 60                                                   NaN   \n", "Anaemia     What does it look like?If you’re experiencing ...   \n", "Depression  The physiological states of depression such as...   \n", "Diabetes    Diabetes can cause hair thinning and hair loss...   \n", "\n", "                                                      option3  \\\n", "categories                                                      \n", "18 - 24                                                   NaN   \n", "25 - 34                                                   NaN   \n", "35 - 40+                                                  NaN   \n", "In my 50s                                                 NaN   \n", "Over 60                                                   NaN   \n", "Anaemia     Why is Iron Important for Hair?Iron is a miner...   \n", "Depression  Side effect of depression can sometimes be hai...   \n", "Diabetes    Damage to blood vessels through diabetes can r...   \n", "\n", "                                                      option4  \n", "categories                                                     \n", "18 - 24                                                   NaN  \n", "25 - 34                                                   NaN  \n", "35 - 40+                                                  NaN  \n", "In my 50s                                                 NaN  \n", "Over 60                                                   NaN  \n", "Anaemia     Iron deficiency anaemia is a condition in whic...  \n", "Depression  The body can react negatively to the symptoms ...  \n", "Diabetes    Hormones and stress - Diabetes can put a great...  "]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["df_transposed.head(8)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "Cannot interpret '<attribute 'dtype' of 'numpy.generic' objects>' as a data type", "output_type": "error", "traceback": ["\u001b[0;31m----------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                            Traceback (most recent call last)", "\u001b[0;32m<ipython-input-23-20d74bfd16f1>\u001b[0m in \u001b[0;36m<module>\u001b[0;34m\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;31m# df.describe()\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 2\u001b[0;31m \u001b[0mdf_transposed\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mdescribe\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m~/Library/Python/3.8/lib/python/site-packages/pandas/core/generic.py\u001b[0m in \u001b[0;36mdescribe\u001b[0;34m(self, percentiles, include, exclude)\u001b[0m\n\u001b[1;32m  10263\u001b[0m         \u001b[0;32melif\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0minclude\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;32mand\u001b[0m \u001b[0;34m(\u001b[0m\u001b[0mexclude\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10264\u001b[0m             \u001b[0;31m# when some numerics are found, keep only numerics\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m> 10265\u001b[0;31m             \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mselect_dtypes\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minclude\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mnumber\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m  10266\u001b[0m             \u001b[0;32mif\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[0;34m)\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;36m0\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10267\u001b[0m                 \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/Library/Python/3.8/lib/python/site-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36mselect_dtypes\u001b[0;34m(self, include, exclude)\u001b[0m\n\u001b[1;32m   3440\u001b[0m         \u001b[0;31m# the \"union\" of the logic of case 1 and case 2:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3441\u001b[0m         \u001b[0;31m# we get the included and excluded, and return their logical and\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 3442\u001b[0;31m         \u001b[0minclude_these\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mSeries\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;32mnot\u001b[0m \u001b[0mbool\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minclude\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mindex\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   3443\u001b[0m         \u001b[0mexclude_these\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mSeries\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;32mnot\u001b[0m \u001b[0mbool\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mexclude\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mindex\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcolumns\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   3444\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/Library/Python/3.8/lib/python/site-packages/pandas/core/series.py\u001b[0m in \u001b[0;36m__init__\u001b[0;34m(self, data, index, dtype, name, copy, fastpath)\u001b[0m\n\u001b[1;32m    312\u001b[0m                     \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mdata\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mcopy\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    313\u001b[0m             \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 314\u001b[0;31m                 \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0msanitize_array\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mindex\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdtype\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcopy\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mraise_cast_failure\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    315\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    316\u001b[0m                 \u001b[0mdata\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mSingleBlockManager\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mdata\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mindex\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mfastpath\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mTrue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/Library/Python/3.8/lib/python/site-packages/pandas/core/internals/construction.py\u001b[0m in \u001b[0;36msanitize_array\u001b[0;34m(data, index, dtype, copy, raise_cast_failure)\u001b[0m\n\u001b[1;32m    710\u001b[0m                 \u001b[0mvalue\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmaybe_cast_to_datetime\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mvalue\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdtype\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    711\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 712\u001b[0;31m             \u001b[0msubarr\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mconstruct_1d_arraylike_from_scalar\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mvalue\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mlen\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mindex\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdtype\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    713\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    714\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m~/Library/Python/3.8/lib/python/site-packages/pandas/core/dtypes/cast.py\u001b[0m in \u001b[0;36mconstruct_1d_arraylike_from_scalar\u001b[0;34m(value, length, dtype)\u001b[0m\n\u001b[1;32m   1231\u001b[0m                 \u001b[0mvalue\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mensure_str\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mvalue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1232\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1233\u001b[0;31m         \u001b[0msubarr\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mnp\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mempty\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mlength\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mdtype\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mdtype\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1234\u001b[0m         \u001b[0msubarr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfill\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mvalue\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1235\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;31mTypeError\u001b[0m: Cannot interpret '<attribute 'dtype' of 'numpy.generic' objects>' as a data type"]}], "source": ["# df.describe()\n", "df_transposed.describe()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# new_df = df.assign(profit=[40000, 20000, 30000, 60000, 200000])\n", "df_ready = df_transposed.assign(q_group=\"question_group\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>option1</th>\n", "      <th>option2</th>\n", "      <th>option3</th>\n", "      <th>option4</th>\n", "      <th>q_group</th>\n", "    </tr>\n", "    <tr>\n", "      <th>categories</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>18 - 24</th>\n", "      <td>In Your 20's - Who doesn't experiment with the...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25 - 34</th>\n", "      <td>In Your 30s - Hair can change completely every...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35 - 40+</th>\n", "      <td>In Your 40s - As the body goes through numerou...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>In my 50s</th>\n", "      <td>Your hair at this age: The average age of meno...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Over 60</th>\n", "      <td>Your hair at this age: Sebum (oil) secretion t...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Anaemia</th>\n", "      <td>Why does iron deficiency cause hair loss?It ca...</td>\n", "      <td>What does it look like?If you’re experiencing ...</td>\n", "      <td>Why is Iron Important for Hair?Iron is a miner...</td>\n", "      <td>Iron deficiency anaemia is a condition in whic...</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Depression</th>\n", "      <td>Many people do not realise that depression can...</td>\n", "      <td>The physiological states of depression such as...</td>\n", "      <td>Side effect of depression can sometimes be hai...</td>\n", "      <td>The body can react negatively to the symptoms ...</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Diabetes</th>\n", "      <td>Diabetes can cause a range of symptoms and hea...</td>\n", "      <td>Diabetes can cause hair thinning and hair loss...</td>\n", "      <td>Damage to blood vessels through diabetes can r...</td>\n", "      <td>Hormones and stress - Diabetes can put a great...</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON> Imbalance</th>\n", "      <td>Hormone levels affect both the quality and qua...</td>\n", "      <td>Common causes of hormone imbalances that cause...</td>\n", "      <td>Hormone levels affect both the quality and qua...</td>\n", "      <td>Common causes of hormone imbalances that cause...</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Polycystic Ovarian Syndrome</th>\n", "      <td>PCOS is caused by an excess of male hormones, ...</td>\n", "      <td>For women with polycystic ovary syndrome (PCOS...</td>\n", "      <td>What causes hirsutism? Hirsutism is mainly cau...</td>\n", "      <td>Women can produce high levels of androgens bec...</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Thyroid Problem</th>\n", "      <td>Hair loss, in fact, is often the first sign of...</td>\n", "      <td>The thyroid gland accumulates more toxins than...</td>\n", "      <td>Hair loss due to thyroid disease becomes appar...</td>\n", "      <td>Most cases of scalp and eyebrow hair loss caus...</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Yes</th>\n", "      <td>Extreme physical or emotional stress and high ...</td>\n", "      <td>Abnormal hair shedding and hair miniaturizatio...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>No</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Lactose/dairy</th>\n", "      <td>BE WARY OF DAIRY - Dairy products are a great ...</td>\n", "      <td>Dairy can be a big problem as it can trigger d...</td>\n", "      <td>Commercially available dairy is almost always ...</td>\n", "      <td>Dairy products contain calcium, it’s important...</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <td>Caf<PERSON>ine helps to promote hair growth as the c...</td>\n", "      <td>Caffeine has a positive effect on the hair fol...</td>\n", "      <td>When we drink products that contain caffeine t...</td>\n", "      <td>Hair products that contain caffeine are not ab...</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Nuts</th>\n", "      <td>Nuts contain L-lysine is also present in the h...</td>\n", "      <td>Someone with a nut allergy won’t necessarily b...</td>\n", "      <td>Nut can be the base ingredient of so many natu...</td>\n", "      <td>Make sure you read all your labels carefully b...</td>\n", "      <td>question_group</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                                       option1  \\\n", "categories                                                                       \n", "18 - 24                      In Your 20's - Who doesn't experiment with the...   \n", "25 - 34                      In Your 30s - Hair can change completely every...   \n", "35 - 40+                     In Your 40s - As the body goes through numerou...   \n", "In my 50s                    Your hair at this age: The average age of meno...   \n", "Over 60                      Your hair at this age: Sebum (oil) secretion t...   \n", "Anaemia                      Why does iron deficiency cause hair loss?It ca...   \n", "Depression                   Many people do not realise that depression can...   \n", "Diabetes                     Diabetes can cause a range of symptoms and hea...   \n", "Hormone Imbalance            Hormone levels affect both the quality and qua...   \n", "Polycystic Ovarian Syndrome  PCOS is caused by an excess of male hormones, ...   \n", "Thyroid Problem              Hair loss, in fact, is often the first sign of...   \n", "Yes                          Extreme physical or emotional stress and high ...   \n", "No                                                                       None    \n", "Lactose/dairy                BE WARY OF DAIRY - Dairy products are a great ...   \n", "Caffeine                     Caffeine helps to promote hair growth as the c...   \n", "Nuts                         Nuts contain L-lysine is also present in the h...   \n", "\n", "                                                                       option2  \\\n", "categories                                                                       \n", "18 - 24                                                                    NaN   \n", "25 - 34                                                                    NaN   \n", "35 - 40+                                                                   NaN   \n", "In my 50s                                                                  NaN   \n", "Over 60                                                                    NaN   \n", "Anaemia                      What does it look like?If you’re experiencing ...   \n", "Depression                   The physiological states of depression such as...   \n", "Diabetes                     Diabetes can cause hair thinning and hair loss...   \n", "Hormone Imbalance            Common causes of hormone imbalances that cause...   \n", "Polycystic Ovarian Syndrome  For women with polycystic ovary syndrome (PCOS...   \n", "Thyroid Problem              The thyroid gland accumulates more toxins than...   \n", "Yes                          Abnormal hair shedding and hair miniaturizatio...   \n", "No                                                                       None    \n", "Lactose/dairy                Dairy can be a big problem as it can trigger d...   \n", "Caffeine                     Caffeine has a positive effect on the hair fol...   \n", "Nuts                         Someone with a nut allergy won’t necessarily b...   \n", "\n", "                                                                       option3  \\\n", "categories                                                                       \n", "18 - 24                                                                    NaN   \n", "25 - 34                                                                    NaN   \n", "35 - 40+                                                                   NaN   \n", "In my 50s                                                                  NaN   \n", "Over 60                                                                    NaN   \n", "Anaemia                      Why is Iron Important for Hair?Iron is a miner...   \n", "Depression                   Side effect of depression can sometimes be hai...   \n", "Diabetes                     Damage to blood vessels through diabetes can r...   \n", "Hormone Imbalance            Hormone levels affect both the quality and qua...   \n", "Polycystic Ovarian Syndrome  What causes hirsutism? Hirsutism is mainly cau...   \n", "Thyroid Problem              Hair loss due to thyroid disease becomes appar...   \n", "Yes                                                                        NaN   \n", "No                                                                         NaN   \n", "Lactose/dairy                Commercially available dairy is almost always ...   \n", "Caffeine                     When we drink products that contain caffeine t...   \n", "Nuts                         Nut can be the base ingredient of so many natu...   \n", "\n", "                                                                       option4  \\\n", "categories                                                                       \n", "18 - 24                                                                    NaN   \n", "25 - 34                                                                    NaN   \n", "35 - 40+                                                                   NaN   \n", "In my 50s                                                                  NaN   \n", "Over 60                                                                    NaN   \n", "Anaemia                      Iron deficiency anaemia is a condition in whic...   \n", "Depression                   The body can react negatively to the symptoms ...   \n", "Diabetes                     Hormones and stress - Diabetes can put a great...   \n", "Hormone Imbalance            Common causes of hormone imbalances that cause...   \n", "Polycystic Ovarian Syndrome  Women can produce high levels of androgens bec...   \n", "Thyroid Problem              Most cases of scalp and eyebrow hair loss caus...   \n", "Yes                                                                        NaN   \n", "No                                                                         NaN   \n", "Lactose/dairy                Dairy products contain calcium, it’s important...   \n", "Caffeine                     Hair products that contain caffeine are not ab...   \n", "Nuts                         Make sure you read all your labels carefully b...   \n", "\n", "                                    q_group  \n", "categories                                   \n", "18 - 24                      question_group  \n", "25 - 34                      question_group  \n", "35 - 40+                     question_group  \n", "In my 50s                    question_group  \n", "Over 60                      question_group  \n", "Anaemia                      question_group  \n", "Depression                   question_group  \n", "Diabetes                     question_group  \n", "Hormone Imbalance            question_group  \n", "Polycystic Ovarian Syndrome  question_group  \n", "Thyroid Problem              question_group  \n", "Yes                          question_group  \n", "No                           question_group  \n", "Lactose/dairy                question_group  \n", "Caffeine                     question_group  \n", "Nuts                         question_group  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["#  Step 3: Display the transposed DataFrame\n", "df_ready.head(16)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.4"}}, "nbformat": 4, "nbformat_minor": 5}